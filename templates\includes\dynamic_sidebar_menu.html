<!-- 动态左侧菜单组件 - 从数据库读取菜单配置 -->
<style>
  /* 子菜单默认隐藏 */
  .submenu {
    display: none;
  }

  /* 子菜单展开时显示 */
  .submenu.show {
    display: block;
  }

  /* 菜单项高亮样式 */
  .sidebar-menu-item.active {
    background-color: #f3f4f6;
    color: #1f2937;
    font-weight: 500;
  }
</style>
<aside class="w-48 bg-white shadow-md flex-shrink-0">
  <div class="py-4">
    {% if menu_items %}
      {% for menu_item in menu_items %}
        {% if menu_item.is_collapsible and menu_item.children %}
          <!-- 可折叠菜单项 -->
          <div class="mt-2">
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="{{ menu_item.id }}-menu">
              <div class="w-6 h-6 flex items-center justify-center mr-2">
                <i class="{{ menu_item.icon }}"></i>
              </div>
              <span>{{ menu_item.text }}</span>
              <div class="w-4 h-4 flex items-center justify-center ml-auto">
                <i class="ri-arrow-right-s-line menu-arrow"></i>
              </div>
            </div>
            <div id="{{ menu_item.id }}-menu" class="submenu {% if menu_item.is_expanded %}show{% endif %}">
              {% for child in menu_item.children %}
                <a href="{{ child.url }}" class="no-underline">
                  <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == child.id %}active{% endif %}">
                    <span>{{ child.text }}</span>
                  </div>
                </a>
              {% endfor %}
            </div>
          </div>
        {% else %}
          <!-- 普通菜单项 -->
          <a href="{{ menu_item.url }}" class="no-underline">
            <div class="px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer {% if current_page == menu_item.id %}active{% endif %}">
              <div class="w-6 h-6 flex items-center justify-center mr-2">
                <i class="{{ menu_item.icon }}"></i>
              </div>
              <span>{{ menu_item.text }}</span>
            </div>
          </a>
        {% endif %}
      {% endfor %}
    {% else %}
      <!-- 如果没有菜单配置，显示默认菜单 -->
      <!-- 首页 -->
      <a href="{{ url_for('index') }}" class="no-underline">
        <div class="px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer {% if current_page == 'dashboard' %}active{% endif %}">
          <div class="w-6 h-6 flex items-center justify-center mr-2">
            <i class="ri-home-line"></i>
          </div>
          <span>首页</span>
        </div>
      </a>

      <!-- 用户管理 -->
      <div class="mt-2">
        <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="user-menu">
          <div class="w-6 h-6 flex items-center justify-center mr-2">
            <i class="ri-user-line"></i>
          </div>
          <span>用户管理</span>
          <div class="w-4 h-4 flex items-center justify-center ml-auto">
            <i class="ri-arrow-right-s-line menu-arrow"></i>
          </div>
        </div>
        <div id="user-menu" class="submenu">
          <a href="{{ url_for('user_list') }}" class="no-underline">
            <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'user_list' %}active{% endif %}">
              <span>用户列表</span>
            </div>
          </a>
          <a href="{{ url_for('banned_users') }}" class="no-underline">
            <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'banned_users' %}active{% endif %}">
              <span>封禁用户列表</span>
            </div>
          </a>
        </div>
      </div>
    {% endif %}
  </div>
</aside>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // 菜单折叠/展开功能
  const menuHeaders = document.querySelectorAll('.menu-header');
  
  menuHeaders.forEach(header => {
    header.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const submenu = document.getElementById(targetId);
      const arrow = this.querySelector('.menu-arrow');
      
      if (submenu) {
        submenu.classList.toggle('show');
        
        // 旋转箭头
        if (submenu.classList.contains('show')) {
          arrow.style.transform = 'rotate(90deg)';
        } else {
          arrow.style.transform = 'rotate(0deg)';
        }
      }
    });
  });

  // 初始化箭头状态
  const expandedMenus = document.querySelectorAll('.submenu.show');
  expandedMenus.forEach(menu => {
    const menuId = menu.id;
    const header = document.querySelector(`[data-target="${menuId}"]`);
    if (header) {
      const arrow = header.querySelector('.menu-arrow');
      if (arrow) {
        arrow.style.transform = 'rotate(90deg)';
      }
    }
  });
});
</script>
