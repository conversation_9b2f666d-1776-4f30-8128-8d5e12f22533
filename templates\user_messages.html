<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台 - 用户消息列表</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      .submenu-item {
        display: none;
      }
      .submenu-item.show {
        display: flex;
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }
      .message-content {
        max-width: 400px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .message-content-full {
        max-width: 500px;
        word-wrap: break-word;
        white-space: pre-wrap;
      }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ contents.header_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ contents.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ contents.username|default('admin') }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}

      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>消息管理</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>用户消息列表</span>
          </div>
        </div>

        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 用户信息栏 -->
          <div class="flex items-center mb-6 p-4 bg-gray-50 rounded-lg">
            <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
              <i class="ri-user-line ri-xl text-gray-600"></i>
            </div>
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900" id="user-name">用户名</h3>
              <p class="text-sm text-gray-500">
                <span>用户ID: </span><span id="user-id">-</span>
                <span class="ml-4">注册时间: </span><span id="user-register-time">-</span>
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm" id="user-status">正常</span>
              <button class="px-4 py-2 bg-blue-500 text-white rounded-button hover:bg-blue-600 transition-colors">
                查看详情
              </button>
            </div>
          </div>

          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                id="search-input"
                placeholder="搜索消息内容"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>

            <!-- 筛选器 -->
            <div class="flex items-center ml-4 space-x-2">
              <select id="message-type-filter" class="px-3 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm">
                <option value="">全部类型</option>
                <option value="text">文本消息</option>
                <option value="image">图片消息</option>
                <option value="file">文件消息</option>
                <option value="voice">语音消息</option>
                <option value="video">视频消息</option>
              </select>

              <select id="date-filter" class="px-3 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm">
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="yesterday">昨天</option>
                <option value="week">最近一周</option>
                <option value="month">最近一月</option>
              </select>

              <button id="export-btn" class="px-4 py-2 bg-green-500 text-white rounded-button hover:bg-green-600 transition-colors">
                <i class="ri-download-line mr-1"></i>导出
              </button>
            </div>

            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded"
                id="refresh-btn"
              >
                <i class="ri-refresh-line ri-lg"></i>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="grid grid-cols-4 gap-4 mb-6">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-2xl font-bold text-blue-600" id="total-messages">0</div>
              <div class="text-sm text-gray-600">总消息数</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-2xl font-bold text-green-600" id="today-messages">0</div>
              <div class="text-sm text-gray-600">今日消息</div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <div class="text-2xl font-bold text-yellow-600" id="sensitive-messages">0</div>
              <div class="text-sm text-gray-600">敏感消息</div>
            </div>
            <div class="bg-red-50 p-4 rounded-lg">
              <div class="text-2xl font-bold text-red-600" id="blocked-messages">0</div>
              <div class="text-sm text-gray-600">已屏蔽</div>
            </div>
          </div>

          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium text-left">
                    <input type="checkbox" id="select-all" class="rounded">
                  </th>
                  <th class="font-medium">消息ID</th>
                  <th class="font-medium">消息类型</th>
                  <th class="font-medium">消息内容</th>
                  <th class="font-medium">接收者</th>
                  <th class="font-medium">发送时间</th>
                  <th class="font-medium">状态</th>
                  <th class="font-medium">操作</th>
                </tr>
              </thead>
              <tbody id="messages-table-body">
                <!-- 消息数据将通过JavaScript动态加载 -->
                <tr>
                  <td colspan="8" class="text-center py-8 text-gray-500">
                    <i class="ri-loader-4-line ri-xl animate-spin"></i>
                    <div class="mt-2">正在加载消息数据...</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 批量操作栏 -->
          <div id="batch-actions" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <span class="text-sm text-gray-600 mr-4">已选择 <span id="selected-count">0</span> 条消息</span>
                <div class="flex space-x-2">
                  <button id="batch-delete-btn" class="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors">
                    批量删除
                  </button>
                  <button id="batch-export-btn" class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors">
                    批量导出
                  </button>
                </div>
              </div>
              <button id="cancel-selection-btn" class="text-sm text-gray-500 hover:text-gray-700">
                取消选择
              </button>
            </div>
          </div>

          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600">
              共 <span id="total-count">0</span> 条，每页显示
              <select id="page-size" class="mx-1 px-2 py-1 border border-gray-200 rounded">
                <option value="10">10</option>
                <option value="20" selected>20</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select> 条
            </div>
            <div class="flex items-center" id="pagination-container">
              <div
                class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer hover:bg-gray-100 rounded"
                id="prev-page"
              >
                <i class="ri-arrow-left-s-line"></i>
              </div>
              <div class="pagination-numbers flex items-center mx-2">
                <!-- 页码将通过JavaScript动态生成 -->
              </div>
              <div
                class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer hover:bg-gray-100 rounded"
                id="next-page"
              >
                <i class="ri-arrow-right-s-line"></i>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 消息详情模态框 -->
    <div id="message-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
          <div class="flex items-center justify-between p-4 border-b">
            <h3 class="text-lg font-medium">消息详情</h3>
            <button id="close-modal" class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded">
              <i class="ri-close-line"></i>
            </button>
          </div>
          <div class="p-4 overflow-y-auto" id="modal-content">
            <!-- 消息详情内容 -->
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript功能 -->
    <script id="sidebarToggle">
      document.addEventListener("DOMContentLoaded", function () {
        // 侧边栏菜单切换功能
        const menuHeaders = document.querySelectorAll(".menu-header");
        menuHeaders.forEach((header) => {
          header.addEventListener("click", function () {
            const target = this.getAttribute("data-target");
            const submenu = document.getElementById(target);
            const arrow = this.querySelector(".menu-arrow");

            if (submenu) {
              const submenuItems = submenu.querySelectorAll(".submenu-item");
              const isVisible = submenuItems[0]?.classList.contains("show");

              submenuItems.forEach((item) => {
                if (isVisible) {
                  item.classList.remove("show");
                } else {
                  item.classList.add("show");
                }
              });

              // 旋转箭头
              if (arrow) {
                if (isVisible) {
                  arrow.style.transform = "rotate(0deg)";
                } else {
                  arrow.style.transform = "rotate(180deg)";
                }
              }
            }
          });
        });

        // 侧边栏菜单项点击高亮
        const sidebarMenuItems = document.querySelectorAll(".sidebar-menu-item");
        sidebarMenuItems.forEach((item) => {
          item.addEventListener("click", function () {
            sidebarMenuItems.forEach((i) => i.classList.remove("active"));
            this.classList.add("active");
          });
        });
      });
    </script>

    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script id="userMessagesHandler">
      // 设置当前页面标识
      window.currentPage = 'user_messages';

      document.addEventListener("DOMContentLoaded", function () {
        // 菜单将由sidebar_menu.js自动初始化

        // 全局变量
        let currentPage = 1;
        let pageSize = 20;
        let totalCount = 0;
        let currentUserId = null;
        let searchQuery = '';
        let messageTypeFilter = '';
        let dateFilter = '';

        // 从URL获取用户ID
        const urlPath = window.location.pathname;
        const userIdMatch = urlPath.match(/\/user_messages\/(.+)/);
        if (userIdMatch) {
          currentUserId = userIdMatch[1];
        }

        // DOM元素
        const searchInput = document.getElementById('search-input');
        const messageTypeFilterSelect = document.getElementById('message-type-filter');
        const dateFilterSelect = document.getElementById('date-filter');
        const pageSizeSelect = document.getElementById('page-size');
        const refreshBtn = document.getElementById('refresh-btn');
        const exportBtn = document.getElementById('export-btn');
        const selectAllCheckbox = document.getElementById('select-all');
        const batchActions = document.getElementById('batch-actions');
        const selectedCountSpan = document.getElementById('selected-count');
        const messageModal = document.getElementById('message-modal');
        const closeModalBtn = document.getElementById('close-modal');

        // 初始化
        init();

        function init() {
          loadUserInfo();
          loadMessages();
          bindEvents();
        }

        // 绑定事件
        function bindEvents() {
          // 搜索功能
          let searchTimeout;
          searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
              searchQuery = this.value.trim();
              currentPage = 1;
              loadMessages();
            }, 500);
          });

          // 筛选器
          messageTypeFilterSelect.addEventListener('change', function() {
            messageTypeFilter = this.value;
            currentPage = 1;
            loadMessages();
          });

          dateFilterSelect.addEventListener('change', function() {
            dateFilter = this.value;
            currentPage = 1;
            loadMessages();
          });

          // 每页显示数量
          pageSizeSelect.addEventListener('change', function() {
            pageSize = parseInt(this.value);
            currentPage = 1;
            loadMessages();
          });

          // 刷新按钮
          refreshBtn.addEventListener('click', function() {
            this.querySelector('i').classList.add('animate-spin');
            loadMessages().finally(() => {
              this.querySelector('i').classList.remove('animate-spin');
            });
          });

          // 导出按钮
          exportBtn.addEventListener('click', exportMessages);

          // 全选功能
          selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBatchActions();
          });

          // 批量操作
          document.getElementById('batch-delete-btn').addEventListener('click', batchDeleteMessages);
          document.getElementById('batch-export-btn').addEventListener('click', batchExportMessages);
          document.getElementById('cancel-selection-btn').addEventListener('click', cancelSelection);

          // 模态框关闭
          closeModalBtn.addEventListener('click', closeModal);
          messageModal.addEventListener('click', function(e) {
            if (e.target === this) closeModal();
          });

          // 分页事件
          document.getElementById('prev-page').addEventListener('click', () => {
            if (currentPage > 1) {
              currentPage--;
              loadMessages();
            }
          });

          document.getElementById('next-page').addEventListener('click', () => {
            const totalPages = Math.ceil(totalCount / pageSize);
            if (currentPage < totalPages) {
              currentPage++;
              loadMessages();
            }
          });
        }

        // 加载用户信息
        function loadUserInfo() {
          if (!currentUserId) return;

          // 模拟用户数据（实际应该从API获取）
          const userData = {
            id: currentUserId,
            name: `用户${currentUserId}`,
            registerTime: '2023-01-15 10:30:00',
            status: '正常'
          };

          document.getElementById('user-id').textContent = userData.id;
          document.getElementById('user-name').textContent = userData.name;
          document.getElementById('user-register-time').textContent = userData.registerTime;
          document.getElementById('user-status').textContent = userData.status;
        }

        // 加载消息数据
        function loadMessages() {
          return new Promise((resolve, reject) => {
            // 显示加载状态
            const tableBody = document.getElementById('messages-table-body');
            tableBody.innerHTML = `
              <tr>
                <td colspan="8" class="text-center py-8 text-gray-500">
                  <i class="ri-loader-4-line ri-xl animate-spin"></i>
                  <div class="mt-2">正在加载消息数据...</div>
                </td>
              </tr>
            `;

            // 模拟API调用（实际应该调用真实API）
            setTimeout(() => {
              try {
                const mockData = generateMockMessages();
                renderMessages(mockData.messages);
                updateStatistics(mockData.statistics);
                updatePagination(mockData.total);
                resolve(mockData);
              } catch (error) {
                console.error('加载消息失败:', error);
                tableBody.innerHTML = `
                  <tr>
                    <td colspan="8" class="text-center py-8 text-red-500">
                      <i class="ri-error-warning-line ri-xl"></i>
                      <div class="mt-2">加载失败，请重试</div>
                    </td>
                  </tr>
                `;
                reject(error);
              }
            }, 1000);
          });
        }

        // 生成模拟消息数据
        function generateMockMessages() {
          const messageTypes = ['text', 'image', 'file', 'voice', 'video'];
          const statuses = ['正常', '已屏蔽', '敏感'];
          const receivers = ['群聊001', '用户123', '群聊002', '用户456'];

          const messages = [];
          const total = 156; // 模拟总数
          const start = (currentPage - 1) * pageSize;
          const end = Math.min(start + pageSize, total);

          for (let i = start; i < end; i++) {
            const messageType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const receiver = receivers[Math.floor(Math.random() * receivers.length)];

            let content = '';
            switch (messageType) {
              case 'text':
                content = `这是一条文本消息内容 ${i + 1}`;
                break;
              case 'image':
                content = '[图片消息]';
                break;
              case 'file':
                content = '[文件消息] document.pdf';
                break;
              case 'voice':
                content = '[语音消息] 15秒';
                break;
              case 'video':
                content = '[视频消息] 30秒';
                break;
            }

            messages.push({
              id: `msg_${1000 + i}`,
              type: messageType,
              content: content,
              receiver: receiver,
              sendTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleString(),
              status: status
            });
          }

          const statistics = {
            total: total,
            today: 23,
            sensitive: 5,
            blocked: 2
          };

          return { messages, statistics, total };
        }

        // 渲染消息表格
        function renderMessages(messages) {
          const tableBody = document.getElementById('messages-table-body');

          if (messages.length === 0) {
            tableBody.innerHTML = `
              <tr>
                <td colspan="8" class="text-center py-8 text-gray-500">
                  <i class="ri-inbox-line ri-xl"></i>
                  <div class="mt-2">暂无消息数据</div>
                </td>
              </tr>
            `;
            return;
          }

          const rows = messages.map(message => {
            const statusClass = message.status === '正常' ? 'text-green-600' :
                               message.status === '敏感' ? 'text-yellow-600' : 'text-red-600';
            const statusBg = message.status === '正常' ? 'bg-green-100' :
                            message.status === '敏感' ? 'bg-yellow-100' : 'bg-red-100';

            return `
              <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td>
                  <input type="checkbox" class="rounded message-checkbox" data-id="${message.id}">
                </td>
                <td class="font-mono text-sm">${message.id}</td>
                <td>
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                    ${getMessageTypeText(message.type)}
                  </span>
                </td>
                <td>
                  <div class="message-content" title="${message.content}">
                    ${message.content}
                  </div>
                </td>
                <td>${message.receiver}</td>
                <td class="text-sm text-gray-600">${message.sendTime}</td>
                <td>
                  <span class="px-2 py-1 ${statusBg} ${statusClass} rounded text-xs">
                    ${message.status}
                  </span>
                </td>
                <td>
                  <div class="flex space-x-1">
                    <button class="px-2 py-1 text-blue-600 hover:bg-blue-50 rounded text-sm view-detail-btn"
                            data-id="${message.id}">
                      详情
                    </button>
                    <button class="px-2 py-1 text-red-600 hover:bg-red-50 rounded text-sm delete-btn"
                            data-id="${message.id}">
                      删除
                    </button>
                  </div>
                </td>
              </tr>
            `;
          }).join('');

          tableBody.innerHTML = rows;

          // 绑定行内事件
          bindRowEvents();
        }

        // 获取消息类型文本
        function getMessageTypeText(type) {
          const typeMap = {
            'text': '文本',
            'image': '图片',
            'file': '文件',
            'voice': '语音',
            'video': '视频'
          };
          return typeMap[type] || type;
        }

        // 绑定表格行事件
        function bindRowEvents() {
          // 消息复选框事件
          const checkboxes = document.querySelectorAll('.message-checkbox');
          checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchActions);
          });

          // 查看详情按钮
          const viewDetailBtns = document.querySelectorAll('.view-detail-btn');
          viewDetailBtns.forEach(btn => {
            btn.addEventListener('click', function() {
              const messageId = this.getAttribute('data-id');
              showMessageDetail(messageId);
            });
          });

          // 删除按钮
          const deleteBtns = document.querySelectorAll('.delete-btn');
          deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
              const messageId = this.getAttribute('data-id');
              deleteMessage(messageId);
            });
          });
        }

        // 更新统计信息
        function updateStatistics(stats) {
          document.getElementById('total-messages').textContent = stats.total;
          document.getElementById('today-messages').textContent = stats.today;
          document.getElementById('sensitive-messages').textContent = stats.sensitive;
          document.getElementById('blocked-messages').textContent = stats.blocked;
        }

        // 更新分页
        function updatePagination(total) {
          totalCount = total;
          document.getElementById('total-count').textContent = total;

          const totalPages = Math.ceil(total / pageSize);
          const paginationNumbers = document.querySelector('.pagination-numbers');

          // 生成页码
          let paginationHTML = '';
          const maxVisiblePages = 7;
          let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
          let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

          if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
          }

          for (let i = startPage; i <= endPage; i++) {
            const isActive = i === currentPage ? 'active' : '';
            paginationHTML += `
              <div class="pagination-item ${isActive} mx-1 cursor-pointer" data-page="${i}">
                ${i}
              </div>
            `;
          }

          if (endPage < totalPages) {
            paginationHTML += `
              <div class="pagination-item mx-1">...</div>
              <div class="pagination-item mx-1 cursor-pointer" data-page="${totalPages}">
                ${totalPages}
              </div>
            `;
          }

          paginationNumbers.innerHTML = paginationHTML;

          // 绑定页码点击事件
          const pageItems = paginationNumbers.querySelectorAll('[data-page]');
          pageItems.forEach(item => {
            item.addEventListener('click', function() {
              const page = parseInt(this.getAttribute('data-page'));
              if (page !== currentPage) {
                currentPage = page;
                loadMessages();
              }
            });
          });

          // 更新前后页按钮状态
          const prevBtn = document.getElementById('prev-page');
          const nextBtn = document.getElementById('next-page');

          prevBtn.style.opacity = currentPage === 1 ? '0.5' : '1';
          prevBtn.style.cursor = currentPage === 1 ? 'not-allowed' : 'pointer';

          nextBtn.style.opacity = currentPage === totalPages ? '0.5' : '1';
          nextBtn.style.cursor = currentPage === totalPages ? 'not-allowed' : 'pointer';
        }

        // 更新批量操作状态
        function updateBatchActions() {
          const checkboxes = document.querySelectorAll('.message-checkbox');
          const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');

          selectedCountSpan.textContent = checkedBoxes.length;

          if (checkedBoxes.length > 0) {
            batchActions.classList.remove('hidden');
          } else {
            batchActions.classList.add('hidden');
          }

          // 更新全选状态
          selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
          selectAllCheckbox.checked = checkedBoxes.length === checkboxes.length && checkboxes.length > 0;
        }

        // 显示消息详情
        function showMessageDetail(messageId) {
          // 模拟获取消息详情
          const messageDetail = {
            id: messageId,
            type: 'text',
            content: '这是一条详细的消息内容，包含了完整的信息...',
            sender: currentUserId,
            receiver: '群聊001',
            sendTime: '2024-01-15 14:30:25',
            status: '正常',
            metadata: {
              ip: '*************',
              device: 'iPhone 15',
              location: '北京市朝阳区'
            }
          };

          const modalContent = document.getElementById('modal-content');
          modalContent.innerHTML = `
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">消息ID</label>
                  <div class="text-sm text-gray-900 font-mono">${messageDetail.id}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">消息类型</label>
                  <div class="text-sm text-gray-900">${getMessageTypeText(messageDetail.type)}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">发送者</label>
                  <div class="text-sm text-gray-900">${messageDetail.sender}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">接收者</label>
                  <div class="text-sm text-gray-900">${messageDetail.receiver}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">发送时间</label>
                  <div class="text-sm text-gray-900">${messageDetail.sendTime}</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                  <div class="text-sm text-gray-900">${messageDetail.status}</div>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">消息内容</label>
                <div class="p-3 bg-gray-50 rounded-lg text-sm text-gray-900 message-content-full">
                  ${messageDetail.content}
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">元数据</label>
                <div class="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500">IP地址:</span>
                    <span class="text-gray-900">${messageDetail.metadata.ip}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">设备:</span>
                    <span class="text-gray-900">${messageDetail.metadata.device}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">位置:</span>
                    <span class="text-gray-900">${messageDetail.metadata.location}</span>
                  </div>
                </div>
              </div>

              <div class="flex justify-end space-x-2 pt-4 border-t">
                <button class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                        onclick="deleteMessage('${messageDetail.id}')">
                  删除消息
                </button>
                <button class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                        onclick="closeModal()">
                  关闭
                </button>
              </div>
            </div>
          `;

          messageModal.classList.remove('hidden');
        }

        // 关闭模态框
        function closeModal() {
          messageModal.classList.add('hidden');
        }

        // 删除单个消息
        function deleteMessage(messageId) {
          if (confirm('确定要删除这条消息吗？')) {
            // 模拟删除操作
            console.log('删除消息:', messageId);
            // 实际应该调用API删除
            // 删除成功后重新加载数据
            loadMessages();
            closeModal();
          }
        }

        // 批量删除消息
        function batchDeleteMessages() {
          const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
          if (checkedBoxes.length === 0) {
            alert('请选择要删除的消息');
            return;
          }

          if (confirm(`确定要删除选中的 ${checkedBoxes.length} 条消息吗？`)) {
            const messageIds = Array.from(checkedBoxes).map(cb => cb.getAttribute('data-id'));
            console.log('批量删除消息:', messageIds);
            // 实际应该调用API批量删除
            // 删除成功后重新加载数据
            loadMessages();
            cancelSelection();
          }
        }

        // 导出消息
        function exportMessages() {
          console.log('导出所有消息');
          // 实际应该调用API导出
          alert('导出功能开发中...');
        }

        // 批量导出消息
        function batchExportMessages() {
          const checkedBoxes = document.querySelectorAll('.message-checkbox:checked');
          if (checkedBoxes.length === 0) {
            alert('请选择要导出的消息');
            return;
          }

          const messageIds = Array.from(checkedBoxes).map(cb => cb.getAttribute('data-id'));
          console.log('批量导出消息:', messageIds);
          // 实际应该调用API批量导出
          alert('批量导出功能开发中...');
        }

        // 取消选择
        function cancelSelection() {
          const checkboxes = document.querySelectorAll('.message-checkbox');
          checkboxes.forEach(cb => cb.checked = false);
          selectAllCheckbox.checked = false;
          selectAllCheckbox.indeterminate = false;
          updateBatchActions();
        }

        // 全局函数，供模态框内按钮调用
        window.deleteMessage = deleteMessage;
        window.closeModal = closeModal;
      });
    </script>
  </body>
</html>
