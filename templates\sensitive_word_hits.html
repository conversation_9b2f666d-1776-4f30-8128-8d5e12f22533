<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台 - 敏感词命中</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      .submenu-item {
        display: none;
      }
      .submenu-item.show {
        display: flex;
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .action-filter { color: #10b981; }
      .action-warning { color: #f59e0b; }
      .action-ban { color: #ef4444; }
      .content-preview {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ contents.header_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ contents.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ contents.username|default('admin') }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}
      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>敏感词管理</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>敏感词命中</span>
          </div>
        </div>
        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                id="searchInput"
                placeholder="搜索用户名、敏感词或内容"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <select id="actionFilter" class="ml-4 px-3 py-2 border border-gray-200 rounded-button text-sm">
              <option value="">全部处理方式</option>
              <option value="过滤">过滤</option>
              <option value="警告">警告</option>
              <option value="封禁">封禁</option>
            </select>
            <select id="statusFilter" class="ml-2 px-3 py-2 border border-gray-200 rounded-button text-sm">
              <option value="">全部状态</option>
              <option value="false">未处理</option>
              <option value="true">已处理</option>
            </select>
            <button
              id="batchProcessBtn"
              class="bg-blue-500 text-white px-4 py-2 rounded-button flex items-center ml-4 hover:bg-blue-600 transition-colors"
            >
              <i class="ri-check-line mr-2"></i>
              <span>批量处理</span>
            </button>
            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer"
              >
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium w-10">
                    <input type="checkbox" id="selectAll" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                  </th>
                  <th class="font-medium">序号</th>
                  <th class="font-medium">用户名</th>
                  <th class="font-medium">命中敏感词</th>
                  <th class="font-medium">原始内容</th>
                  <th class="font-medium">处理方式</th>
                  <th class="font-medium">来源类型</th>
                  <th class="font-medium">状态</th>
                  <th class="font-medium">命中时间</th>
                  <th class="font-medium">操作</th>
                </tr>
              </thead>
              <tbody id="hitsTableBody">
                <!-- 表格数据行将通过JavaScript动态生成 -->
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600" id="totalRecords">共 0 条</div>
            <div class="flex items-center" id="pagination">
              <!-- 分页将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 查看详情模态框 -->
    <div id="detailModal" class="modal">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-2xl mx-4">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">敏感词命中详情</h3>
          <button id="closeDetailModal" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
              <div id="detailUsername" class="text-sm text-gray-900"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">用户ID</label>
              <div id="detailUserId" class="text-sm text-gray-900"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">命中敏感词</label>
              <div id="detailHitWord" class="text-sm text-red-600 font-medium"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">处理方式</label>
              <div id="detailAction" class="text-sm"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">来源类型</label>
              <div id="detailSourceType" class="text-sm text-gray-900"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">IP地址</label>
              <div id="detailIpAddress" class="text-sm text-gray-900"></div>
            </div>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">原始内容</label>
            <div id="detailOriginalContent" class="text-sm text-gray-900 bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto"></div>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">过滤后内容</label>
            <div id="detailFilteredContent" class="text-sm text-gray-900 bg-gray-50 p-3 rounded border max-h-32 overflow-y-auto"></div>
          </div>
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-1">命中上下文</label>
            <div id="detailHitContext" class="text-sm text-gray-900 bg-yellow-50 p-3 rounded border"></div>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">命中时间</label>
              <div id="detailCreatedAt" class="text-sm text-gray-900"></div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">处理时间</label>
              <div id="detailProcessedAt" class="text-sm text-gray-900"></div>
            </div>
          </div>
        </div>
        <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            id="markProcessedBtn"
            class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            标记为已处理
          </button>
          <button
            id="closeDetailBtn"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script>
      // 设置当前页面标识
      window.currentPage = 'sensitive_word_hits';

      // 全局变量
      let currentPage = 1;
      let totalPages = 1;
      let hitsData = [];
      let filteredData = [];
      let currentDetailId = null;

      // DOM元素
      const detailModal = document.getElementById('detailModal');
      const closeDetailModal = document.getElementById('closeDetailModal');
      const closeDetailBtn = document.getElementById('closeDetailBtn');
      const markProcessedBtn = document.getElementById('markProcessedBtn');
      const searchInput = document.getElementById('searchInput');
      const actionFilter = document.getElementById('actionFilter');
      const statusFilter = document.getElementById('statusFilter');
      const selectAllCheckbox = document.getElementById('selectAll');
      const batchProcessBtn = document.getElementById('batchProcessBtn');

      // 初始化页面
      document.addEventListener('DOMContentLoaded', function() {
        loadSensitiveWordHits();
        initEventListeners();
        // 菜单将由sidebar_menu.js自动初始化
      });

      // 初始化事件监听器
      function initEventListeners() {
        // 模态框事件
        closeDetailModal.addEventListener('click', () => closeDetailModalHandler());
        closeDetailBtn.addEventListener('click', () => closeDetailModalHandler());
        markProcessedBtn.addEventListener('click', () => markAsProcessed());

        // 点击模态框外部关闭
        detailModal.addEventListener('click', (e) => {
          if (e.target === detailModal) closeDetailModalHandler();
        });

        // 搜索和过滤功能
        searchInput.addEventListener('input', handleFilter);
        actionFilter.addEventListener('change', handleFilter);
        statusFilter.addEventListener('change', handleFilter);

        // 全选功能
        selectAllCheckbox.addEventListener('change', handleSelectAll);

        // 批量处理
        batchProcessBtn.addEventListener('click', handleBatchProcess);

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && detailModal.classList.contains('show')) {
            closeDetailModalHandler();
          }
        });
      }

      // 菜单切换功能已由统一组件处理

      // 加载敏感词命中数据
      async function loadSensitiveWordHits() {
        try {
          const response = await fetch('/api/sensitive_word_hits');
          if (response.ok) {
            hitsData = await response.json();
            filteredData = [...hitsData];
            renderTable();
            updatePagination();
          } else {
            console.error('Failed to load sensitive word hits');
            // 如果API失败，使用模拟数据
            loadMockData();
          }
        } catch (error) {
          console.error('Error loading sensitive word hits:', error);
          // 如果API失败，使用模拟数据
          loadMockData();
        }
      }

      // 加载模拟数据
      function loadMockData() {
        hitsData = [
          {
            id: 1,
            user_id: 'user001',
            username: 'testUser1',
            hit_word: '垃圾',
            original_content: '这是一条垃圾信息，请大家不要相信。',
            filtered_content: '这是一条***信息，请大家不要相信。',
            hit_context: '...一条垃圾信息...',
            action_taken: '过滤',
            source_type: 'message',
            ip_address: '*************',
            is_processed: false,
            created_at: '2023-07-25 16:04',
            processed_at: null
          },
          {
            id: 2,
            user_id: 'user002',
            username: 'spammer123',
            hit_word: '色情',
            original_content: '点击链接查看色情内容，绝对精彩！',
            filtered_content: '点击链接查看***内容，绝对精彩！',
            hit_context: '...查看色情内容...',
            action_taken: '封禁',
            source_type: 'comment',
            ip_address: '*********',
            is_processed: true,
            created_at: '2023-07-25 16:02',
            processed_at: '2023-07-25 16:05'
          },
          {
            id: 3,
            user_id: 'user003',
            username: 'badActor',
            hit_word: '暴力',
            original_content: '我要用暴力解决这个问题！',
            filtered_content: '我要用***解决这个问题！',
            hit_context: '...用暴力解决...',
            action_taken: '警告',
            source_type: 'post',
            ip_address: '***********',
            is_processed: false,
            created_at: '2023-07-25 15:30',
            processed_at: null
          },
          {
            id: 4,
            user_id: 'user004',
            username: 'advertiser',
            hit_word: '广告',
            original_content: '最新广告推广，点击了解更多详情！',
            filtered_content: '最新***推广，点击了解更多详情！',
            hit_context: '...最新广告推广...',
            action_taken: '过滤',
            source_type: 'message',
            ip_address: '************',
            is_processed: true,
            created_at: '2023-07-25 14:45',
            processed_at: '2023-07-25 15:00'
          },
          {
            id: 5,
            user_id: 'user005',
            username: 'scammer',
            hit_word: '诈骗',
            original_content: '这是一个诈骗网站，大家小心！',
            filtered_content: '这是一个***网站，大家小心！',
            hit_context: '...一个诈骗网站...',
            action_taken: '封禁',
            source_type: 'comment',
            ip_address: '************',
            is_processed: false,
            created_at: '2023-07-25 13:20',
            processed_at: null
          }
        ];
        filteredData = [...hitsData];
        renderTable();
        updatePagination();
      }

      // 渲染表格
      function renderTable() {
        const tbody = document.getElementById('hitsTableBody');
        const startIndex = (currentPage - 1) * 10;
        const endIndex = startIndex + 10;
        const pageData = filteredData.slice(startIndex, endIndex);

        tbody.innerHTML = pageData.map((hit, index) => `
          <tr class="border-b border-gray-200">
            <td>
              <input type="checkbox" class="row-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" data-id="${hit.id}">
            </td>
            <td>${startIndex + index + 1}</td>
            <td class="font-medium">${hit.username}</td>
            <td class="text-red-600 font-medium">${hit.hit_word}</td>
            <td>
              <div class="content-preview" title="${hit.original_content}">
                ${hit.original_content}
              </div>
            </td>
            <td>
              <span class="action-${hit.action_taken === '过滤' ? 'filter' : hit.action_taken === '警告' ? 'warning' : 'ban'}">
                ${hit.action_taken}
              </span>
            </td>
            <td>${hit.source_type}</td>
            <td>
              <span class="px-2 py-1 text-xs rounded-full ${hit.is_processed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                ${hit.is_processed ? '已处理' : '未处理'}
              </span>
            </td>
            <td>${hit.created_at}</td>
            <td>
              <button onclick="viewDetail(${hit.id})" class="text-blue-500 hover:text-blue-700 mr-2">
                查看
              </button>
              ${!hit.is_processed ? `<button onclick="markProcessed(${hit.id})" class="text-green-500 hover:text-green-700">处理</button>` : ''}
            </td>
          </tr>
        `).join('');

        // 更新总记录数
        document.getElementById('totalRecords').textContent = `共 ${filteredData.length} 条`;

        // 重新绑定复选框事件
        bindCheckboxEvents();
      }

      // 绑定复选框事件
      function bindCheckboxEvents() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.addEventListener('change', updateSelectAllState);
        });
      }

      // 更新分页
      function updatePagination() {
        totalPages = Math.ceil(filteredData.length / 10);
        const pagination = document.getElementById('pagination');

        if (totalPages <= 1) {
          pagination.innerHTML = '';
          return;
        }

        let paginationHTML = `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage > 1 ? 'changePage(' + (currentPage - 1) + ')' : ''}">
            <i class="ri-arrow-left-s-line"></i>
          </div>
        `;

        for (let i = 1; i <= totalPages; i++) {
          if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
              <div class="pagination-item mx-1 ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                ${i}
              </div>
            `;
          } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
        }

        paginationHTML += `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage < totalPages ? 'changePage(' + (currentPage + 1) + ')' : ''}">
            <i class="ri-arrow-right-s-line"></i>
          </div>
        `;

        pagination.innerHTML = paginationHTML;
      }

      // 切换页面
      function changePage(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
          currentPage = page;
          renderTable();
          updatePagination();
        }
      }

      // 搜索和过滤处理
      function handleFilter() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const actionValue = actionFilter.value;
        const statusValue = statusFilter.value;

        filteredData = hitsData.filter(hit => {
          const matchesSearch = searchTerm === '' ||
            hit.username.toLowerCase().includes(searchTerm) ||
            hit.hit_word.toLowerCase().includes(searchTerm) ||
            hit.original_content.toLowerCase().includes(searchTerm);

          const matchesAction = actionValue === '' || hit.action_taken === actionValue;
          const matchesStatus = statusValue === '' || hit.is_processed.toString() === statusValue;

          return matchesSearch && matchesAction && matchesStatus;
        });

        currentPage = 1;
        renderTable();
        updatePagination();
      }

      // 查看详情
      function viewDetail(id) {
        const hit = hitsData.find(h => h.id === id);
        if (!hit) return;

        currentDetailId = id;

        // 填充详情信息
        document.getElementById('detailUsername').textContent = hit.username;
        document.getElementById('detailUserId').textContent = hit.user_id;
        document.getElementById('detailHitWord').textContent = hit.hit_word;
        document.getElementById('detailAction').textContent = hit.action_taken;
        document.getElementById('detailAction').className = `text-sm action-${hit.action_taken === '过滤' ? 'filter' : hit.action_taken === '警告' ? 'warning' : 'ban'}`;
        document.getElementById('detailSourceType').textContent = hit.source_type;
        document.getElementById('detailIpAddress').textContent = hit.ip_address || '未知';
        document.getElementById('detailOriginalContent').textContent = hit.original_content;
        document.getElementById('detailFilteredContent').textContent = hit.filtered_content || '无';
        document.getElementById('detailHitContext').textContent = hit.hit_context || '无';
        document.getElementById('detailCreatedAt').textContent = hit.created_at;
        document.getElementById('detailProcessedAt').textContent = hit.processed_at || '未处理';

        // 更新按钮状态
        const markBtn = document.getElementById('markProcessedBtn');
        if (hit.is_processed) {
          markBtn.style.display = 'none';
        } else {
          markBtn.style.display = 'inline-block';
        }

        detailModal.classList.add('show');
      }

      // 关闭详情模态框
      function closeDetailModalHandler() {
        detailModal.classList.remove('show');
        currentDetailId = null;
      }

      // 标记为已处理
      async function markAsProcessed() {
        if (!currentDetailId) return;

        try {
          const response = await fetch(`/api/sensitive_word_hits/${currentDetailId}/process`, {
            method: 'PUT'
          });

          if (response.ok) {
            // 更新本地数据
            const hit = hitsData.find(h => h.id === currentDetailId);
            if (hit) {
              hit.is_processed = true;
              hit.processed_at = new Date().toLocaleString('zh-CN');
            }

            filteredData = [...hitsData];
            renderTable();
            closeDetailModalHandler();
            showMessage('标记为已处理成功', 'success');
          } else {
            showMessage('处理失败', 'error');
          }
        } catch (error) {
          console.error('Error marking as processed:', error);
          // 模拟成功
          const hit = hitsData.find(h => h.id === currentDetailId);
          if (hit) {
            hit.is_processed = true;
            hit.processed_at = new Date().toLocaleString('zh-CN');
          }

          filteredData = [...hitsData];
          renderTable();
          closeDetailModalHandler();
          showMessage('标记为已处理成功', 'success');
        }
      }

      // 单个标记处理
      async function markProcessed(id) {
        try {
          const response = await fetch(`/api/sensitive_word_hits/${id}/process`, {
            method: 'PUT'
          });

          if (response.ok) {
            // 更新本地数据
            const hit = hitsData.find(h => h.id === id);
            if (hit) {
              hit.is_processed = true;
              hit.processed_at = new Date().toLocaleString('zh-CN');
            }

            filteredData = [...hitsData];
            renderTable();
            showMessage('标记为已处理成功', 'success');
          } else {
            showMessage('处理失败', 'error');
          }
        } catch (error) {
          console.error('Error marking as processed:', error);
          // 模拟成功
          const hit = hitsData.find(h => h.id === id);
          if (hit) {
            hit.is_processed = true;
            hit.processed_at = new Date().toLocaleString('zh-CN');
          }

          filteredData = [...hitsData];
          renderTable();
          showMessage('标记为已处理成功', 'success');
        }
      }

      // 全选处理
      function handleSelectAll() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked;
        });
      }

      // 更新全选状态
      function updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

        selectAllCheckbox.checked = checkboxes.length === checkedBoxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
      }

      // 批量处理
      async function handleBatchProcess() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        if (checkedBoxes.length === 0) {
          showMessage('请选择要处理的记录', 'warning');
          return;
        }

        if (!confirm(`确定要批量处理选中的 ${checkedBoxes.length} 条记录吗？`)) {
          return;
        }

        const ids = Array.from(checkedBoxes).map(cb => parseInt(cb.dataset.id));

        try {
          const response = await fetch('/api/sensitive_word_hits/batch_process', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids })
          });

          if (response.ok) {
            // 更新本地数据
            ids.forEach(id => {
              const hit = hitsData.find(h => h.id === id);
              if (hit) {
                hit.is_processed = true;
                hit.processed_at = new Date().toLocaleString('zh-CN');
              }
            });

            filteredData = [...hitsData];
            renderTable();
            showMessage('批量处理成功', 'success');
          } else {
            showMessage('批量处理失败', 'error');
          }
        } catch (error) {
          console.error('Error batch processing:', error);
          // 模拟成功
          ids.forEach(id => {
            const hit = hitsData.find(h => h.id === id);
            if (hit) {
              hit.is_processed = true;
              hit.processed_at = new Date().toLocaleString('zh-CN');
            }
          });

          filteredData = [...hitsData];
          renderTable();
          showMessage('批量处理成功', 'success');
        }
      }

      // 显示消息
      function showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
          type === 'success' ? 'bg-green-500' :
          type === 'error' ? 'bg-red-500' :
          type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        messageEl.textContent = message;

        document.body.appendChild(messageEl);

        // 3秒后自动移除
        setTimeout(() => {
          if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
          }
        }, 3000);
      }
    </script>
  </body>
</html>