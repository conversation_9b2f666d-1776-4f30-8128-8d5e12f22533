<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>余额 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
            boxShadow: {
              card: "0 2px 8px rgba(0, 0, 0, 0.08)",
              dropdown: "0 4px 12px rgba(0, 0, 0, 0.1)",
              nav: "0 1px 4px rgba(0, 0, 0, 0.05)"
            },
            animation: {
              'fade-in': 'fadeIn 0.2s ease-in-out',
              'slide-down': 'slideDown 0.3s ease-out',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideDown: {
                '0%': { transform: 'translateY(-10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
      }

      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }

      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }

      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }

      .hover-card {
        transition: all 0.2s ease;
      }

      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a
              href="/"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >工作台</a
            >
            <a
              href="/financial_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >资金管理</a
            >
            <a
              href="/reconciliation_center"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >对账中心</a
            >
            <a
              href="/product_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >产品中心</a
            >
            <a
              href="/data_analysis"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >数据中心</a
            >
            <a
              href="/marketing_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >营销中心</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input
              type="text"
              placeholder="搜索功能/应用/服务"
              class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
            >
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button
            class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-notification-3-line text-xl"></i>
            <span
              class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"
            ></span>
          </button>
          <button
            class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div
              class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all"
            >
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      <aside class="w-56 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
        <div class="py-3">
          <div class="px-4 mb-4">
            <div class="relative w-full">
              <input
                type="text"
                placeholder="搜索菜单"
                class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
              />
              <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>
      
          <div class="px-4 py-2">
            <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
              <span>应用中心</span>
              <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                <i class="ri-arrow-down-s-line"></i>
              </button>
            </div>
            <ul class="space-y-0.5">
              <li>
                <a
                  href="/shop_overview"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-store-line text-lg"></i>
                  </span>
                  <span>店铺概况</span>
                </a>
              </li>
              <li>
                <a
                  href="/balance"
                  class="flex items-center px-3 py-2.5 text-sm text-primary bg-blue-50 rounded-lg sidebar-menu-item active"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 sidebar-icon">
                    <i class="ri-wallet-3-line text-lg"></i>
                  </span>
                  <span class="font-medium">余额</span>
                </a>
              </li>
              <li>
                <a
                  href="/modules"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-apps-line text-lg"></i>
                  </span>
                  <span>模块</span>
                </a>
              </li>
              <li>
                <a
                  href="/data_analysis"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-bar-chart-line text-lg"></i>
                  </span>
                  <span>统计</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      
      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">余额</h1>
            <p class="mt-1 text-sm text-gray-500">管理您的账户余额和资金流动</p>
          </div>
          
          <!-- 余额卡片 -->
          <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <div class="text-sm text-gray-500 mb-1">账户余额 (元)</div>
                <div class="text-3xl font-semibold text-gray-800">1,106,345.67</div>
                <div class="mt-2 text-sm text-gray-500">
                  上次更新时间: 2025-05-10 10:30:45
                </div>
              </div>
              <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
                <button class="px-4 py-2 bg-primary text-white rounded-button text-sm hover:bg-primary/90 transition-all flex items-center">
                  <i class="ri-add-line mr-1"></i>
                  充值
                </button>
                <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-exchange-funds-line mr-1"></i>
                  转账
                </button>
                <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-refund-2-line mr-1"></i>
                  提现
                </button>
                <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-refresh-line mr-1"></i>
                  刷新
                </button>
              </div>
            </div>
          </div>
          
          <!-- 余额明细 -->
          <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-800">余额明细</h2>
                <div class="flex items-center space-x-2">
                  <button class="px-3 py-1 text-sm bg-blue-50 text-primary rounded-md">全部</button>
                  <button class="px-3 py-1 text-sm text-gray-500 hover:bg-gray-50 rounded-md">收入</button>
                  <button class="px-3 py-1 text-sm text-gray-500 hover:bg-gray-50 rounded-md">支出</button>
                </div>
              </div>
            </div>
            
            <!-- 筛选条件 -->
            <div class="p-4 bg-gray-50 border-b border-gray-100">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">交易类型</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                    <option value="">全部类型</option>
                    <option value="payment">收款</option>
                    <option value="refund">退款</option>
                    <option value="transfer">转账</option>
                    <option value="withdraw">提现</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">交易状态</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                    <option value="">全部状态</option>
                    <option value="success">成功</option>
                    <option value="pending">处理中</option>
                    <option value="failed">失败</option>
                  </select>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">开始日期</label>
                  <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">结束日期</label>
                  <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                </div>
              </div>
              <div class="mt-4 flex justify-end">
                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-button text-sm hover:bg-gray-200 transition-all mr-2">
                  重置
                </button>
                <button class="px-4 py-2 bg-primary text-white rounded-button text-sm hover:bg-primary/90 transition-all">
                  查询
                </button>
              </div>
            </div>
            
            <!-- 交易记录表格 -->
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="bg-gray-50">
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易时间</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易类型</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易金额</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易后余额</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易状态</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-09 09:45:23</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                      <span class="inline-flex items-center">
                        <i class="ri-arrow-down-circle-line text-green-500 mr-1"></i> 收款
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-green-600">+61,634.56</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">1,106,345.67</td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        成功
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">订单支付</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-09 08:32:15</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                      <span class="inline-flex items-center">
                        <i class="ri-arrow-up-circle-line text-red-500 mr-1"></i> 提现
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600">-5,000.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">127,222.22</td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                        处理中
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">提现到银行卡</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-08 17:21:09</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                      <span class="inline-flex items-center">
                        <i class="ri-arrow-down-circle-line text-green-500 mr-1"></i> 收款
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-green-600">+2,345.67</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">132,222.22</td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        成功
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">订单支付</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-08 15:10:42</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                      <span class="inline-flex items-center">
                        <i class="ri-arrow-up-circle-line text-red-500 mr-1"></i> 转账
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600">-1,500.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">129,876.55</td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        成功
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">转账给供应商</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-08 11:05:37</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                      <span class="inline-flex items-center">
                        <i class="ri-arrow-down-circle-line text-green-500 mr-1"></i> 收款
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-green-600">+3,456.78</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">131,376.55</td>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        成功
                      </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">订单支付</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- 分页 -->
            <div class="p-4 flex justify-between items-center border-t border-gray-100">
              <div class="text-sm text-gray-500">
                共 <span class="font-medium">125</span> 条记录，当前第 <span class="font-medium">1</span> 页，共 <span class="font-medium">25</span> 页
              </div>
              <div class="flex space-x-1">
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all opacity-50 cursor-not-allowed">
                  上一页
                </button>
                <button class="px-3 py-1 text-sm text-white bg-primary rounded hover:bg-primary/90 transition-all">
                  1
                </button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                  2
                </button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                  3
                </button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                  ...
                </button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                  25
                </button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        console.log('余额页面已加载');
      });
    </script>
  </body>
</html>
