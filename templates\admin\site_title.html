<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站标题设置 - 管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .success-message {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .error-message {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    {% include 'admin/navbar.html' %}

    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">网站标题设置</h1>
            <p class="text-gray-600">修改网站顶部导航栏显示的标题</p>
        </div>

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-6 fade-in">
                        <div class="p-4 rounded-lg shadow-md {% if category == 'success' %}success-message{% else %}error-message{% endif %}">
                            <div class="flex items-center">
                                <i class="{% if category == 'success' %}ri-check-circle-line{% else %}ri-error-warning-line{% endif %} text-xl mr-3"></i>
                                <span class="font-medium">{{ message }}</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 主要内容区域 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="ri-edit-line text-blue-600 mr-2"></i>
                    标题设置
                </h2>
            </div>

            <div class="p-6">
                <!-- 当前标题显示 -->
                <div class="mb-6 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">当前网站标题</h3>
                    <p class="text-lg font-semibold text-gray-900" id="current-title-display">{{ current_title }}</p>
                </div>

                <!-- 标题修改表单 -->
                <form method="POST" class="space-y-6">
                    <div>
                        <label for="site_title" class="block text-sm font-medium text-gray-700 mb-2">
                            新的网站标题
                        </label>
                        <div class="relative">
                            <input 
                                type="text" 
                                id="site_title" 
                                name="site_title" 
                                value="{{ current_title }}"
                                required
                                maxlength="100"
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                                placeholder="请输入网站标题"
                            >
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="ri-edit-2-line text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">
                            标题将显示在网站顶部导航栏中，建议长度不超过20个字符
                        </p>
                    </div>

                    <!-- 预览区域 -->
                    <div class="bg-gray-50 rounded-lg p-4 border">
                        <h3 class="text-sm font-medium text-gray-700 mb-3">预览效果</h3>
                        <div class="bg-slate-700 text-white p-3 rounded-lg flex items-center">
                            <div class="w-8 h-8 flex items-center justify-center mr-2">
                                <i class="ri-apps-2-line text-lg"></i>
                            </div>
                            <h1 class="text-lg font-medium" id="title-preview">{{ current_title }}</h1>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                        <a href="{{ url_for('admin_dashboard') }}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">
                            <i class="ri-arrow-left-line mr-2"></i>
                            返回仪表盘
                        </a>
                        
                        <div class="flex space-x-3">
                            <button type="button" 
                                    onclick="resetTitle()"
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-200">
                                <i class="ri-refresh-line mr-2"></i>
                                重置
                            </button>
                            
                            <button type="submit" 
                                    class="inline-flex items-center px-6 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200 shadow-lg">
                                <i class="ri-save-line mr-2"></i>
                                保存标题
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                <i class="ri-information-line mr-2"></i>
                使用说明
            </h3>
            <ul class="text-blue-700 space-y-2">
                <li class="flex items-start">
                    <i class="ri-check-line mt-1 mr-2 text-blue-600"></i>
                    <span>修改后的标题会立即在所有页面的顶部导航栏中显示</span>
                </li>
                <li class="flex items-start">
                    <i class="ri-check-line mt-1 mr-2 text-blue-600"></i>
                    <span>建议使用简洁明了的标题，便于用户识别</span>
                </li>
                <li class="flex items-start">
                    <i class="ri-check-line mt-1 mr-2 text-blue-600"></i>
                    <span>标题长度建议控制在20个字符以内，以确保在移动设备上正常显示</span>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // 实时预览功能
        document.getElementById('site_title').addEventListener('input', function() {
            const newTitle = this.value || '网站标题';
            document.getElementById('title-preview').textContent = newTitle;
        });

        // 重置标题
        function resetTitle() {
            const originalTitle = '{{ current_title }}';
            document.getElementById('site_title').value = originalTitle;
            document.getElementById('title-preview').textContent = originalTitle;
        }

        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const titleInput = document.getElementById('site_title');
            const title = titleInput.value.trim();
            
            if (!title) {
                e.preventDefault();
                alert('请输入网站标题');
                titleInput.focus();
                return false;
            }
            
            if (title.length > 100) {
                e.preventDefault();
                alert('标题长度不能超过100个字符');
                titleInput.focus();
                return false;
            }
        });
    </script>
</body>
</html>
