<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>商品管理 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a href="/" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">工作台</a>
            <a href="/financial_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">资金管理</a>
            <a href="/reconciliation_center" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">对账中心</a>
            <a href="/product_management" class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all">产品中心</a>
            <a href="/data_analysis" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">数据中心</a>
            <a href="/marketing_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">营销中心</a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input type="text" placeholder="搜索功能/应用/服务" class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-notification-3-line text-xl"></i>
            <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all">
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">商品管理</h1>
            <p class="mt-1 text-sm text-gray-500">管理所有商品，设置商品分类，调整库存和价格</p>
          </div>

          <!-- 商品统计卡片 -->
          <div class="grid grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">在售商品</span>
                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center text-green-500">
                  <i class="ri-shopping-bag-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">86</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">仓库中</span>
                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center text-blue-500">
                  <i class="ri-inbox-archive-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">24</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">库存预警</span>
                <div class="w-8 h-8 rounded-full bg-orange-50 flex items-center justify-center text-orange-500">
                  <i class="ri-alert-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">5</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">商品分类</span>
                <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center text-purple-500">
                  <i class="ri-price-tag-3-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">12</div>
            </div>
          </div>

          <!-- 商品操作按钮 -->
          <div class="mb-6 flex items-center justify-between">
            <div class="flex space-x-3">
              <button class="px-4 py-2 bg-primary text-white rounded-lg text-sm hover:bg-primary/90 transition-all flex items-center">
                <i class="ri-add-line mr-1"></i>
                添加商品
              </button>
              <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-upload-2-line mr-1"></i>
                批量导入
              </button>
              <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-download-2-line mr-1"></i>
                批量导出
              </button>
            </div>
            <div class="flex items-center space-x-2">
              <div class="relative">
                <input type="text" placeholder="商品名称/编码" class="pl-9 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                  <i class="ri-search-line"></i>
                </div>
              </div>
              <button class="px-3 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-filter-3-line mr-1"></i>
                筛选
              </button>
            </div>
          </div>

          <!-- 商品分类标签 -->
          <div class="mb-6 bg-white rounded-lg shadow-sm p-4">
            <div class="flex items-center space-x-2 mb-3">
              <span class="text-sm font-medium text-gray-700">商品分类:</span>
              <button class="px-3 py-1 bg-primary text-white rounded-full text-sm">全部</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">办公家具</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">电子设备</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">办公用品</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">智能设备</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">其他分类</button>
              <button class="px-2 py-1 text-gray-500 hover:text-primary transition-all">
                <i class="ri-add-circle-line"></i>
              </button>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-700">商品状态:</span>
              <button class="px-3 py-1 bg-primary text-white rounded-full text-sm">全部</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">在售</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">仓库中</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">已售罄</button>
            </div>
          </div>

          <!-- 商品列表 -->
          <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
            <table class="w-full">
              <thead class="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品信息</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">销量</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <!-- 商品项 1 -->
                <tr>
                  <td class="py-4 px-4">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-start">
                      <img src="https://img.alicdn.com/imgextra/i3/2091362823/O1CN01i0OTy31Wiz53ldAEI_!!2091362823.jpg_.webp" alt="商品图片" class="w-12 h-12 rounded object-cover mr-3">
                      <div>
                        <div class="text-sm font-medium text-gray-800 mb-1">高级商务办公椅人体工学椅</div>
                        <div class="text-xs text-gray-500">商品编码: SP2025050001</div>
                        <div class="text-xs text-gray-500">分类: 办公家具</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm font-medium text-gray-800">¥1,299.00</div>
                    <div class="text-xs text-gray-500 line-through">¥1,599.00</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">86</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">42</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">在售</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下架</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 商品项 2 -->
                <tr>
                  <td class="py-4 px-4">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-start">
                      <img src="https://img.alicdn.com/imgextra/i1/2216993959661/O1CN01IOhJvU2LEnQqTjdOn_!!2216993959661.jpg_.webp" alt="商品图片" class="w-12 h-12 rounded object-cover mr-3">
                      <div>
                        <div class="text-sm font-medium text-gray-800 mb-1">智能手表健康监测多功能运动手环</div>
                        <div class="text-xs text-gray-500">商品编码: SP2025050002</div>
                        <div class="text-xs text-gray-500">分类: 智能设备</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm font-medium text-gray-800">¥899.00</div>
                    <div class="text-xs text-gray-500 line-through">¥1,099.00</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">32</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">78</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">在售</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下架</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 商品项 3 -->
                <tr>
                  <td class="py-4 px-4">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-start">
                      <img src="https://img.alicdn.com/imgextra/i1/2213057543129/O1CN01IpO9OQ1Yz8JUb7qQH_!!2213057543129.png_.webp" alt="商品图片" class="w-12 h-12 rounded object-cover mr-3">
                      <div>
                        <div class="text-sm font-medium text-gray-800 mb-1">多功能无线充电器手机支架</div>
                        <div class="text-xs text-gray-500">商品编码: SP2025050003</div>
                        <div class="text-xs text-gray-500">分类: 电子设备</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm font-medium text-gray-800">¥199.00</div>
                    <div class="text-xs text-gray-500 line-through">¥249.00</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-red-500 font-medium">4</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">126</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-orange-50 text-orange-600 rounded-full">库存预警</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下架</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- 分页 -->
            <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div class="text-sm text-gray-500">
                显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">86</span> 条
              </div>
              <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">上一页</button>
                <button class="px-3 py-1 text-sm text-white bg-primary rounded hover:bg-primary/90 transition-all">1</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">2</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">3</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下一页</button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      // 这里可以添加商品管理相关的JavaScript代码
      document.addEventListener('DOMContentLoaded', function() {
        console.log('商品管理页面已加载');
      });
    </script>
  </body>
</html>
