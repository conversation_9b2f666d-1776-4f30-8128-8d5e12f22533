<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      .submenu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
      }
      .submenu.show {
        max-height: 200px;
      }
      .menu-arrow {
        transition: transform 0.3s ease;
      }
      .menu-arrow.rotated {
        transform: rotate(180deg);
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ system_settings.labels.system_title|default(contents.header_title|default('finviz数据提取后台')) }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ system_settings.labels.language_text|default(contents.language_text|default('语言')) }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ system_settings.labels.username_display|default(contents.username|default('admin')) }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}
      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>{{ system_settings.breadcrumb.user_management|default(contents.breadcrumb_parent|default('用户管理')) }}</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>{{ system_settings.breadcrumb.user_list|default(contents.breadcrumb_current|default('用户列表')) }}</span>
          </div>
        </div>
        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                placeholder="{{ system_settings.search.user_placeholder|default(contents.search_placeholder|default('搜索用户')) }}"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <button
              class="bg-blue-500 text-white px-4 py-2 rounded-button flex items-center ml-4"
            >
              <span>{{ system_settings.buttons.add_user|default(contents.add_button|default('添加用户')) }}</span>
            </button>
            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer"
              >
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium">{{ system_settings.table_headers.serial_number|default(contents.table_header_1|default('序号')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.user_id|default(contents.table_header_2|default('用户ID')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.username|default(contents.table_header_3|default('用户名')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.phone|default(contents.table_header_4|default('手机号')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.user_code|default(contents.table_header_5|default('用户编码')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.avatar|default(contents.table_header_6|default('头像')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.create_time|default(contents.table_header_7|default('创建时间')) }}</th>
                  <th class="font-medium">{{ system_settings.table_headers.actions|default(contents.table_header_8|default('操作')) }}</th>
                </tr>
              </thead>
              <tbody>
                <!-- 表格数据行 -->
                {% if user_samples %}
                  {% for user in user_samples %}
                  <tr class="border-b border-gray-200">
                    <td>{{ loop.index }}</td>
                    <td>{{ user.user_id }}</td>
                    <td>{{ user.username }}</td>
                    <td>{{ user.phone }}</td>
                    <td>{{ user.user_code }}</td>
                    <td>
                      <div class="w-8 h-8 bg-gray-100 rounded"></div>
                    </td>
                    <td>{{ user.create_time }}</td>
                    <td>
                      <button class="text-blue-500 whitespace-nowrap">
                        {{ system_settings.buttons.more|default(contents.more_button|default('更多')) }}
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <!-- 如果没有数据库数据，显示默认数据 -->
                  {% for i in range(1, 11) %}
                  <tr class="border-b border-gray-200">
                    <td>{{ i }}</td>
                    <td>用户{{ 73684 - (i-1) * 10000 }}</td>
                    <td>{{ ['PDG8AHJL4', 'EGCTTGYQ', 'KALSXLEW', 'MH2KMAG2', 'MT9KGBJK', 'YKVDEKVR', '8VDSEQDK', 'NR7WY73R', 'Y8L8KY0B', 'AK8Z57B8'][i-1] }}</td>
                    <td>{{ ['15867846364', '18235023323', '13128677720', '17521377116', '18051568804', '18325911624', '17672516868', '15835724563', '13002911636', '13120868200'][i-1] }}</td>
                    <td>{{ ['n5Fgmwa2k', '5r8pmwa2k', 'ct5gmwa2k', 'j18pmwa2k', 'q18gmwa2k', 'x16gmwa2k', '4t8gmwa2k', 'b16gmwa2k', 'p16gmwa2k', 'w16gmwa2k'][i-1] }}</td>
                    <td>
                      <div class="w-8 h-8 bg-gray-100 rounded"></div>
                    </td>
                    <td>{{ ['2023-07-25 11:04', '2023-07-25 10:02', '2023-07-25 08:13', '2023-07-24 22:08', '2023-07-24 20:33', '2023-07-24 19:55', '2023-07-24 17:50', '2023-07-24 17:48', '2023-07-24 16:31', '2023-07-24 16:16'][i-1] }}</td>
                    <td>
                      <button class="text-blue-500 whitespace-nowrap">
                        {{ contents.more_button|default('更多') }}
                      </button>
                    </td>
                  </tr>
                  {% endfor %}
                {% endif %}
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600" id="totalRecords">共 0 条</div>
            <div class="flex items-center" id="pagination">
              <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </main>
    </div>
    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script>
      // 设置当前页面标识
      window.currentPage = 'user_list';

      // 全局变量
      let currentPage = 1;
      let totalPages = 1;
      let usersData = [];
      let searchTerm = '';

      document.addEventListener("DOMContentLoaded", function () {
        // 初始化页面
        loadUsers();

        // 搜索功能
        const searchInput = document.querySelector('input[placeholder*="搜索"]');
        if (searchInput) {
          searchInput.addEventListener('input', function() {
            searchTerm = this.value;
            currentPage = 1;
            loadUsers();
          });
        }

        // 菜单将由sidebar_menu.js自动初始化
      });

      // 加载用户数据
      async function loadUsers() {
        try {
          const params = new URLSearchParams({
            page: currentPage,
            per_page: 10,
            search: searchTerm
          });

          const response = await fetch(`/api/sample_data/users?${params}`);
          if (response.ok) {
            const data = await response.json();
            usersData = data.users;
            updateTable(data.users);
            updatePagination(data.pagination);
          } else {
            console.error('Failed to load users');
            // 如果API失败，保持现有的静态数据显示
          }
        } catch (error) {
          console.error('Error loading users:', error);
          // 如果API失败，保持现有的静态数据显示
        }
      }

      // 更新表格数据
      function updateTable(users) {
        const tbody = document.querySelector('tbody');
        if (!tbody || users.length === 0) return;

        tbody.innerHTML = users.map((user, index) => `
          <tr class="border-b border-gray-200">
            <td>${(currentPage - 1) * 10 + index + 1}</td>
            <td>${user.user_id}</td>
            <td>${user.username}</td>
            <td>${user.phone}</td>
            <td>${user.user_code}</td>
            <td>
              <div class="w-8 h-8 bg-gray-100 rounded"></div>
            </td>
            <td>${user.create_time}</td>
            <td>
              <button class="text-blue-500 whitespace-nowrap">更多</button>
            </td>
          </tr>
        `).join('');
      }

      // 更新分页
      function updatePagination(pagination) {
        const totalRecords = document.getElementById('totalRecords');
        const paginationContainer = document.getElementById('pagination');

        if (totalRecords) {
          totalRecords.textContent = `共 ${pagination.total} 条`;
        }

        if (!paginationContainer) return;

        totalPages = pagination.pages;
        let paginationHTML = '';

        // 上一页按钮
        paginationHTML += `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${!pagination.has_prev ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${pagination.has_prev ? `changePage(${pagination.prev_num})` : ''}">
            <i class="ri-arrow-left-s-line"></i>
          </div>
        `;

        // 页码按钮
        for (let i = 1; i <= totalPages; i++) {
          if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
              <div class="pagination-item mx-1 ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                ${i}
              </div>
            `;
          } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
        }

        // 下一页按钮
        paginationHTML += `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${!pagination.has_next ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${pagination.has_next ? `changePage(${pagination.next_num})` : ''}">
            <i class="ri-arrow-right-s-line"></i>
          </div>
        `;

        paginationContainer.innerHTML = paginationHTML;
      }

      // 切换页面
      function changePage(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
          currentPage = page;
          loadUsers();
        }
      }
    </script>
  </body>
</html>
