#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合数据迁移脚本
将所有硬编码的示例数据写入到data.sqlite数据库中
"""

import os
import sys
from datetime import datetime, timezone

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入Flask应用和数据库模型
import importlib.util
spec = importlib.util.spec_from_file_location("yehuo_app", "yehuo-3823.py")
yehuo_app = importlib.util.module_from_spec(spec)
spec.loader.exec_module(yehuo_app)

# 获取需要的对象
app = yehuo_app.app
db = yehuo_app.db
UserSampleData = yehuo_app.UserSampleData
GroupSampleData = yehuo_app.GroupSampleData
SensitiveWordSampleData = yehuo_app.SensitiveWordSampleData
MessageSampleData = yehuo_app.MessageSampleData
BannedUserSampleData = yehuo_app.BannedUserSampleData
MenuConfiguration = yehuo_app.MenuConfiguration
SystemSettings = yehuo_app.SystemSettings

def migrate_user_data():
    """迁移用户示例数据"""
    print("\n=== 迁移用户示例数据 ===")

    user_samples = [
        {'user_id': '用户73684', 'username': 'PDG8AHJL4', 'phone': '15867846364', 'user_code': 'n5Fgmwa2k', 'create_time': '2023-07-25 11:04'},
        {'user_id': '用户63684', 'username': 'EGCTTGYQ', 'phone': '18235023323', 'user_code': '5r8pmwa2k', 'create_time': '2023-07-25 10:02'},
        {'user_id': '用户53684', 'username': 'KALSXLEW', 'phone': '13128677720', 'user_code': 'ct5gmwa2k', 'create_time': '2023-07-25 08:13'},
        {'user_id': '用户43684', 'username': 'MH2KMAG2', 'phone': '17521377116', 'user_code': 'j18pmwa2k', 'create_time': '2023-07-24 22:08'},
        {'user_id': '用户33684', 'username': 'MT9KGBJK', 'phone': '18051568804', 'user_code': 'q18gmwa2k', 'create_time': '2023-07-24 20:33'},
        {'user_id': '用户23684', 'username': 'YKVDEKVR', 'phone': '18325911624', 'user_code': 'x16gmwa2k', 'create_time': '2023-07-24 19:55'},
        {'user_id': '用户13684', 'username': '8VDSEQDK', 'phone': '17672516868', 'user_code': '4t8gmwa2k', 'create_time': '2023-07-24 17:50'},
        {'user_id': '用户3684', 'username': 'NR7WY73R', 'phone': '15835724563', 'user_code': 'b16gmwa2k', 'create_time': '2023-07-24 17:48'},
        {'user_id': '用户93683', 'username': 'Y8L8KY0B', 'phone': '13002911636', 'user_code': 'p16gmwa2k', 'create_time': '2023-07-24 16:31'},
        {'user_id': '用户83683', 'username': 'AK8Z57B8', 'phone': '13120868200', 'user_code': 'w16gmwa2k', 'create_time': '2023-07-24 16:16'},
    ]

    existing_count = UserSampleData.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条用户数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            UserSampleData.query.delete()
            print("已清空现有用户数据")
        else:
            print("跳过用户数据导入")
            return 0

    success_count = 0
    for user_data in user_samples:
        try:
            existing = UserSampleData.query.filter_by(user_id=user_data['user_id']).first()
            if existing:
                print(f"用户 {user_data['user_id']} 已存在，跳过")
                continue

            user_sample = UserSampleData(**user_data)
            db.session.add(user_sample)
            success_count += 1
            print(f"添加用户: {user_data['username']} ({user_data['user_id']})")

        except Exception as e:
            print(f"添加用户 {user_data.get('user_id', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_group_data():
    """迁移群组示例数据"""
    print("\n=== 迁移群组示例数据 ===")

    group_samples = [
        {'group_id': 'group001', 'group_name': '用户产品、服务、小、群', 'owner': 'msg2k', 'member_count': 4, 'group_type': '普通群组', 'create_time': '2023-07-25 16:14', 'description': '产品服务讨论群'},
        {'group_id': 'group002', 'group_name': '用户产品、服务、小、一群组', 'owner': 'msg2k', 'member_count': 3, 'group_type': '普通群组', 'create_time': '2023-07-24 17:51', 'description': '产品服务交流'},
        {'group_id': 'group003', 'group_name': '用户产品', 'owner': 'msg2k', 'member_count': 20, 'group_type': '普通群组', 'create_time': '2023-07-24 16:57', 'description': '用户产品讨论'},
        {'group_id': 'group004', 'group_name': '用户产品1、小K、群组2', 'owner': 'msg2k', 'member_count': 6, 'group_type': '普通群组', 'create_time': '2023-07-21 21:08', 'description': '产品开发群'},
        {'group_id': 'group005', 'group_name': '技术交流群', 'owner': 'admin', 'member_count': 15, 'group_type': '技术群组', 'create_time': '2023-07-20 14:30', 'description': '技术讨论与分享'},
        {'group_id': 'group006', 'group_name': 'finviz数据提取后台官方群', 'owner': 'admin', 'member_count': 128, 'group_type': '官方群组', 'create_time': '2023-07-20 10:00', 'description': '官方公告和活动群'},
        {'group_id': 'group007', 'group_name': '游戏攻略分享群', 'owner': 'game_master', 'member_count': 89, 'group_type': '游戏群组', 'create_time': '2023-07-19 18:30', 'description': '游戏攻略和心得分享'},
        {'group_id': 'group008', 'group_name': '新手指导群', 'owner': 'helper01', 'member_count': 45, 'group_type': '新手群组', 'create_time': '2023-07-19 15:20', 'description': '新手玩家指导和帮助'},
        {'group_id': 'group009', 'group_name': '竞技赛事讨论群', 'owner': 'esports_fan', 'member_count': 67, 'group_type': '赛事群组', 'create_time': '2023-07-18 20:45', 'description': '电竞赛事讨论和预测'},
        {'group_id': 'group010', 'group_name': '建筑、建材、钢丁、钢', 'owner': 'builder_pro', 'member_count': 23, 'group_type': '行业群组', 'create_time': '2023-07-18 14:15', 'description': '建筑行业交流群'},
        {'group_id': 'group011', 'group_name': '用户产品小K钢筑建筑群', 'owner': 'architect', 'member_count': 37, 'group_type': '专业群组', 'create_time': '2023-07-17 16:30', 'description': '建筑设计专业讨论'},
        {'group_id': 'group012', 'group_name': '游戏公会联盟', 'owner': 'guild_leader', 'member_count': 156, 'group_type': '公会群组', 'create_time': '2023-07-17 12:00', 'description': '各大公会联盟交流'},
        {'group_id': 'group013', 'group_name': '装备交易群', 'owner': 'trader_king', 'member_count': 78, 'group_type': '交易群组', 'create_time': '2023-07-16 19:20', 'description': '游戏装备买卖交易'},
        {'group_id': 'group014', 'group_name': '策略讨论群', 'owner': 'strategist', 'member_count': 34, 'group_type': '策略群组', 'create_time': '2023-07-16 11:45', 'description': '游戏策略和战术讨论'},
        {'group_id': 'group015', 'group_name': '休闲娱乐群', 'owner': 'fun_master', 'member_count': 92, 'group_type': '娱乐群组', 'create_time': '2023-07-15 21:30', 'description': '休闲聊天和娱乐活动'},
        {'group_id': 'group016', 'group_name': '高手进阶群', 'owner': 'pro_player', 'member_count': 56, 'group_type': '高级群组', 'create_time': '2023-07-15 17:10', 'description': '高级玩家技巧交流'},
        {'group_id': 'group017', 'group_name': '活动组织群', 'owner': 'event_organizer', 'member_count': 41, 'group_type': '活动群组', 'create_time': '2023-07-14 13:25', 'description': '线上线下活动组织'},
        {'group_id': 'group018', 'group_name': '反馈建议群', 'owner': 'feedback_admin', 'member_count': 29, 'group_type': '反馈群组', 'create_time': '2023-07-14 09:40', 'description': '用户反馈和建议收集'},
        {'group_id': 'group019', 'group_name': '测试体验群', 'owner': 'test_manager', 'member_count': 18, 'group_type': '测试群组', 'create_time': '2023-07-13 16:55', 'description': '新功能测试和体验'},
        {'group_id': 'group020', 'group_name': '社区管理群', 'owner': 'community_admin', 'member_count': 12, 'group_type': '管理群组', 'create_time': '2023-07-13 10:30', 'description': '社区管理员内部交流'}
    ]

    existing_count = GroupSampleData.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条群组数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            GroupSampleData.query.delete()
            print("已清空现有群组数据")
        else:
            print("跳过群组数据导入")
            return 0

    success_count = 0
    for group_data in group_samples:
        try:
            existing = GroupSampleData.query.filter_by(group_id=group_data['group_id']).first()
            if existing:
                print(f"群组 {group_data['group_id']} 已存在，跳过")
                continue

            group_sample = GroupSampleData(**group_data)
            db.session.add(group_sample)
            success_count += 1
            print(f"添加群组: {group_data['group_name']} ({group_data['group_id']})")

        except Exception as e:
            print(f"添加群组 {group_data.get('group_id', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_sensitive_word_data():
    """迁移敏感词示例数据"""
    print("\n=== 迁移敏感词示例数据 ===")

    sensitive_word_samples = [
        {'word': '垃圾', 'category': '广告垃圾', 'severity': '中等', 'action': '过滤', 'description': '垃圾信息过滤'},
        {'word': '色情', 'category': '色情内容', 'severity': '高', 'action': '封禁', 'description': '色情内容严格禁止'},
        {'word': '暴力', 'category': '暴力内容', 'severity': '高', 'action': '警告', 'description': '暴力内容警告'},
        {'word': '广告', 'category': '广告垃圾', 'severity': '低', 'action': '过滤', 'description': '广告信息过滤'},
        {'word': '诈骗', 'category': '其他', 'severity': '高', 'action': '封禁', 'description': '诈骗信息严厉打击'},
    ]

    existing_count = SensitiveWordSampleData.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条敏感词数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            SensitiveWordSampleData.query.delete()
            print("已清空现有敏感词数据")
        else:
            print("跳过敏感词数据导入")
            return 0

    success_count = 0
    for word_data in sensitive_word_samples:
        try:
            existing = SensitiveWordSampleData.query.filter_by(word=word_data['word']).first()
            if existing:
                print(f"敏感词 {word_data['word']} 已存在，跳过")
                continue

            word_sample = SensitiveWordSampleData(**word_data)
            db.session.add(word_sample)
            success_count += 1
            print(f"添加敏感词: {word_data['word']} ({word_data['category']})")

        except Exception as e:
            print(f"添加敏感词 {word_data.get('word', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_message_data():
    """迁移消息示例数据"""
    print("\n=== 迁移消息示例数据 ===")

    message_samples = [
        {'message_id': 'msg_1001', 'user_id': 'user001', 'message_type': 'text', 'content': '这是一条文本消息内容 1', 'receiver': '群组A', 'send_time': '2023-07-25 11:04', 'status': '已发送'},
        {'message_id': 'msg_1002', 'user_id': 'user002', 'message_type': 'image', 'content': '[图片消息]', 'receiver': '用户B', 'send_time': '2023-07-25 10:30', 'status': '已读'},
        {'message_id': 'msg_1003', 'user_id': 'user003', 'message_type': 'file', 'content': '[文件消息] document.pdf', 'receiver': '群组B', 'send_time': '2023-07-25 09:15', 'status': '已发送'},
        {'message_id': 'msg_1004', 'user_id': 'user004', 'message_type': 'voice', 'content': '[语音消息] 15秒', 'receiver': '用户C', 'send_time': '2023-07-24 22:45', 'status': '已读'},
        {'message_id': 'msg_1005', 'user_id': 'user005', 'message_type': 'video', 'content': '[视频消息] 30秒', 'receiver': '群组C', 'send_time': '2023-07-24 20:20', 'status': '已发送'},
    ]

    existing_count = MessageSampleData.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条消息数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            MessageSampleData.query.delete()
            print("已清空现有消息数据")
        else:
            print("跳过消息数据导入")
            return 0

    success_count = 0
    for message_data in message_samples:
        try:
            existing = MessageSampleData.query.filter_by(message_id=message_data['message_id']).first()
            if existing:
                print(f"消息 {message_data['message_id']} 已存在，跳过")
                continue

            message_sample = MessageSampleData(**message_data)
            db.session.add(message_sample)
            success_count += 1
            print(f"添加消息: {message_data['message_id']} ({message_data['message_type']})")

        except Exception as e:
            print(f"添加消息 {message_data.get('message_id', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_banned_user_data():
    """迁移封禁用户示例数据"""
    print("\n=== 迁移封禁用户示例数据 ===")

    banned_user_samples = [
        {'user_id': 'banned001', 'username': 'SPAMMER01', 'phone': '13800000001', 'ban_reason': '发送垃圾信息', 'ban_time': '2023-07-25 10:00', 'ban_duration': '7天', 'status': '已封禁'},
        {'user_id': 'banned002', 'username': 'VIOLATOR02', 'phone': '13800000002', 'ban_reason': '发布违规内容', 'ban_time': '2023-07-24 15:30', 'ban_duration': '30天', 'status': '已封禁'},
        {'user_id': 'banned003', 'username': 'ABUSER03', 'phone': '13800000003', 'ban_reason': '恶意骚扰其他用户', 'ban_time': '2023-07-23 09:15', 'ban_duration': '永久', 'status': '已封禁'},
        {'user_id': 'banned004', 'username': 'CHEATER04', 'phone': '13800000004', 'ban_reason': '使用外挂作弊', 'ban_time': '2023-07-22 20:45', 'ban_duration': '14天', 'status': '已封禁'},
        {'user_id': 'banned005', 'username': 'TOXIC05', 'phone': '13800000005', 'ban_reason': '恶意刷屏', 'ban_time': '2023-07-21 14:20', 'ban_duration': '3天', 'status': '已解封'},
        {'user_id': 'banned006', 'username': 'FRAUD06', 'phone': '13800000006', 'ban_reason': '诈骗行为', 'ban_time': '2023-07-20 11:30', 'ban_duration': '永久', 'status': '已封禁'},
        {'user_id': 'banned007', 'username': 'HACKER07', 'phone': '13800000007', 'ban_reason': '恶意攻击系统', 'ban_time': '2023-07-19 16:45', 'ban_duration': '永久', 'status': '已封禁'},
        {'user_id': 'banned008', 'username': 'SELLER08', 'phone': '13800000008', 'ban_reason': '违规交易', 'ban_time': '2023-07-18 13:10', 'ban_duration': '7天', 'status': '已解封'},
        {'user_id': 'banned009', 'username': 'MULTI09', 'phone': '13800000009', 'ban_reason': '多开账号', 'ban_time': '2023-07-17 18:25', 'ban_duration': '14天', 'status': '已封禁'},
        {'user_id': 'banned010', 'username': 'RACIST10', 'phone': '13800000010', 'ban_reason': '发布歧视言论', 'ban_time': '2023-07-16 12:40', 'ban_duration': '30天', 'status': '已封禁'},
        {'user_id': 'banned011', 'username': 'ADVERT11', 'phone': '13800000011', 'ban_reason': '恶意广告', 'ban_time': '2023-07-15 09:55', 'ban_duration': '7天', 'status': '已解封'},
        {'user_id': 'banned012', 'username': 'VULGAR12', 'phone': '13800000012', 'ban_reason': '使用低俗语言', 'ban_time': '2023-07-14 21:15', 'ban_duration': '3天', 'status': '已解封'},
        {'user_id': 'banned013', 'username': 'THREAT13', 'phone': '13800000013', 'ban_reason': '威胁恐吓他人', 'ban_time': '2023-07-13 17:30', 'ban_duration': '30天', 'status': '已封禁'},
        {'user_id': 'banned014', 'username': 'LEAK14', 'phone': '13800000014', 'ban_reason': '泄露他人隐私', 'ban_time': '2023-07-12 14:45', 'ban_duration': '14天', 'status': '已封禁'},
        {'user_id': 'banned015', 'username': 'MINOR15', 'phone': '13800000015', 'ban_reason': '未成年人违规', 'ban_time': '2023-07-11 10:20', 'ban_duration': '7天', 'status': '已解封'},
    ]

    existing_count = BannedUserSampleData.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条封禁用户数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            BannedUserSampleData.query.delete()
            print("已清空现有封禁用户数据")
        else:
            print("跳过封禁用户数据导入")
            return 0

    success_count = 0
    for banned_data in banned_user_samples:
        try:
            existing = BannedUserSampleData.query.filter_by(user_id=banned_data['user_id']).first()
            if existing:
                print(f"封禁用户 {banned_data['user_id']} 已存在，跳过")
                continue

            banned_sample = BannedUserSampleData(**banned_data)
            db.session.add(banned_sample)
            success_count += 1
            print(f"添加封禁用户: {banned_data['username']} ({banned_data['user_id']})")

        except Exception as e:
            print(f"添加封禁用户 {banned_data.get('user_id', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_menu_configuration():
    """迁移菜单配置数据"""
    print("\n=== 迁移菜单配置数据 ===")

    menu_configs = [
        {'menu_id': 'user_management', 'menu_name': '用户管理', 'menu_icon': 'ri-user-line', 'menu_order': 1, 'is_active': True},
        {'menu_id': 'sensitive_words', 'menu_name': '敏感词管理', 'menu_icon': 'ri-spam-2-line', 'menu_order': 2, 'is_active': True},
        {'menu_id': 'group_management', 'menu_name': '群组管理', 'menu_icon': 'ri-group-line', 'menu_order': 3, 'is_active': True},
        {'menu_id': 'message_management', 'menu_name': '消息管理', 'menu_icon': 'ri-message-2-line', 'menu_order': 4, 'is_active': True},
        {'menu_id': 'robot_management', 'menu_name': '机器人管理', 'menu_icon': 'ri-robot-line', 'menu_order': 5, 'is_active': True},
        {'menu_id': 'channel_management', 'menu_name': '频道管理', 'menu_icon': 'ri-broadcast-line', 'menu_order': 6, 'is_active': True},
        {'menu_id': 'settings', 'menu_name': '设置', 'menu_icon': 'ri-settings-line', 'menu_order': 7, 'is_active': True},
    ]

    existing_count = MenuConfiguration.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条菜单配置数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            MenuConfiguration.query.delete()
            print("已清空现有菜单配置数据")
        else:
            print("跳过菜单配置数据导入")
            return 0

    success_count = 0
    for menu_data in menu_configs:
        try:
            existing = MenuConfiguration.query.filter_by(menu_id=menu_data['menu_id']).first()
            if existing:
                print(f"菜单配置 {menu_data['menu_id']} 已存在，跳过")
                continue

            menu_config = MenuConfiguration(**menu_data)
            db.session.add(menu_config)
            success_count += 1
            print(f"添加菜单配置: {menu_data['menu_name']} ({menu_data['menu_id']})")

        except Exception as e:
            print(f"添加菜单配置 {menu_data.get('menu_id', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_system_settings():
    """迁移系统设置数据"""
    print("\n=== 迁移系统设置数据 ===")

    system_settings = [
        {'setting_key': 'system_name', 'setting_value': 'finviz数据提取后台', 'setting_description': '系统名称'},
        {'setting_key': 'max_users', 'setting_value': '10000', 'setting_description': '最大用户数量'},
        {'setting_key': 'max_groups', 'setting_value': '1000', 'setting_description': '最大群组数量'},
        {'setting_key': 'message_retention_days', 'setting_value': '30', 'setting_description': '消息保留天数'},
        {'setting_key': 'auto_ban_threshold', 'setting_value': '5', 'setting_description': '自动封禁阈值'},
        {'setting_key': 'sensitive_word_action', 'setting_value': 'filter', 'setting_description': '敏感词默认处理方式'},
        {'setting_key': 'registration_enabled', 'setting_value': 'true', 'setting_description': '是否允许注册'},
        {'setting_key': 'maintenance_mode', 'setting_value': 'false', 'setting_description': '维护模式'},
    ]

    existing_count = SystemSettings.query.count()
    if existing_count > 0:
        print(f"数据库中已有 {existing_count} 条系统设置数据")
        choice = input("是否要清空并重新导入？(y/N): ").strip().lower()
        if choice == 'y' or choice == 'yes':
            SystemSettings.query.delete()
            print("已清空现有系统设置数据")
        else:
            print("跳过系统设置数据导入")
            return 0

    success_count = 0
    for setting_data in system_settings:
        try:
            existing = SystemSettings.query.filter_by(setting_key=setting_data['setting_key']).first()
            if existing:
                print(f"系统设置 {setting_data['setting_key']} 已存在，跳过")
                continue

            setting = SystemSettings(**setting_data)
            db.session.add(setting)
            success_count += 1
            print(f"添加系统设置: {setting_data['setting_key']} = {setting_data['setting_value']}")

        except Exception as e:
            print(f"添加系统设置 {setting_data.get('setting_key', 'unknown')} 时出错: {str(e)}")
            continue

    return success_count

def migrate_all_data():
    """迁移所有示例数据"""
    print("=== JJ竞技世界管理系统 - 数据迁移工具 ===")
    print("本工具将把所有硬编码的示例数据迁移到data.sqlite数据库中")
    print()

    with app.app_context():
        try:
            # 显示菜单
            print("请选择要迁移的数据类型:")
            print("1. 用户示例数据")
            print("2. 群组示例数据")
            print("3. 敏感词示例数据")
            print("4. 消息示例数据")
            print("5. 封禁用户示例数据")
            print("6. 菜单配置数据")
            print("7. 系统设置数据")
            print("8. 迁移所有数据")
            print("0. 退出")
            print()

            choice = input("请输入选择 (0-8): ").strip()

            total_success = 0

            if choice == '1':
                total_success += migrate_user_data()
            elif choice == '2':
                total_success += migrate_group_data()
            elif choice == '3':
                total_success += migrate_sensitive_word_data()
            elif choice == '4':
                total_success += migrate_message_data()
            elif choice == '5':
                total_success += migrate_banned_user_data()
            elif choice == '6':
                total_success += migrate_menu_configuration()
            elif choice == '7':
                total_success += migrate_system_settings()
            elif choice == '8':
                print("开始迁移所有数据...")
                total_success += migrate_user_data()
                total_success += migrate_group_data()
                total_success += migrate_sensitive_word_data()
                total_success += migrate_message_data()
                total_success += migrate_banned_user_data()
                total_success += migrate_menu_configuration()
                total_success += migrate_system_settings()
            elif choice == '0':
                print("退出迁移工具")
                return
            else:
                print("无效选择，请重新运行")
                return

            # 提交所有更改
            db.session.commit()
            print(f"\n=== 迁移完成 ===")
            print(f"总共成功迁移 {total_success} 条记录")

            # 显示最终统计
            print("\n=== 数据库统计 ===")
            print(f"用户数据: {UserSampleData.query.count()} 条")
            print(f"群组数据: {GroupSampleData.query.count()} 条")
            print(f"敏感词数据: {SensitiveWordSampleData.query.count()} 条")
            print(f"消息数据: {MessageSampleData.query.count()} 条")
            print(f"封禁用户数据: {BannedUserSampleData.query.count()} 条")
            print(f"菜单配置数据: {MenuConfiguration.query.count()} 条")
            print(f"系统设置数据: {SystemSettings.query.count()} 条")

        except Exception as e:
            db.session.rollback()
            print(f"数据迁移失败: {str(e)}")
            raise

if __name__ == "__main__":
    migrate_all_data()