/**
 * 模板配置加载器
 * 用于加载JSON配置并动态渲染模板内容
 */

class TemplateLoader {
  constructor(templateName) {
    this.templateName = templateName || 'alipay';
    this.config = null;
  }

  /**
   * 加载模板配置
   * @returns {Promise} 返回加载配置的Promise
   */
  async loadConfig() {
    try {
      const response = await fetch(`/api/template_config/${this.templateName}`);
      if (!response.ok) {
        throw new Error(`Failed to load template config: ${response.statusText}`);
      }
      this.config = await response.json();
      return this.config;
    } catch (error) {
      console.error('Error loading template config:', error);
      throw error;
    }
  }

  /**
   * 应用全局设置
   */
  applyGlobalSettings() {
    if (!this.config || !this.config.global_settings) return;

    const settings = this.config.global_settings;

    // 应用主色调
    if (settings.primary_color) {
      document.documentElement.style.setProperty('--primary-color', settings.primary_color);
    }

    // 应用布局样式
    if (settings.layout_style) {
      document.body.setAttribute('data-layout', settings.layout_style);
    }

    // 控制侧边栏菜单项的显示/隐藏
    this.toggleSidebarItems('shop_overview', settings.show_shop_overview);
    this.toggleSidebarItems('balance', settings.show_balance);
    this.toggleSidebarItems('modules', settings.show_modules);
    this.toggleSidebarItems('statistics', settings.show_statistics);
  }

  /**
   * 切换侧边栏菜单项的显示/隐藏
   * @param {string} itemId - 菜单项ID
   * @param {boolean} show - 是否显示
   */
  toggleSidebarItems(itemId, show) {
    const item = document.querySelector(`[data-sidebar-id="${itemId}"]`);
    if (item) {
      item.style.display = show ? 'flex' : 'none';
    }
  }

  /**
   * 渲染顶部导航栏
   */
  renderHeader() {
    if (!this.config || !this.config.header) return;

    const header = this.config.header;

    // 设置标题
    const titleElement = document.querySelector('.header-title');
    if (titleElement && header.title) {
      titleElement.textContent = header.title;
    }

    // 设置logo
    const logoElement = document.querySelector('.header-logo');
    if (logoElement && header.logo) {
      logoElement.className = `${header.logo} text-2xl mr-2`;
    }

    // 设置搜索框
    const searchInput = document.querySelector('.header-search');
    if (searchInput && header.search) {
      searchInput.placeholder = header.search.placeholder;
      searchInput.style.display = header.search.enabled ? 'block' : 'none';
    }

    // 渲染导航菜单
    this.renderNavigation(header.navigation);
  }

  /**
   * 渲染导航菜单
   * @param {Array} navItems - 导航菜单项
   */
  renderNavigation(navItems) {
    if (!navItems || !navItems.length) return;

    const navContainer = document.querySelector('.header-nav');
    if (!navContainer) return;

    navContainer.innerHTML = '';

    navItems.forEach(item => {
      const navLink = document.createElement('a');
      navLink.href = item.url;
      navLink.className = item.active
        ? 'flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all'
        : 'flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all';
      navLink.textContent = item.text;
      navContainer.appendChild(navLink);
    });
  }

  /**
   * 渲染侧边栏
   */
  renderSidebar() {
    if (!this.config || !this.config.sidebar) return;

    const sidebar = this.config.sidebar;
    const sidebarContainer = document.querySelector('.sidebar-container');
    if (!sidebarContainer) return;

    sidebarContainer.innerHTML = '';

    // 渲染侧边栏分类和菜单项
    sidebar.categories.forEach(category => {
      const categoryElement = this.createSidebarCategory(category);
      sidebarContainer.appendChild(categoryElement);
    });
  }

  /**
   * 创建侧边栏分类
   * @param {Object} category - 分类配置
   * @returns {HTMLElement} 分类元素
   */
  createSidebarCategory(category) {
    const categoryDiv = document.createElement('div');
    categoryDiv.className = 'mt-2 px-4 py-2';

    // 创建分类标题
    const titleDiv = document.createElement('div');
    titleDiv.className = 'flex items-center justify-between text-sm font-medium text-gray-700 mb-2';

    const titleSpan = document.createElement('span');
    titleSpan.textContent = category.title;
    titleDiv.appendChild(titleSpan);

    if (category.collapsible) {
      const collapseButton = document.createElement('button');
      collapseButton.className = 'w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all';
      collapseButton.innerHTML = '<i class="ri-arrow-down-s-line"></i>';
      collapseButton.addEventListener('click', () => {
        const itemsList = categoryDiv.querySelector('ul');
        if (itemsList) {
          itemsList.style.display = itemsList.style.display === 'none' ? 'block' : 'none';
          collapseButton.innerHTML = itemsList.style.display === 'none'
            ? '<i class="ri-arrow-right-s-line"></i>'
            : '<i class="ri-arrow-down-s-line"></i>';
        }
      });
      titleDiv.appendChild(collapseButton);
    }

    categoryDiv.appendChild(titleDiv);

    // 创建菜单项列表
    const itemsList = document.createElement('ul');
    itemsList.className = 'space-y-0.5';
    itemsList.style.display = category.expanded ? 'block' : 'none';

    // 添加菜单项
    category.items.forEach(item => {
      const listItem = this.createSidebarItem(item);
      itemsList.appendChild(listItem);
    });

    categoryDiv.appendChild(itemsList);
    return categoryDiv;
  }

  /**
   * 创建侧边栏菜单项
   * @param {Object} item - 菜单项配置
   * @returns {HTMLElement} 菜单项元素
   */
  createSidebarItem(item) {
    const listItem = document.createElement('li');
    listItem.setAttribute('data-sidebar-id', item.id);

    // 如果有可见性控制，检查是否应该显示
    if (item.visible && this.config.global_settings) {
      const shouldShow = this.config.global_settings[item.visible];
      if (shouldShow === false) {
        listItem.style.display = 'none';
      }
    }

    const itemLink = document.createElement('a');
    itemLink.href = item.url;
    itemLink.className = item.active
      ? 'flex items-center px-3 py-2.5 text-sm text-primary bg-blue-50 rounded-lg sidebar-menu-item active'
      : 'flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all';

    // 添加图标
    const iconSpan = document.createElement('span');
    iconSpan.className = 'w-6 h-6 flex items-center justify-center mr-3 ' +
      (item.active ? 'sidebar-icon' : 'text-gray-500 sidebar-icon');
    iconSpan.innerHTML = `<i class="${item.icon} text-lg"></i>`;
    itemLink.appendChild(iconSpan);

    // 添加文本
    const textSpan = document.createElement('span');
    textSpan.className = item.active ? 'font-medium' : '';
    textSpan.textContent = item.text;
    itemLink.appendChild(textSpan);

    // 添加徽章（如果有）
    if (item.badge) {
      const badgeSpan = document.createElement('span');
      badgeSpan.className = `ml-auto bg-${item.badge.color}-500 text-white text-xs px-1.5 py-0.5 rounded-full`;
      badgeSpan.textContent = item.badge.text;
      itemLink.appendChild(badgeSpan);
    }

    listItem.appendChild(itemLink);
    return listItem;
  }

  /**
   * 渲染主内容区
   */
  renderMainContent() {
    if (!this.config || !this.config.main_content) return;

    const mainContent = this.config.main_content;
    const mainContainer = document.querySelector('.main-content-container');
    if (!mainContainer) return;

    mainContainer.innerHTML = '';

    // 渲染各个内容区块
    mainContent.sections.forEach(section => {
      const sectionElement = this.createContentSection(section);
      mainContainer.appendChild(sectionElement);
    });
  }

  /**
   * 创建内容区块
   * @param {Object} section - 区块配置
   * @returns {HTMLElement} 区块元素
   */
  createContentSection(section) {
    // 根据区块类型创建不同的内容
    switch (section.type) {
      case 'info_card':
        return this.createInfoCard(section);
      case 'chart_row':
        return this.createChartRow(section);
      case 'account_row':
        return this.createAccountRow(section);
      case 'action_bar':
        return this.createActionBar(section);
      default:
        console.warn(`Unknown section type: ${section.type}`);
        return document.createElement('div');
    }
  }

  /**
   * 创建信息卡片
   * @param {Object} section - 区块配置
   * @returns {HTMLElement} 卡片元素
   */
  createInfoCard(section) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded shadow-sm mb-6';

    const content = document.createElement('div');
    content.className = 'p-6';

    // 创建标题和操作链接
    if (section.title || section.action_link) {
      const header = document.createElement('div');
      header.className = 'flex items-center justify-between mb-4';

      if (section.title) {
        const title = document.createElement('h2');
        title.className = 'text-lg font-medium text-gray-800';
        title.textContent = section.title;
        header.appendChild(title);
      }

      if (section.action_link) {
        const link = document.createElement('a');
        link.href = section.action_link.url || '#';
        link.className = 'text-primary text-sm flex items-center';

        const linkText = document.createElement('span');
        linkText.textContent = section.action_link.text || '';
        link.appendChild(linkText);

        const icon = document.createElement('i');
        icon.className = 'ri-arrow-right-s-line ml-1';
        link.appendChild(icon);

        header.appendChild(link);
      }

      content.appendChild(header);
    }

    // 创建描述
    if (section.description) {
      const description = document.createElement('p');
      description.className = 'text-gray-600 text-sm mb-4';
      description.textContent = section.description;
      content.appendChild(description);
    }

    // 创建列
    if (section.columns && section.columns.length) {
      const columnsContainer = document.createElement('div');
      columnsContainer.className = 'grid grid-cols-' + section.columns.length + ' gap-6';

      section.columns.forEach(column => {
        const columnElement = this.createInfoCardColumn(column);
        columnsContainer.appendChild(columnElement);
      });

      content.appendChild(columnsContainer);
    }

    // 创建按钮
    if (section.button) {
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'mt-4';

      const button = document.createElement('button');
      button.className = 'px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap';
      button.textContent = section.button.text || '';

      buttonContainer.appendChild(button);
      content.appendChild(buttonContainer);
    }

    card.appendChild(content);
    return card;
  }

  /**
   * 创建信息卡片列
   * @param {Object} column - 列配置
   * @returns {HTMLElement} 列元素
   */
  createInfoCardColumn(column) {
    const columnElement = document.createElement('div');
    columnElement.className = 'border border-gray-100 rounded p-4';

    // 创建标题
    if (column.title) {
      const header = document.createElement('div');
      header.className = 'flex items-center justify-between mb-3';

      const title = document.createElement('h3');
      title.className = 'text-base font-medium text-gray-700';
      title.textContent = column.title;
      header.appendChild(title);

      columnElement.appendChild(header);
    }

    // 创建描述
    if (column.description) {
      const description = document.createElement('p');
      description.className = 'text-gray-600 text-sm mb-3';
      description.textContent = column.description;
      columnElement.appendChild(description);
    }

    // 创建指南链接
    if (column.guide_link) {
      const link = document.createElement('a');
      link.href = column.guide_link.url || '#';
      link.className = 'text-primary text-sm flex items-center';

      const linkText = document.createElement('span');
      linkText.textContent = column.guide_link.text || '';
      link.appendChild(linkText);

      const icon = document.createElement('i');
      icon.className = 'ri-arrow-right-s-line ml-1';
      link.appendChild(icon);

      columnElement.appendChild(link);
    }

    // 创建按钮
    if (column.button) {
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'mt-4';

      const button = document.createElement('button');
      button.className = 'px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap';
      button.textContent = column.button.text || '';

      buttonContainer.appendChild(button);
      columnElement.appendChild(buttonContainer);
    }

    return columnElement;
  }

  /**
   * 创建图表行
   * @param {Object} section - 区块配置
   * @returns {HTMLElement} 图表行元素
   */
  createChartRow(section) {
    const row = document.createElement('div');
    row.className = 'grid grid-cols-' + section.columns.length + ' gap-6 mb-6';

    section.columns.forEach(column => {
      const chartCard = this.createChartCard(column);
      row.appendChild(chartCard);
    });

    // 延迟初始化图表，确保DOM已经渲染
    setTimeout(() => {
      section.columns.forEach(column => {
        this.initChart(column);
      });
    }, 100);

    return row;
  }

  /**
   * 创建图表卡片
   * @param {Object} chart - 图表配置
   * @returns {HTMLElement} 图表卡片元素
   */
  createChartCard(chart) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded shadow-sm hover:shadow-md transition-shadow duration-300';

    const content = document.createElement('div');
    content.className = 'p-6';

    // 创建标题和过滤器
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-4';

    const title = document.createElement('h2');
    title.className = 'text-lg font-medium text-gray-800';
    title.textContent = chart.title || '';
    header.appendChild(title);

    if (chart.filters && chart.filters.length) {
      const filtersContainer = document.createElement('div');
      filtersContainer.className = 'flex items-center space-x-2 ' + chart.id + '-buttons';

      chart.filters.forEach((filter, index) => {
        const button = document.createElement('button');
        button.setAttribute('data-range', filter.id);

        if (filter.default) {
          button.className = 'px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90';
        } else {
          button.className = 'px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200';
        }

        button.textContent = filter.text || '';

        // 添加点击事件
        button.addEventListener('click', () => {
          // 更新按钮样式
          filtersContainer.querySelectorAll('button').forEach(btn => {
            btn.className = 'px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200';
          });
          button.className = 'px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90';

          // 更新图表数据
          this.updateChart(chart, filter.id);
        });

        filtersContainer.appendChild(button);
      });

      header.appendChild(filtersContainer);
    }

    content.appendChild(header);

    // 创建图表容器
    const chartContainer = document.createElement('div');
    chartContainer.id = chart.chart_id;
    chartContainer.className = 'w-full h-' + (chart.height || '64');
    content.appendChild(chartContainer);

    card.appendChild(content);
    return card;
  }

  /**
   * 初始化图表
   * @param {Object} chart - 图表配置
   */
  initChart(chart) {
    if (!chart || !chart.chart_id) return;

    const chartElement = document.getElementById(chart.chart_id);
    if (!chartElement) return;

    // 检查是否已加载ApexCharts
    if (typeof ApexCharts === 'undefined') {
      // 如果没有加载ApexCharts，加载它
      this.loadApexCharts(() => {
        this.renderChart(chart, chartElement);
      });
    } else {
      // 如果已加载ApexCharts，直接渲染图表
      this.renderChart(chart, chartElement);
    }
  }

  /**
   * 加载ApexCharts
   * @param {Function} callback - 加载完成后的回调函数
   */
  loadApexCharts(callback) {
    // 检查是否已经有ApexCharts脚本
    if (document.querySelector('script[src*="apexcharts"]')) {
      if (callback) callback();
      return;
    }

    // 创建ApexCharts脚本
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js';
    script.onload = () => {
      if (callback) callback();
    };
    document.head.appendChild(script);

    // 创建ApexCharts样式
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.css';
    document.head.appendChild(link);
  }

  /**
   * 渲染图表
   * @param {Object} chart - 图表配置
   * @param {HTMLElement} element - 图表容器元素
   */
  renderChart(chart, element) {
    if (!chart || !element || typeof ApexCharts === 'undefined') return;

    let options = {};

    // 根据图表ID设置不同的配置
    if (chart.chart_id === 'revenueChart') {
      options = {
        series: [{
          name: '收入',
          data: [31, 40, 28, 51, 42, 109, 100]
        }],
        chart: {
          height: 250,
          type: 'area',
          toolbar: {
            show: false
          }
        },
        colors: [this.config.global_settings.primary_color || '#1677FF'],
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: 2
        },
        fill: {
          type: 'gradient',
          gradient: {
            shadeIntensity: 1,
            opacityFrom: 0.7,
            opacityTo: 0.3,
            stops: [0, 90, 100]
          }
        },
        xaxis: {
          categories: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        },
        tooltip: {
          x: {
            format: 'dd/MM/yy HH:mm'
          },
        },
      };
    } else if (chart.chart_id === 'orderChart') {
      options = {
        series: [{
          name: '订单数',
          data: [44, 55, 57, 56, 61, 58, 63]
        }],
        chart: {
          type: 'bar',
          height: 250,
          toolbar: {
            show: false
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            borderRadius: 4
          },
        },
        colors: [this.config.global_settings.primary_color || '#1677FF'],
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val + " 单"
            }
          }
        }
      };
    }

    // 创建图表
    const apexChart = new ApexCharts(element, options);
    apexChart.render();

    // 保存图表实例，以便后续更新
    if (!this.charts) this.charts = {};
    this.charts[chart.chart_id] = apexChart;
  }

  /**
   * 更新图表
   * @param {Object} chart - 图表配置
   * @param {string} range - 范围
   */
  updateChart(chart, range) {
    if (!chart || !chart.chart_id || !this.charts || !this.charts[chart.chart_id]) return;

    const apexChart = this.charts[chart.chart_id];

    // 根据图表ID和范围更新数据
    if (chart.chart_id === 'revenueChart') {
      let newData = [];
      let newCategories = [];

      if (range === 'week') {
        newData = [31, 40, 28, 51, 42, 109, 100];
        newCategories = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      } else if (range === 'month') {
        newData = [44, 55, 41, 67, 22, 43, 21, 49, 45, 35, 31, 27, 39, 46, 28, 38, 42, 50, 33, 29, 25, 36, 40, 51, 47, 32, 43, 39, 29, 35];
        newCategories = Array.from({length: 30}, (_, i) => (i + 1) + '日');
      } else if (range === 'year') {
        newData = [76, 85, 101, 98, 87, 105, 91, 114, 94, 86, 115, 35];
        newCategories = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      }

      apexChart.updateOptions({
        xaxis: {
          categories: newCategories
        }
      });

      apexChart.updateSeries([{
        name: '收入',
        data: newData
      }]);
    } else if (chart.chart_id === 'orderChart') {
      let newData = [];
      let newCategories = [];

      if (range === 'today') {
        newData = [44, 55, 57, 56, 61, 58, 63];
        newCategories = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'];
      } else if (range === 'yesterday') {
        newData = [35, 41, 36, 26, 45, 48, 52];
        newCategories = ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'];
      } else if (range === 'week') {
        newData = [31, 40, 28, 51, 42, 109, 100];
        newCategories = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      }

      apexChart.updateOptions({
        xaxis: {
          categories: newCategories
        }
      });

      apexChart.updateSeries([{
        name: '订单数',
        data: newData
      }]);
    }
  }

  /**
   * 创建账户行
   * @param {Object} section - 区块配置
   * @returns {HTMLElement} 账户行元素
   */
  createAccountRow(section) {
    const row = document.createElement('div');
    row.className = 'grid grid-cols-' + section.columns.length + ' gap-6 mb-6';

    section.columns.forEach(column => {
      const accountCard = this.createAccountCard(column);
      row.appendChild(accountCard);
    });

    return row;
  }

  /**
   * 创建账户卡片
   * @param {Object} account - 账户配置
   * @returns {HTMLElement} 账户卡片元素
   */
  createAccountCard(account) {
    const card = document.createElement('div');
    card.className = 'bg-white rounded shadow-sm';

    const content = document.createElement('div');
    content.className = 'p-6';

    // 创建标题和操作链接
    const header = document.createElement('div');
    header.className = 'flex items-center justify-between mb-6';

    const titleContainer = document.createElement('div');
    titleContainer.className = 'flex items-center';

    const title = document.createElement('h2');
    title.className = 'text-lg font-medium text-gray-800';
    title.textContent = account.title || '';
    titleContainer.appendChild(title);

    const infoIcon = document.createElement('div');
    infoIcon.className = 'w-5 h-5 flex items-center justify-center ml-1 text-gray-400';
    infoIcon.innerHTML = '<i class="ri-information-line"></i>';
    titleContainer.appendChild(infoIcon);

    header.appendChild(titleContainer);

    if (account.details_link) {
      const link = document.createElement('a');
      link.href = account.details_link.url || '#';
      link.className = 'text-primary text-sm flex items-center';

      const linkText = document.createElement('span');
      linkText.textContent = account.details_link.text || '';
      link.appendChild(linkText);

      const icon = document.createElement('i');
      icon.className = 'ri-arrow-right-s-line ml-1';
      link.appendChild(icon);

      header.appendChild(link);
    }

    content.appendChild(header);

    // 创建余额项
    if (account.balance_items && account.balance_items.length) {
      const balanceContainer = document.createElement('div');
      balanceContainer.className = 'grid grid-cols-' + account.balance_items.length + ' gap-4 mb-6';

      account.balance_items.forEach(item => {
        const balanceItem = document.createElement('div');
        balanceItem.className = 'text-center';

        const labelContainer = document.createElement('div');
        labelContainer.className = 'flex items-center justify-center mb-1';

        const label = document.createElement('span');
        label.className = 'text-gray-600 text-sm mr-1';
        label.textContent = item.label || '';
        labelContainer.appendChild(label);

        if (item.has_tooltip) {
          const tooltip = document.createElement('div');
          tooltip.className = 'w-4 h-4 flex items-center justify-center text-gray-400';
          tooltip.innerHTML = '<i class="ri-question-line"></i>';
          labelContainer.appendChild(tooltip);
        }

        balanceItem.appendChild(labelContainer);

        const value = document.createElement('div');
        value.className = 'text-lg font-medium text-gray-800';
        value.textContent = item.value || '0.00';  // 默认显示0.00而不是星号
        balanceItem.appendChild(value);

        balanceContainer.appendChild(balanceItem);
      });

      content.appendChild(balanceContainer);
    }

    // 创建按钮
    if (account.buttons && account.buttons.length) {
      const buttonsContainer = document.createElement('div');
      buttonsContainer.className = 'flex flex-wrap gap-2';

      account.buttons.forEach(buttonConfig => {
        const button = document.createElement('button');
        button.className = 'px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap';

        if (buttonConfig.disabled) {
          button.disabled = true;
          button.classList.add('opacity-50', 'cursor-not-allowed');
        }

        button.textContent = buttonConfig.text || '';
        buttonsContainer.appendChild(button);
      });

      content.appendChild(buttonsContainer);
    }

    card.appendChild(content);
    return card;
  }

  /**
   * 创建操作栏
   * @param {Object} section - 区块配置
   * @returns {HTMLElement} 操作栏元素
   */
  createActionBar(section) {
    const actionBar = document.createElement('div');
    actionBar.className = 'bg-white rounded shadow-sm';

    const content = document.createElement('div');
    content.className = 'p-6 flex items-center justify-between';

    // 创建左侧按钮
    const leftButtons = section.buttons.filter(button => button.position === 'left' || !button.position);
    leftButtons.forEach(buttonConfig => {
      const button = document.createElement('button');
      button.className = 'px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap';
      button.textContent = buttonConfig.text || '';
      content.appendChild(button);
    });

    // 创建右侧按钮
    const rightButtons = section.buttons.filter(button => button.position === 'right');
    if (rightButtons.length > 0) {
      const rightContainer = document.createElement('div');

      rightButtons.forEach(buttonConfig => {
        const button = document.createElement('button');
        button.className = 'px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap';
        button.textContent = buttonConfig.text || '';
        rightContainer.appendChild(button);
      });

      content.appendChild(rightContainer);
    }

    actionBar.appendChild(content);
    return actionBar;
  }

  /**
   * 渲染右侧栏
   */
  renderRightbar() {
    if (!this.config || !this.config.rightbar) return;

    const rightbar = this.config.rightbar;
    const rightbarContainer = document.querySelector('.rightbar-container');
    if (!rightbarContainer) return;

    // 控制右侧栏的显示/隐藏
    rightbarContainer.style.display = rightbar.enabled ? 'block' : 'none';
    if (!rightbar.enabled) return;

    // 设置日期
    const dateElement = document.querySelector('.rightbar-date');
    if (dateElement && rightbar.date) {
      dateElement.textContent = rightbar.date;
    }

    // 设置标题
    const titleElement = document.querySelector('.rightbar-title');
    if (titleElement && rightbar.title) {
      titleElement.textContent = rightbar.title;
    }

    // 设置"查看全部"链接
    const viewAllElement = document.querySelector('.rightbar-view-all');
    if (viewAllElement && rightbar.view_all) {
      viewAllElement.textContent = rightbar.view_all.text;
      viewAllElement.href = rightbar.view_all.url;
    }

    // 渲染消息列表
    this.renderMessages(rightbar.messages);
  }

  /**
   * 渲染消息列表
   * @param {Array} messages - 消息列表
   */
  renderMessages(messages) {
    if (!messages || !messages.length) return;

    const messagesContainer = document.querySelector('.rightbar-messages');
    if (!messagesContainer) return;

    messagesContainer.innerHTML = '';

    messages.forEach(message => {
      const messageElement = document.createElement('div');
      messageElement.className = `border-l-2 ${message.highlight ? 'border-primary' : 'border-gray-200'} pl-3 py-1`;

      const contentDiv = document.createElement('div');
      contentDiv.className = 'flex items-start mb-2';

      // 创建图标
      const iconDiv = document.createElement('div');
      iconDiv.className = `w-8 h-8 flex items-center justify-center ${
        message.highlight ? 'bg-blue-50 text-primary' : 'bg-gray-50 text-gray-500'
      } rounded-full mr-2 flex-shrink-0`;
      iconDiv.innerHTML = `<i class="${message.icon}"></i>`;
      contentDiv.appendChild(iconDiv);

      // 创建消息内容
      const textDiv = document.createElement('div');

      const titleH4 = document.createElement('h4');
      titleH4.className = 'text-sm font-medium text-gray-800 mb-1';
      titleH4.textContent = message.title;
      textDiv.appendChild(titleH4);

      const statsP = document.createElement('p');
      statsP.className = 'text-xs text-gray-500';
      statsP.textContent = message.stats;
      textDiv.appendChild(statsP);

      contentDiv.appendChild(textDiv);
      messageElement.appendChild(contentDiv);

      messagesContainer.appendChild(messageElement);
    });
  }

  /**
   * 初始化模板
   */
  async init() {
    try {
      await this.loadConfig();
      this.applyGlobalSettings();
      this.renderHeader();
      this.renderSidebar();
      this.renderMainContent();
      this.renderRightbar();
      console.log('Template initialized successfully');
    } catch (error) {
      console.error('Failed to initialize template:', error);
    }
  }
}

// 当DOM加载完成后初始化模板
document.addEventListener('DOMContentLoaded', () => {
  // 从页面获取模板名称
  const templateName = document.body.getAttribute('data-template') || 'alipay';
  const loader = new TemplateLoader(templateName);
  loader.init();
});
