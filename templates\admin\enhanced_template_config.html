<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版模板管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .template-preview {
            transition: all 0.2s ease;
        }
        .template-preview:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-admin-line text-blue-600 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-900">支付宝管理后台</span>
                    </div>
                    <nav class="ml-6 flex space-x-8">
                        <a href="{{ url_for('admin_dashboard') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            仪表盘
                        </a>
                        <a href="{{ url_for('admin_templates') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            模板管理
                        </a>
                        <a href="{{ url_for('admin_enhanced_templates') }}" class="border-blue-500 text-blue-600 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            模板自定义
                        </a>
                        <a href="{{ url_for('admin_credentials') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            凭证管理
                        </a>
                        <a href="{{ url_for('admin_content') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            内容管理
                        </a>
                    </nav>
                </div>
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="ri-eye-line mr-1"></i>
                        查看首页
                    </a>
                    <a href="{{ url_for('template_logout') }}" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="ri-logout-box-line mr-1"></i>
                        退出登录
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    增强版模板管理
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    选择、预览和自定义模板
                </p>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8">
                <button id="tab-templates" class="border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button">
                    模板选择
                </button>
                <button id="tab-customize" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button">
                    模板自定义
                </button>
                <button id="tab-statistics" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm tab-button">
                    使用统计
                </button>
            </nav>
        </div>

        <!-- 模板选择内容 -->
        <div id="content-templates" class="tab-content active">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
                {% for template in templates %}
                <div class="bg-white overflow-hidden shadow rounded-lg template-preview">
                    <div class="relative">
                        {% if 'yehuo' in template.template_name %}
                        <img src="https://via.placeholder.com/600x400/1e293b/ffffff?text=finviz数据提取后台" alt="finviz数据提取后台预览" class="w-full h-48 object-cover">
                        {% else %}
                        <img src="https://via.placeholder.com/600x400/f3f4f6/6b7280?text={{ template.template_name }}" alt="模板预览" class="w-full h-48 object-cover">
                        {% endif %}

                        {% if template.is_active %}
                        <div class="absolute top-2 right-2 px-2 py-1 bg-green-500 text-white text-xs font-semibold rounded-md shadow-sm">
                            当前使用
                        </div>
                        {% endif %}
                    </div>
                    <div class="p-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">
                                {% if 'yehuo' in template.template_name %}
                                    finviz数据提取后台
                                {% else %}
                                    {{ template.template_name }}
                                {% endif %}
                            </h3>
                            <div class="flex items-center">
                                <span class="text-xs text-gray-500">最后更新: {{ template.updated_at.strftime('%Y-%m-%d') }}</span>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">
                            文件名: {{ template.template_name }}
                        </p>
                        <div class="mt-4 flex items-center justify-between">
                            <form method="post" action="{{ url_for('admin_templates') }}" class="inline">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 {% if template.is_active %}opacity-50 cursor-not-allowed{% endif %}" {% if template.is_active %}disabled{% endif %}>
                                    {% if template.is_active %}
                                    <i class="ri-check-line mr-1"></i> 已激活
                                    {% else %}
                                    <i class="ri-arrow-right-line mr-1"></i> 设为首页
                                    {% endif %}
                                </button>
                            </form>
                            <div class="flex space-x-2">
                                <a href="{{ url_for('index') }}?preview={{ template.id }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="ri-eye-line mr-1"></i> 预览
                                </a>
                                <a href="{{ url_for('admin_friendly_config') }}?template_id={{ template.id }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <i class="ri-settings-line mr-1"></i> 设置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 模板自定义内容 -->
        <div id="content-customize" class="tab-content">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">模板自定义设置</h3>
                    <div class="mt-2 max-w-xl text-sm text-gray-500">
                        <p>自定义模板的外观和功能</p>
                    </div>

                    <div class="mt-5 border-t border-gray-200 pt-5">
                        <!-- 模板选择 -->
                        <div class="mb-6">
                            <label for="template-select" class="block text-sm font-medium text-gray-700 mb-2">选择要自定义的模板</label>
                            <select id="template-select" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                {% for template in templates %}
                                <option value="{{ template.id }}" {% if template.is_active %}selected{% endif %}>
                                    {% if 'yehuo' in template.template_name %}
                                        finviz数据提取后台
                                    {% else %}
                                        {{ template.template_name }}
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        {% for template in templates %}
                        <form method="post" action="{{ url_for('admin_enhanced_templates') }}" id="customize-form-{{ template.id }}" class="customize-form {% if not template.is_active %}hidden{% endif %}">
                            <input type="hidden" name="action" value="customize">
                            <input type="hidden" name="template_id" value="{{ template.id }}">

                            <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                <div class="sm:col-span-3">
                                    <label for="primary-color-{{ template.id }}" class="block text-sm font-medium text-gray-700">主题色</label>
                                    <div class="mt-1 flex items-center">
                                        <input type="color" id="primary-color-{{ template.id }}" name="primary_color"
                                            value="{{ customizations[template.id].primary_color if template.id in customizations else '#1677FF' }}"
                                            class="h-8 w-8 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 color-picker">
                                        <span class="ml-2 text-sm text-gray-500 color-value">{{ customizations[template.id].primary_color if template.id in customizations else '#1677FF' }}</span>
                                    </div>
                                </div>

                                <div class="sm:col-span-3">
                                    <label for="layout-style-{{ template.id }}" class="block text-sm font-medium text-gray-700">布局样式</label>
                                    <select id="layout-style-{{ template.id }}" name="layout_style" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                                        <option value="default" {% if template.id not in customizations or customizations[template.id].layout_style == 'default' %}selected{% endif %}>默认布局</option>
                                        <option value="compact" {% if template.id in customizations and customizations[template.id].layout_style == 'compact' %}selected{% endif %}>紧凑布局</option>
                                        <option value="wide" {% if template.id in customizations and customizations[template.id].layout_style == 'wide' %}selected{% endif %}>宽屏布局</option>
                                    </select>
                                </div>

                                <div class="sm:col-span-6">
                                    <fieldset>
                                        <legend class="text-sm font-medium text-gray-700">功能模块显示</legend>
                                        <div class="mt-2 space-y-4">
                                            <div class="flex items-center">
                                                <input id="show-shop-overview-{{ template.id }}" name="show_shop_overview" type="checkbox"
                                                    {% if template.id not in customizations or customizations[template.id].show_shop_overview %}checked{% endif %}
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="show-shop-overview-{{ template.id }}" class="ml-3 block text-sm font-medium text-gray-700">店铺概况</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="show-balance-{{ template.id }}" name="show_balance" type="checkbox"
                                                    {% if template.id not in customizations or customizations[template.id].show_balance %}checked{% endif %}
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="show-balance-{{ template.id }}" class="ml-3 block text-sm font-medium text-gray-700">余额</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="show-modules-{{ template.id }}" name="show_modules" type="checkbox"
                                                    {% if template.id not in customizations or customizations[template.id].show_modules %}checked{% endif %}
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="show-modules-{{ template.id }}" class="ml-3 block text-sm font-medium text-gray-700">模块</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="show-statistics-{{ template.id }}" name="show_statistics" type="checkbox"
                                                    {% if template.id not in customizations or customizations[template.id].show_statistics %}checked{% endif %}
                                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                                <label for="show-statistics-{{ template.id }}" class="ml-3 block text-sm font-medium text-gray-700">统计</label>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>

                                <div class="sm:col-span-6">
                                    <label for="custom-css-{{ template.id }}" class="block text-sm font-medium text-gray-700">自定义CSS</label>
                                    <div class="mt-1">
                                        <textarea id="custom-css-{{ template.id }}" name="custom_css" rows="5"
                                            class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                            placeholder="/* 在这里添加自定义CSS样式 */">{{ customizations[template.id].custom_css if template.id in customizations and customizations[template.id].custom_css else '' }}</textarea>
                                    </div>
                                    <p class="mt-2 text-sm text-gray-500">添加自定义CSS样式，将应用于模板的所有页面</p>
                                </div>
                            </div>

                            <div class="mt-5 flex justify-end">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    保存设置
                                </button>
                            </div>
                        </form>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用统计内容 -->
        <div id="content-statistics" class="tab-content">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">模板使用统计</h3>
                    <div class="mt-2 max-w-xl text-sm text-gray-500">
                        <p>查看各模板的使用情况和访问数据</p>
                    </div>

                    <div class="mt-5">
                        <div class="flex flex-col">
                            <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                                <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                                    <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                                        <table class="min-w-full divide-y divide-gray-200">
                                            <thead class="bg-gray-50">
                                                <tr>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模板名称</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用次数</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">访问量</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均停留时间</th>
                                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后使用时间</th>
                                                </tr>
                                            </thead>
                                            <tbody class="bg-white divide-y divide-gray-200">
                                                {% for template in templates %}
                                                <tr>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="flex items-center">
                                                            <div class="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-500">
                                                                <i class="ri-file-text-line text-lg"></i>
                                                            </div>
                                                            <div class="ml-4">
                                                                <div class="text-sm font-medium text-gray-900">
                                                                    {% if 'yehuo' in template.template_name %}
                                                                        finviz数据提取后台
                                                                    {% else %}
                                                                        {{ template.template_name }}
                                                                    {% endif %}
                                                                </div>
                                                                <div class="text-sm text-gray-500">{{ template.template_name }}</div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900">{{ template.usage_count or 0 }}</div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900">{{ template.view_count or 0 }}</div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap">
                                                        <div class="text-sm text-gray-900">{{ template.avg_duration or '0分0秒' }}</div>
                                                    </td>
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {{ template.updated_at.strftime('%Y-%m-%d %H:%M:%S') if template.updated_at else '未知' }}
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的激活状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-600');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });

                    // 激活当前标签页
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-blue-500', 'text-blue-600');

                    // 隐藏所有内容
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // 显示对应内容
                    const contentId = 'content-' + this.id.split('-')[1];
                    document.getElementById(contentId).classList.add('active');
                });
            });

            // 模板选择功能
            const templateSelect = document.getElementById('template-select');
            const customizeForms = document.querySelectorAll('.customize-form');

            if (templateSelect) {
                templateSelect.addEventListener('change', function() {
                    const selectedTemplateId = this.value;

                    // 隐藏所有表单
                    customizeForms.forEach(form => {
                        form.classList.add('hidden');
                    });

                    // 显示选中的模板表单
                    const selectedForm = document.getElementById('customize-form-' + selectedTemplateId);
                    if (selectedForm) {
                        selectedForm.classList.remove('hidden');
                    }
                });
            }

            // 颜色选择器功能
            const colorPickers = document.querySelectorAll('.color-picker');

            colorPickers.forEach(picker => {
                picker.addEventListener('input', function() {
                    // 更新颜色值显示
                    const colorValue = this.closest('.flex').querySelector('.color-value');
                    if (colorValue) {
                        colorValue.textContent = this.value.toUpperCase();
                    }
                });
            });

            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                .hidden {
                    display: none;
                }
                .customize-form {
                    transition: opacity 0.3s ease;
                }
                .customize-form:not(.hidden) {
                    animation: fadeIn 0.3s ease-in-out;
                }
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
