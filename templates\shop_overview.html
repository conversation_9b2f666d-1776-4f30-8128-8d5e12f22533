<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>店铺概况 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
            boxShadow: {
              card: "0 2px 8px rgba(0, 0, 0, 0.08)",
              dropdown: "0 4px 12px rgba(0, 0, 0, 0.1)",
              nav: "0 1px 4px rgba(0, 0, 0, 0.05)"
            },
            animation: {
              'fade-in': 'fadeIn 0.2s ease-in-out',
              'slide-down': 'slideDown 0.3s ease-out',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideDown: {
                '0%': { transform: 'translateY(-10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
      }

      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }

      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }

      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }

      .hover-card {
        transition: all 0.2s ease;
      }

      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a
              href="/"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >工作台</a
            >
            <a
              href="/financial_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >资金管理</a
            >
            <a
              href="/reconciliation_center"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >对账中心</a
            >
            <a
              href="/product_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >产品中心</a
            >
            <a
              href="/data_analysis"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >数据中心</a
            >
            <a
              href="/marketing_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >营销中心</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input
              type="text"
              placeholder="搜索功能/应用/服务"
              class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
            >
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button
            class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-notification-3-line text-xl"></i>
            <span
              class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"
            ></span>
          </button>
          <button
            class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div
              class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all"
            >
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      <aside class="w-56 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
        <div class="py-3">
          <div class="px-4 mb-4">
            <div class="relative w-full">
              <input
                type="text"
                placeholder="搜索菜单"
                class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
              />
              <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>

          <div class="px-4 py-2">
            <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
              <span>应用中心</span>
              <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                <i class="ri-arrow-down-s-line"></i>
              </button>
            </div>
            <ul class="space-y-0.5">
              <li>
                <a
                  href="/shop_overview"
                  class="flex items-center px-3 py-2.5 text-sm text-primary bg-blue-50 rounded-lg sidebar-menu-item active"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 sidebar-icon">
                    <i class="ri-store-line text-lg"></i>
                  </span>
                  <span class="font-medium">店铺概况</span>
                </a>
              </li>
              <li>
                <a
                  href="/balance"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-wallet-3-line text-lg"></i>
                  </span>
                  <span>余额</span>
                </a>
              </li>
              <li>
                <a
                  href="/modules"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-apps-line text-lg"></i>
                  </span>
                  <span>模块</span>
                </a>
              </li>
              <li>
                <a
                  href="/data_analysis"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-bar-chart-line text-lg"></i>
                  </span>
                  <span>统计</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">店铺概况</h1>
            <p class="mt-1 text-sm text-gray-500">查看您的店铺运营数据和业绩指标</p>
          </div>

          <!-- 数据概览卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">今日交易额</span>
                <i class="ri-information-line text-gray-400 cursor-pointer hover:text-primary"></i>
              </div>
              <div class="text-2xl font-semibold text-gray-800">¥12,345.67</div>
              <div class="mt-2 text-xs flex items-center">
                <span class="text-green-500 flex items-center">
                  <i class="ri-arrow-up-s-line mr-1"></i>
                  12.5%
                </span>
                <span class="text-gray-400 ml-2">较昨日</span>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">今日订单数</span>
                <i class="ri-information-line text-gray-400 cursor-pointer hover:text-primary"></i>
              </div>
              <div class="text-2xl font-semibold text-gray-800">156</div>
              <div class="mt-2 text-xs flex items-center">
                <span class="text-green-500 flex items-center">
                  <i class="ri-arrow-up-s-line mr-1"></i>
                  8.3%
                </span>
                <span class="text-gray-400 ml-2">较昨日</span>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">今日访客数</span>
                <i class="ri-information-line text-gray-400 cursor-pointer hover:text-primary"></i>
              </div>
              <div class="text-2xl font-semibold text-gray-800">2,345</div>
              <div class="mt-2 text-xs flex items-center">
                <span class="text-red-500 flex items-center">
                  <i class="ri-arrow-down-s-line mr-1"></i>
                  3.2%
                </span>
                <span class="text-gray-400 ml-2">较昨日</span>
              </div>
            </div>

            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">转化率</span>
                <i class="ri-information-line text-gray-400 cursor-pointer hover:text-primary"></i>
              </div>
              <div class="text-2xl font-semibold text-gray-800">6.65%</div>
              <div class="mt-2 text-xs flex items-center">
                <span class="text-green-500 flex items-center">
                  <i class="ri-arrow-up-s-line mr-1"></i>
                  0.8%
                </span>
                <span class="text-gray-400 ml-2">较昨日</span>
              </div>
            </div>
          </div>

          <!-- 销售趋势图表 -->
          <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-800">销售趋势</h2>
                <div class="flex items-center space-x-2 sales-chart-buttons">
                  <button class="px-3 py-1 text-sm bg-blue-50 text-primary rounded-md" data-range="today">今日</button>
                  <button class="px-3 py-1 text-sm text-gray-500 hover:bg-gray-50 rounded-md" data-range="week">本周</button>
                  <button class="px-3 py-1 text-sm text-gray-500 hover:bg-gray-50 rounded-md" data-range="month">本月</button>
                  <button class="px-3 py-1 text-sm text-gray-500 hover:bg-gray-50 rounded-md" data-range="year">本年</button>
                </div>
              </div>
            </div>
            <div class="p-4">
              <div id="sales-trend-chart" class="w-full h-80"></div>
            </div>
          </div>

          <!-- 热销商品 -->
          <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-4 border-b border-gray-100">
              <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-800">热销商品</h2>
                <a href="/product_management" class="text-primary text-sm flex items-center">
                  查看全部
                  <i class="ri-arrow-right-s-line ml-1"></i>
                </a>
              </div>
            </div>
            <div class="overflow-x-auto">
              <table class="w-full">
                <thead>
                  <tr class="bg-gray-50">
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商品</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">销量</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">销售额</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化率</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">高级商务休闲皮鞋</div>
                          <div class="text-sm text-gray-500">SKU: SP12345</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">234</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">¥23,400.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">8.7%</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">时尚女士手提包</div>
                          <div class="text-sm text-gray-500">SKU: SP23456</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">198</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">¥19,800.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">7.5%</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">智能手表</div>
                          <div class="text-sm text-gray-500">SKU: SP34567</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">156</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">¥15,600.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">6.2%</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">无线蓝牙耳机</div>
                          <div class="text-sm text-gray-500">SKU: SP45678</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">145</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">¥14,500.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">5.8%</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                  <tr>
                    <td class="px-4 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded-md"></div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">高端护肤套装</div>
                          <div class="text-sm text-gray-500">SKU: SP56789</div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">132</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">¥13,200.00</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700">5.3%</td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-primary">
                      <a href="#">查看详情</a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // 销售趋势图表数据
        const salesData = {
          today: {
            xAxis: ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
            sales: [1200, 800, 600, 1400, 2500, 3200, 2800, 3600, 4200, 3800, 2900, 1800],
            orders: [12, 8, 6, 14, 25, 32, 28, 36, 42, 38, 29, 18]
          },
          week: {
            xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            sales: [8500, 9200, 8700, 9500, 12000, 15000, 13500],
            orders: [85, 92, 87, 95, 120, 150, 135]
          },
          month: {
            xAxis: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
            sales: [25000, 28000, 32000, 30000, 35000, 40000, 38000],
            orders: [250, 280, 320, 300, 350, 400, 380]
          },
          year: {
            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            sales: [120000, 110000, 130000, 140000, 150000, 160000, 155000, 165000, 175000, 170000, 180000, 190000],
            orders: [1200, 1100, 1300, 1400, 1500, 1600, 1550, 1650, 1750, 1700, 1800, 1900]
          }
        };

        // 当前选中的时间范围
        let currentSalesRange = 'today';

        // 初始化销售趋势图表
        const salesChart = echarts.init(document.getElementById('sales-trend-chart'));

        function updateSalesChart(range) {
          currentSalesRange = range;
          const data = salesData[range];

          // 图表配置
          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            legend: {
              data: ['销售额', '订单数'],
              right: 10,
              top: 0
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: data.xAxis,
              axisLine: {
                lineStyle: {
                  color: '#E8EDF3'
                }
              },
              axisLabel: {
                color: '#8696A7'
              }
            },
            yAxis: [
              {
                type: 'value',
                name: '销售额',
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  color: '#8696A7',
                  formatter: '{value} 元'
                },
                splitLine: {
                  lineStyle: {
                    color: '#E8EDF3'
                  }
                }
              },
              {
                type: 'value',
                name: '订单数',
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                axisLabel: {
                  color: '#8696A7',
                  formatter: '{value} 笔'
                },
                splitLine: {
                  show: false
                }
              }
            ],
            series: [
              {
                name: '销售额',
                type: 'bar',
                barWidth: '60%',
                data: data.sales,
                itemStyle: {
                  color: '#1677FF'
                }
              },
              {
                name: '订单数',
                type: 'line',
                yAxisIndex: 1,
                smooth: true,
                data: data.orders,
                itemStyle: {
                  color: '#FF6A00'
                },
                lineStyle: {
                  color: '#FF6A00'
                }
              }
            ]
          };

          // 设置图表配置
          salesChart.setOption(option, true);
        }

        // 初始化图表
        updateSalesChart(currentSalesRange);

        // 添加图表点击事件
        salesChart.on('click', function(params) {
          if (params.seriesName === '销售额') {
            // 显示销售额详情
            const value = params.value;
            const date = params.name;

            // 创建一个弹出提示
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 animate-fade-in';
            toast.innerHTML = `
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-3">
                  <i class="ri-money-cny-circle-line"></i>
                </div>
                <div>
                  <h3 class="font-medium text-gray-800">销售额详情 - ${date}</h3>
                  <p class="text-sm text-gray-600">销售金额: ¥${value.toLocaleString()}</p>
                  <p class="text-xs text-gray-500 mt-1">点击查看销售明细</p>
                </div>
              </div>
            `;

            document.body.appendChild(toast);

            // 3秒后移除提示
            setTimeout(() => {
              toast.classList.add('animate-fade-out');
              setTimeout(() => {
                document.body.removeChild(toast);
              }, 300);
            }, 3000);
          } else if (params.seriesName === '订单数') {
            // 显示订单数详情
            const value = params.value;
            const date = params.name;

            // 创建一个弹出提示
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 animate-fade-in';
            toast.innerHTML = `
              <div class="flex items-center">
                <div class="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center text-orange-500 mr-3">
                  <i class="ri-file-list-line"></i>
                </div>
                <div>
                  <h3 class="font-medium text-gray-800">订单详情 - ${date}</h3>
                  <p class="text-sm text-gray-600">订单数量: ${value} 笔</p>
                  <p class="text-xs text-gray-500 mt-1">点击查看订单列表</p>
                </div>
              </div>
            `;

            document.body.appendChild(toast);

            // 3秒后移除提示
            setTimeout(() => {
              toast.classList.add('animate-fade-out');
              setTimeout(() => {
                document.body.removeChild(toast);
              }, 300);
            }, 3000);
          }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
          .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
          }
          .animate-fade-out {
            animation: fadeOut 0.3s ease-in-out;
          }
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
          }
        `;
        document.head.appendChild(style);

        // 绑定销售趋势图表的时间范围切换按钮
        const salesButtons = document.querySelectorAll('.sales-chart-buttons button');
        if (salesButtons.length > 0) {
          salesButtons.forEach(button => {
            button.addEventListener('click', function() {
              const range = this.getAttribute('data-range');
              if (range) {
                // 更新按钮样式
                salesButtons.forEach(btn => {
                  btn.classList.remove('bg-blue-50', 'text-primary');
                  btn.classList.add('text-gray-500', 'hover:bg-gray-50');
                });
                this.classList.remove('text-gray-500', 'hover:bg-gray-50');
                this.classList.add('bg-blue-50', 'text-primary');

                // 更新图表
                updateSalesChart(range);
              }
            });
          });
        }

        // 响应窗口大小变化
        window.addEventListener('resize', function() {
          salesChart.resize();
        });
      });
    </script>
  </body>
</html>
