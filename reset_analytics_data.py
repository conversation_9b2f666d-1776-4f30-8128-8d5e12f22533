#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重置数据分析数据
"""

from app import app, db, AnalyticsData, TransactionTrend, UserSource, ProductSales

def reset_analytics_data():
    """删除所有数据分析数据"""
    
    with app.app_context():
        # 删除所有商品销售数据
        ProductSales.query.delete()
        
        # 删除所有用户来源数据
        UserSource.query.delete()
        
        # 删除所有交易趋势数据
        TransactionTrend.query.delete()
        
        # 删除所有分析数据
        AnalyticsData.query.delete()
        
        # 提交更改
        db.session.commit()
        
        print("数据分析数据已重置")

if __name__ == '__main__':
    reset_analytics_data()
