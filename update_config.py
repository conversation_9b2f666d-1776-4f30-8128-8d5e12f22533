import json

# 读取配置文件
with open('static/config/alipay_template.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

# 添加资金管理和对账中心菜单项
product_service_items = config['sidebar']['categories'][1]['items']
product_service_items.append({
    "id": "financial_management",
    "text": "资金管理",
    "icon": "ri-bank-card-line",
    "url": "/financial_management",
    "active": False
})
product_service_items.append({
    "id": "reconciliation_center",
    "text": "对账中心",
    "icon": "ri-file-list-3-line",
    "url": "/reconciliation_center",
    "active": False
})

# 保存修改后的配置文件
with open('static/config/alipay_template.json', 'w', encoding='utf-8') as f:
    json.dump(config, f, ensure_ascii=False, indent=2)

print("配置文件已更新")
