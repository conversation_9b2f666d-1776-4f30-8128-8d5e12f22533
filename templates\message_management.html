<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台 - 消息管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      .submenu-item {
        display: none;
      }
      .submenu-item.show {
        display: flex;
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }
      .message-content {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .message-type-text { color: #10b981; }
      .message-type-image { color: #3b82f6; }
      .message-type-file { color: #f59e0b; }
      .message-type-voice { color: #8b5cf6; }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ contents.header_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ contents.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ contents.username|default('admin') }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}
      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>消息管理</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>消息列表</span>
          </div>
        </div>
        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                id="searchInput"
                placeholder="搜索用户名或消息内容"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <input
              type="date"
              id="dateFilter"
              value="2023-07"
              class="ml-4 px-3 py-2 border border-gray-200 rounded-button text-sm"
            />
            <select id="typeFilter" class="ml-2 px-3 py-2 border border-gray-200 rounded-button text-sm">
              <option value="">全部类型</option>
              <option value="text">文本</option>
              <option value="image">图片</option>
              <option value="file">文件</option>
              <option value="voice">语音</option>
            </select>
            <select id="sourceFilter" class="ml-2 px-3 py-2 border border-gray-200 rounded-button text-sm">
              <option value="">全部来源</option>
              <option value="private">私聊</option>
              <option value="group">群聊</option>
              <option value="channel">频道</option>
            </select>
            <button
              id="exportBtn"
              class="bg-green-500 text-white px-4 py-2 rounded-button flex items-center ml-4 hover:bg-green-600 transition-colors"
            >
              <i class="ri-download-line mr-2"></i>
              <span>导出</span>
            </button>
            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer"
              >
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium">序号</th>
                  <th class="font-medium">发送者</th>
                  <th class="font-medium">接收者</th>
                  <th class="font-medium">消息类型</th>
                  <th class="font-medium">消息内容</th>
                  <th class="font-medium">来源类型</th>
                  <th class="font-medium">消息来源</th>
                  <th class="font-medium">消息时间</th>
                  <th class="font-medium">内容</th>
                  <th class="font-medium">时间</th>
                  <th class="font-medium">操作</th>
                </tr>
              </thead>
              <tbody id="messageTableBody">
                <!-- 表格数据行将通过JavaScript动态生成 -->
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600" id="totalRecords">共 0 条</div>
            <div class="flex items-center" id="pagination">
              <!-- 分页将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script>
      // 设置当前页面标识
      window.currentPage = 'message_management';

      // 全局变量
      let currentPage = 1;
      let totalPages = 1;
      let messagesData = [];
      let filteredData = [];

      // DOM元素
      const searchInput = document.getElementById('searchInput');
      const dateFilter = document.getElementById('dateFilter');
      const typeFilter = document.getElementById('typeFilter');
      const sourceFilter = document.getElementById('sourceFilter');
      const exportBtn = document.getElementById('exportBtn');

      // 初始化页面
      document.addEventListener('DOMContentLoaded', function() {
        loadMessagesData();
        initEventListeners();
        // 菜单将由sidebar_menu.js自动初始化
      });

      // 初始化事件监听器
      function initEventListeners() {
        // 搜索和过滤功能
        searchInput.addEventListener('input', handleFilter);
        dateFilter.addEventListener('change', handleFilter);
        typeFilter.addEventListener('change', handleFilter);
        sourceFilter.addEventListener('change', handleFilter);

        // 导出功能
        exportBtn.addEventListener('click', () => {
          alert('导出消息功能');
        });
      }

      // 加载消息数据
      function loadMessagesData() {
        // 模拟数据
        messagesData = [
          {
            id: 1,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7721',
            sourceType: 'group',
            source: 'hello message 7721',
            messageTime: '2023-07-25 16:30',
            fullContent: 'hello message 7721',
            time: '2023-07-25 16:30'
          },
          {
            id: 2,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7720',
            sourceType: 'group',
            source: 'hello message 7720',
            messageTime: '2023-07-25 16:07',
            fullContent: 'hello message 7720',
            time: '2023-07-25 16:07'
          },
          {
            id: 3,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7719',
            sourceType: 'group',
            source: 'hello message 7719',
            messageTime: '2023-07-25 16:05',
            fullContent: 'hello message 7719',
            time: '2023-07-25 16:05'
          },
          {
            id: 4,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7718',
            sourceType: 'group',
            source: 'hello message 7718',
            messageTime: '2023-07-25 16:03',
            fullContent: 'hello message 7718',
            time: '2023-07-25 16:03'
          },
          {
            id: 5,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7717',
            sourceType: 'group',
            source: 'hello message 7717',
            messageTime: '2023-07-25 16:00',
            fullContent: 'hello message 7717',
            time: '2023-07-25 16:00'
          },
          {
            id: 6,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7716',
            sourceType: 'group',
            source: 'hello message 7716',
            messageTime: '2023-07-25 15:50',
            fullContent: 'hello message 7716',
            time: '2023-07-25 15:50'
          },
          {
            id: 7,
            sender: '群组',
            receiver: '文本',
            type: 'text',
            content: 'hello message 7715',
            sourceType: 'group',
            source: 'hello message 7715',
            messageTime: '2023-07-25 15:47',
            fullContent: 'hello message 7715',
            time: '2023-07-25 15:47'
          }
        ];
        filteredData = [...messagesData];
        renderTable();
        updatePagination();
      }

      // 渲染表格
      function renderTable() {
        const tbody = document.getElementById('messageTableBody');
        const startIndex = (currentPage - 1) * 10;
        const endIndex = startIndex + 10;
        const pageData = filteredData.slice(startIndex, endIndex);

        tbody.innerHTML = pageData.map((message, index) => `
          <tr class="border-b border-gray-200">
            <td>${startIndex + index + 1}</td>
            <td>${message.sender}</td>
            <td>${message.receiver}</td>
            <td>
              <span class="message-type-${message.type}">
                ${getTypeDisplayName(message.type)}
              </span>
            </td>
            <td>
              <div class="message-content" title="${message.content}">
                ${message.content}
              </div>
            </td>
            <td>${getSourceTypeDisplayName(message.sourceType)}</td>
            <td>
              <div class="message-content" title="${message.source}">
                ${message.source}
              </div>
            </td>
            <td>${message.messageTime}</td>
            <td>
              <div class="message-content" title="${message.fullContent}">
                ${message.fullContent}
              </div>
            </td>
            <td>${message.time}</td>
            <td>
              <button onclick="viewMessage(${message.id})" class="text-blue-500 hover:text-blue-700 mr-2">
                查看
              </button>
              <button onclick="viewUserMessages('${message.sender}')" class="text-green-500 hover:text-green-700">
                用户消息
              </button>
            </td>
          </tr>
        `).join('');

        // 更新总记录数
        document.getElementById('totalRecords').textContent = `共 ${filteredData.length} 条`;
      }

      // 更新分页
      function updatePagination() {
        totalPages = Math.ceil(filteredData.length / 10);
        const pagination = document.getElementById('pagination');

        if (totalPages <= 1) {
          pagination.innerHTML = '';
          return;
        }

        let paginationHTML = `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage > 1 ? 'changePage(' + (currentPage - 1) + ')' : ''}">
            <i class="ri-arrow-left-s-line"></i>
          </div>
        `;

        for (let i = 1; i <= totalPages; i++) {
          if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
              <div class="pagination-item mx-1 ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                ${i}
              </div>
            `;
          } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
        }

        paginationHTML += `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage < totalPages ? 'changePage(' + (currentPage + 1) + ')' : ''}">
            <i class="ri-arrow-right-s-line"></i>
          </div>
        `;

        pagination.innerHTML = paginationHTML;
      }

      // 切换页面
      function changePage(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
          currentPage = page;
          renderTable();
          updatePagination();
        }
      }

      // 搜索和过滤处理
      function handleFilter() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const dateValue = dateFilter.value;
        const typeValue = typeFilter.value;
        const sourceValue = sourceFilter.value;

        filteredData = messagesData.filter(message => {
          const matchesSearch = searchTerm === '' ||
            message.sender.toLowerCase().includes(searchTerm) ||
            message.content.toLowerCase().includes(searchTerm) ||
            message.source.toLowerCase().includes(searchTerm);

          const matchesDate = dateValue === '' || message.messageTime.includes(dateValue);
          const matchesType = typeValue === '' || message.type === typeValue;
          const matchesSource = sourceValue === '' || message.sourceType === sourceValue;

          return matchesSearch && matchesDate && matchesType && matchesSource;
        });

        currentPage = 1;
        renderTable();
        updatePagination();
      }

      // 获取类型显示名称
      function getTypeDisplayName(type) {
        const typeMap = {
          'text': '文本',
          'image': '图片',
          'file': '文件',
          'voice': '语音'
        };
        return typeMap[type] || type;
      }

      // 获取来源类型显示名称
      function getSourceTypeDisplayName(sourceType) {
        const sourceMap = {
          'private': '私聊',
          'group': '群聊',
          'channel': '频道'
        };
        return sourceMap[sourceType] || sourceType;
      }

      // 查看消息详情
      function viewMessage(messageId) {
        const message = messagesData.find(m => m.id === messageId);
        if (message) {
          alert(`消息详情:\n发送者: ${message.sender}\n内容: ${message.content}\n时间: ${message.messageTime}`);
        }
      }

      // 查看用户消息列表
      function viewUserMessages(userId) {
        // 跳转到用户消息列表页面
        window.location.href = `/user_messages/${userId}`;
      }
    </script>
  </body>
</html>
