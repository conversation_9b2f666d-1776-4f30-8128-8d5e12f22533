<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板配置 - 模板管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.8.2/dist/themes/classic.min.css">
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.8.2/dist/pickr.min.js"></script>
    <style>
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .color-picker-wrapper {
            position: relative;
            width: 100%;
        }
        .color-picker {
            width: 100%;
            height: 38px;
            border-radius: 0.375rem;
            border: 1px solid #e2e8f0;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    {% include 'admin/navbar.html' %}

    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-xl font-semibold text-gray-800">模板配置</h1>
                <p class="text-sm text-gray-600 mt-1">通过友好的界面配置模板的布局、内容和显示逻辑</p>
            </div>

            <div class="p-6">
                <div class="mb-6">
                    <label for="template-select" class="block text-sm font-medium text-gray-700 mb-2">选择要编辑的模板</label>
                    <div class="flex space-x-4">
                        <select id="template-select" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            {% for template in templates %}
                            <option value="{{ template.id }}" {% if selected_template and template.id == selected_template.id %}selected{% elif not selected_template and template.is_active %}selected{% endif %}>
                                {% if 'yehuo' in template.template_name %}
                                    finviz数据提取后台
                                {% else %}
                                    {{ template.template_name }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <button id="load-config" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-download-line mr-2"></i>
                            加载配置
                        </button>
                    </div>
                </div>

                <!-- 配置选项卡 -->
                <div class="mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button id="tab-global" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-indigo-500 font-medium text-sm text-indigo-600">
                                全局设置
                            </button>
                            <button id="tab-header" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                顶部导航栏
                            </button>
                            <button id="tab-sidebar" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                侧边栏菜单
                            </button>
                            <button id="tab-main" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                主内容区
                            </button>
                            <button id="tab-rightbar" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                右侧栏
                            </button>
                            <button id="tab-data" class="tab-button whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                数据
                            </button>
                        </nav>
                    </div>

                    <!-- 全局设置内容 -->
                    <div id="content-global" class="tab-content active mt-6">
                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <label for="primary-color" class="block text-sm font-medium text-gray-700">主题颜色</label>
                                <div class="mt-1 color-picker-wrapper">
                                    <div id="primary-color-picker" class="color-picker"></div>
                                    <input type="hidden" id="primary-color" name="primary_color" value="#1677FF">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="layout-style" class="block text-sm font-medium text-gray-700">布局样式</label>
                                <div class="mt-1">
                                    <select id="layout-style" name="layout_style" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                        <option value="default">默认布局</option>
                                        <option value="compact">紧凑布局</option>
                                        <option value="wide">宽屏布局</option>
                                    </select>
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <div class="flex flex-col space-y-4">
                                    <div class="flex items-center">
                                        <input id="show-shop-overview" name="show_shop_overview" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <label for="show-shop-overview" class="ml-2 block text-sm text-gray-700">显示店铺概况菜单</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="show-balance" name="show_balance" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <label for="show-balance" class="ml-2 block text-sm text-gray-700">显示余额菜单</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="show-modules" name="show_modules" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <label for="show-modules" class="ml-2 block text-sm text-gray-700">显示模块菜单</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input id="show-statistics" name="show_statistics" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <label for="show-statistics" class="ml-2 block text-sm text-gray-700">显示统计菜单</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 顶部导航栏内容 -->
                    <div id="content-header" class="tab-content mt-6">
                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <label for="header-title" class="block text-sm font-medium text-gray-700">标题</label>
                                <div class="mt-1">
                                    <input type="text" id="header-title" name="header_title" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="header-logo" class="block text-sm font-medium text-gray-700">Logo图标</label>
                                <div class="mt-1">
                                    <input type="text" id="header-logo" name="header_logo" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="search-placeholder" class="block text-sm font-medium text-gray-700">搜索框占位文字</label>
                                <div class="mt-1">
                                    <input type="text" id="search-placeholder" name="search_placeholder" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <div class="flex items-center h-full">
                                    <input id="search-enabled" name="search_enabled" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="search-enabled" class="ml-2 block text-sm text-gray-700">启用搜索框</label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900">导航菜单项</h3>
                            <div id="nav-items-container" class="mt-4 space-y-4">
                                <!-- 导航菜单项将在这里动态生成 -->
                            </div>
                            <button id="add-nav-item" type="button" class="mt-4 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-add-line mr-2"></i>
                                添加导航项
                            </button>
                        </div>
                    </div>

                    <!-- 侧边栏菜单内容 -->
                    <div id="content-sidebar" class="tab-content mt-6">
                        <div id="sidebar-categories-container" class="space-y-8">
                            <!-- 侧边栏分类将在这里动态生成 -->
                        </div>
                        <button id="add-sidebar-category" type="button" class="mt-6 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-add-line mr-2"></i>
                            添加分类
                        </button>
                    </div>

                    <!-- 主内容区内容 -->
                    <div id="content-main" class="tab-content mt-6">
                        <div id="main-sections-container" class="space-y-8">
                            <!-- 主内容区块将在这里动态生成 -->
                        </div>
                        <button id="add-main-section" type="button" class="mt-6 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-add-line mr-2"></i>
                            添加内容区块
                        </button>
                    </div>

                    <!-- 右侧栏内容 -->
                    <div id="content-rightbar" class="tab-content mt-6">
                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <div class="flex items-center">
                                    <input id="rightbar-enabled" name="rightbar_enabled" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                    <label for="rightbar-enabled" class="ml-2 block text-sm text-gray-700">启用右侧栏</label>
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="rightbar-date" class="block text-sm font-medium text-gray-700">日期</label>
                                <div class="mt-1">
                                    <input type="text" id="rightbar-date" name="rightbar_date" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="rightbar-title" class="block text-sm font-medium text-gray-700">标题</label>
                                <div class="mt-1">
                                    <input type="text" id="rightbar-title" name="rightbar_title" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <label for="rightbar-view-all" class="block text-sm font-medium text-gray-700">查看全部文字</label>
                                <div class="mt-1">
                                    <input type="text" id="rightbar-view-all" name="rightbar_view_all" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900">消息列表</h3>
                            <div id="messages-container" class="mt-4 space-y-4">
                                <!-- 消息项将在这里动态生成 -->
                            </div>
                            <button id="add-message" type="button" class="mt-4 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-add-line mr-2"></i>
                                添加消息
                            </button>
                        </div>
                    </div>

                    <!-- 数据标签页内容 -->
                    <div id="content-data" class="tab-content mt-6">
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">数据库表格</h3>
                            <div class="mb-4">
                                <label for="table-select" class="block text-sm font-medium text-gray-700 mb-2">选择表格</label>
                                <select id="table-select" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                    <option value="">请选择表格</option>
                                    <optgroup label="核心数据表">
                                        <option value="user">用户表</option>
                                        <option value="sensitive_words">敏感词表</option>
                                        <option value="sensitive_word_hits">敏感词命中记录</option>
                                        <option value="analytics_data">分析数据表</option>
                                    </optgroup>
                                    <optgroup label="示例数据表">
                                        <option value="user_sample_data">用户示例数据表</option>
                                        <option value="group_sample_data">群组示例数据表</option>
                                        <option value="message_sample_data">消息示例数据表</option>
                                        <option value="sensitive_word_sample_data">敏感词示例数据表</option>
                                        <option value="banned_user_sample_data">封禁用户示例数据表</option>
                                    </optgroup>
                                    <optgroup label="配置数据表">
                                        <option value="template_config">模板配置表</option>
                                        <option value="template_content">模板内容表</option>
                                        <option value="menu_configuration">菜单配置表</option>
                                        <option value="system_settings">系统设置表</option>
                                    </optgroup>
                                </select>
                            </div>
                            <button id="load-table-data" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-refresh-line mr-2"></i>
                                加载数据
                            </button>
                        </div>

                        <!-- 数据表格容器 -->
                        <div id="data-table-container" class="bg-white shadow overflow-hidden sm:rounded-md">
                            <div id="data-table-loading" class="hidden p-8 text-center">
                                <div class="inline-flex items-center">
                                    <i class="ri-loader-4-line animate-spin mr-2"></i>
                                    正在加载数据...
                                </div>
                            </div>
                            <div id="data-table-empty" class="p-8 text-center text-gray-500">
                                请选择表格并点击"加载数据"按钮
                            </div>
                            <div id="data-table-content" class="hidden">
                                <!-- 表格内容将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between">
                    <button id="preview-config" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="ri-eye-line mr-2"></i>
                        预览
                    </button>
                    <button id="save-config" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="ri-save-line mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification" class="fixed top-4 right-4 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg transform transition-transform duration-300 translate-x-full">
        <div class="p-4">
            <div class="flex items-start">
                <div id="notification-icon" class="flex-shrink-0">
                    <i class="ri-check-line text-green-500 text-xl"></i>
                </div>
                <div class="ml-3 w-0 flex-1">
                    <p id="notification-title" class="text-sm font-medium text-gray-900">操作成功</p>
                    <p id="notification-message" class="mt-1 text-sm text-gray-500">配置已成功保存。</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button id="close-notification" class="inline-flex text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据编辑模态框 -->
    <div id="edit-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 id="edit-modal-title" class="text-lg font-medium text-gray-900">编辑数据</h3>
                    <button id="close-edit-modal" class="text-gray-400 hover:text-gray-600">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
                <form id="edit-form" class="space-y-4">
                    <div id="edit-form-fields" class="space-y-4">
                        <!-- 表单字段将在这里动态生成 -->
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="cancel-edit" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            取消
                        </button>
                        <button type="submit" id="save-edit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 模板 -->
    <template id="nav-item-template">
        <div class="nav-item border border-gray-200 rounded-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-sm font-medium text-gray-700">导航项 #<span class="nav-item-index"></span></h4>
                <button type="button" class="remove-nav-item text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">文字</label>
                    <input type="text" class="nav-item-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">链接</label>
                    <input type="text" class="nav-item-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <div class="flex items-center">
                        <input type="checkbox" class="nav-item-active h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label class="ml-2 block text-xs text-gray-700">激活状态</label>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="sidebar-category-template">
        <div class="sidebar-category border border-gray-200 rounded-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-sm font-medium text-gray-700">侧边栏分类 #<span class="sidebar-category-index"></span></h4>
                <button type="button" class="remove-sidebar-category text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">标题</label>
                    <input type="text" class="sidebar-category-title mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">ID</label>
                    <input type="text" class="sidebar-category-id mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <input type="checkbox" class="sidebar-category-collapsible h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">可折叠</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="sidebar-category-expanded h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">默认展开</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <h5 class="text-xs font-medium text-gray-700 mb-2">菜单项</h5>
                <div class="sidebar-items-container space-y-4">
                    <!-- 菜单项将在这里动态生成 -->
                </div>
                <button type="button" class="add-sidebar-item mt-2 inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="ri-add-line mr-1"></i>
                    添加菜单项
                </button>
            </div>
        </div>
    </template>

    <template id="sidebar-item-template">
        <div class="sidebar-item border border-gray-200 rounded-md p-3">
            <div class="flex justify-between items-center mb-3">
                <h6 class="text-xs font-medium text-gray-700">菜单项 #<span class="sidebar-item-index"></span></h6>
                <button type="button" class="remove-sidebar-item text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">文字</label>
                    <input type="text" class="sidebar-item-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">图标</label>
                    <input type="text" class="sidebar-item-icon mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">链接</label>
                    <input type="text" class="sidebar-item-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">ID</label>
                    <input type="text" class="sidebar-item-id mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <input type="checkbox" class="sidebar-item-active h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">激活状态</label>
                        </div>
                    </div>
                </div>
                <div class="sm:col-span-2 border-t border-gray-200 pt-3">
                    <div class="flex items-center justify-between">
                        <label class="block text-xs font-medium text-gray-700">徽章</label>
                        <div class="flex items-center">
                            <input type="checkbox" class="sidebar-item-has-badge h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">显示徽章</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2 mt-2 sidebar-item-badge-container" style="display: none;">
                        <div>
                            <label class="block text-xs font-medium text-gray-700">徽章文字</label>
                            <input type="text" class="sidebar-item-badge-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700">徽章颜色</label>
                            <select class="sidebar-item-badge-color mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="red">红色</option>
                                <option value="blue">蓝色</option>
                                <option value="green">绿色</option>
                                <option value="yellow">黄色</option>
                                <option value="purple">紫色</option>
                                <option value="gray">灰色</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <template id="message-template">
        <div class="message-item border border-gray-200 rounded-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-sm font-medium text-gray-700">消息 #<span class="message-index"></span></h4>
                <button type="button" class="remove-message text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">标题</label>
                    <input type="text" class="message-title mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">图标</label>
                    <input type="text" class="message-icon mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">统计信息</label>
                    <input type="text" class="message-stats mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-1">
                    <div class="flex items-center h-full">
                        <input type="checkbox" class="message-highlight h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label class="ml-2 block text-xs text-gray-700">高亮显示</label>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 主内容区模板 -->
    <template id="main-section-template">
        <div class="main-section border border-gray-200 rounded-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-sm font-medium text-gray-700">内容区块 #<span class="main-section-index"></span></h4>
                <button type="button" class="remove-main-section text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">类型</label>
                    <select class="main-section-type mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="info_card">信息卡片</option>
                        <option value="chart_row">图表行</option>
                        <option value="account_row">账户行</option>
                        <option value="action_bar">操作栏</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">ID</label>
                    <input type="text" class="main-section-id mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <label class="block text-xs font-medium text-gray-700">标题</label>
                    <input type="text" class="main-section-title mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <label class="block text-xs font-medium text-gray-700">描述</label>
                    <textarea class="main-section-description mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                </div>

                <!-- 操作链接 -->
                <div class="sm:col-span-2 border-t border-gray-200 pt-4 mt-2">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-xs font-medium text-gray-700">操作链接</label>
                        <div class="flex items-center">
                            <input type="checkbox" class="main-section-has-action-link h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">显示操作链接</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2 mt-2 main-section-action-link-container" style="display: none;">
                        <div>
                            <label class="block text-xs font-medium text-gray-700">链接文字</label>
                            <input type="text" class="main-section-action-link-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700">链接URL</label>
                            <input type="text" class="main-section-action-link-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>

                <!-- 按钮 -->
                <div class="sm:col-span-2 border-t border-gray-200 pt-4 mt-2">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-xs font-medium text-gray-700">按钮</label>
                        <div class="flex items-center">
                            <input type="checkbox" class="main-section-has-button h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">显示按钮</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2 mt-2 main-section-button-container" style="display: none;">
                        <div>
                            <label class="block text-xs font-medium text-gray-700">按钮文字</label>
                            <input type="text" class="main-section-button-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700">按钮URL</label>
                            <input type="text" class="main-section-button-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>

                <!-- 列 -->
                <div class="sm:col-span-2 border-t border-gray-200 pt-4 mt-2">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-xs font-medium text-gray-700">列</label>
                        <button type="button" class="add-main-section-column inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-add-line mr-1"></i>
                            添加列
                        </button>
                    </div>
                    <div class="main-section-columns-container space-y-4">
                        <!-- 列将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 主内容区列模板 -->
    <template id="main-section-column-template">
        <div class="main-section-column border border-gray-200 rounded-md p-3">
            <div class="flex justify-between items-center mb-3">
                <h6 class="text-xs font-medium text-gray-700">列 #<span class="main-section-column-index"></span></h6>
                <button type="button" class="remove-main-section-column text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">标题</label>
                    <input type="text" class="main-section-column-title mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">ID</label>
                    <input type="text" class="main-section-column-id mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <label class="block text-xs font-medium text-gray-700">描述</label>
                    <textarea class="main-section-column-description mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"></textarea>
                </div>

                <!-- 图表设置 -->
                <div class="sm:col-span-2 main-section-column-chart-settings" style="display: none;">
                    <div class="border-t border-gray-200 pt-3 mt-2">
                        <label class="block text-xs font-medium text-gray-700 mb-2">图表ID</label>
                        <input type="text" class="main-section-column-chart-id mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div class="mt-2">
                        <label class="block text-xs font-medium text-gray-700 mb-2">图表高度</label>
                        <input type="text" class="main-section-column-chart-height mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="例如：64">
                    </div>
                </div>

                <!-- 余额项设置 -->
                <div class="sm:col-span-2 main-section-column-balance-settings" style="display: none;">
                    <div class="border-t border-gray-200 pt-3 mt-2">
                        <div class="flex items-center justify-between mb-2">
                            <label class="block text-xs font-medium text-gray-700">余额项</label>
                            <button type="button" class="add-balance-item inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-add-line mr-1"></i>
                                添加余额项
                            </button>
                        </div>
                        <div class="balance-items-container space-y-4">
                            <!-- 余额项将在这里动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 指南链接 -->
                <div class="sm:col-span-2 border-t border-gray-200 pt-3 mt-2">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-xs font-medium text-gray-700">指南链接</label>
                        <div class="flex items-center">
                            <input type="checkbox" class="main-section-column-has-guide-link h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">显示指南链接</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2 mt-2 main-section-column-guide-link-container" style="display: none;">
                        <div>
                            <label class="block text-xs font-medium text-gray-700">链接文字</label>
                            <input type="text" class="main-section-column-guide-link-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700">链接URL</label>
                            <input type="text" class="main-section-column-guide-link-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>

                <!-- 按钮 -->
                <div class="sm:col-span-2 border-t border-gray-200 pt-3 mt-2">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-xs font-medium text-gray-700">按钮</label>
                        <div class="flex items-center">
                            <input type="checkbox" class="main-section-column-has-button h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 block text-xs text-gray-700">显示按钮</label>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2 mt-2 main-section-column-button-container" style="display: none;">
                        <div>
                            <label class="block text-xs font-medium text-gray-700">按钮文字</label>
                            <input type="text" class="main-section-column-button-text mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-700">按钮URL</label>
                            <input type="text" class="main-section-column-button-url mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 余额项模板 -->
    <template id="balance-item-template">
        <div class="balance-item border border-gray-200 rounded-md p-3">
            <div class="flex justify-between items-center mb-3">
                <h6 class="text-xs font-medium text-gray-700">余额项 #<span class="balance-item-index"></span></h6>
                <button type="button" class="remove-balance-item text-red-600 hover:text-red-800">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
            <div class="grid grid-cols-1 gap-y-3 gap-x-3 sm:grid-cols-2">
                <div>
                    <label class="block text-xs font-medium text-gray-700">标签</label>
                    <input type="text" class="balance-item-label mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700">值</label>
                    <input type="text" class="balance-item-value mt-1 shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                </div>
                <div class="sm:col-span-2">
                    <div class="flex items-center">
                        <input type="checkbox" class="balance-item-has-tooltip h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                        <label class="ml-2 block text-xs text-gray-700">显示提示图标</label>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <script src="{{ smart_static('js/friendly_config.js') }}"></script>
</body>
</html>
