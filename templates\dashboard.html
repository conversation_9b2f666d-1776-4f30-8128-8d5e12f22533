<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{{ system_settings.labels.system_title|default('finviz数据提取后台') }} - 首页</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6",
              success: "#10b981",
              warning: "#f59e0b",
              danger: "#ef4444"
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <style>
      .pagination-item {
        @apply w-8 h-8 flex items-center justify-center text-sm cursor-pointer rounded transition-all;
      }
      .pagination-item:hover {
        @apply bg-gray-100;
      }
      .pagination-item.active {
        @apply bg-primary text-white;
      }
      .sidebar-menu-item.active {
        @apply bg-primary text-white;
      }
      .sidebar-menu-item.active i {
        @apply text-white;
      }
      .submenu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
      }
      .submenu.show {
        max-height: 500px;
      }
      .menu-arrow {
        transition: transform 0.3s ease;
      }
      .menu-arrow.rotated {
        transform: rotate(90deg);
      }
      .stat-card {
        @apply bg-white rounded-lg shadow-sm p-6 border border-gray-100;
      }
      .stat-card:hover {
        @apply shadow-md;
        transform: translateY(-2px);
        transition: all 0.3s ease;
      }
      .quick-action-card {
        @apply bg-white rounded-lg shadow-sm p-4 border border-gray-100 cursor-pointer;
      }
      .quick-action-card:hover {
        @apply shadow-md bg-gray-50;
        transform: translateY(-2px);
        transition: all 0.3s ease;
      }
      .activity-item {
        @apply flex items-center p-3 border-b border-gray-100 last:border-b-0;
      }
      .activity-item:hover {
        @apply bg-gray-50;
      }
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10">
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-gamepad-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ system_settings.labels.system_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ system_settings.labels.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ system_settings.labels.username_display|default('admin') }}</span>
          <div class="w-6 h-6 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <i class="ri-user-line text-sm"></i>
          </div>
        </div>
      </div>
    </header>

    <div class="flex flex-1">
      <!-- 侧边栏 -->
      <aside class="w-64 bg-white shadow-sm">
        <div class="p-4">
          <div class="space-y-1">
            <!-- 首页 -->
            <div class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer active">
              <i class="ri-home-line mr-3"></i>
              <span>首页</span>
            </div>

            <!-- 用户管理 -->
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 cursor-pointer" data-target="user-menu">
              <i class="ri-user-line mr-3"></i>
              <span>用户管理</span>
              <i class="ri-arrow-right-s-line ml-auto menu-arrow"></i>
            </div>
            <div id="user-menu" class="submenu">
              <a href="/user_list" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>用户列表</span>
              </a>
              <a href="/banned_users" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>封禁用户列表</span>
              </a>
            </div>

            <!-- 敏感词管理 -->
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 cursor-pointer" data-target="sensitive-menu">
              <i class="ri-spam-2-line mr-3"></i>
              <span>敏感词管理</span>
              <i class="ri-arrow-right-s-line ml-auto menu-arrow"></i>
            </div>
            <div id="sensitive-menu" class="submenu">
              <a href="/sensitive_words" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>敏感词列表</span>
              </a>
              <a href="/sensitive_word_hits" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>敏感词命中</span>
              </a>
            </div>

            <!-- 群组管理 -->
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 cursor-pointer" data-target="group-menu">
              <i class="ri-group-line mr-3"></i>
              <span>群组管理</span>
              <i class="ri-arrow-right-s-line ml-auto menu-arrow"></i>
            </div>
            <div id="group-menu" class="submenu">
              <a href="/group_management" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>群组列表</span>
              </a>
            </div>

            <!-- 消息管理 -->
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 cursor-pointer" data-target="message-menu">
              <i class="ri-message-2-line mr-3"></i>
              <span>消息管理</span>
              <i class="ri-arrow-right-s-line ml-auto menu-arrow"></i>
            </div>
            <div id="message-menu" class="submenu">
              <a href="/user_messages" class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>用户消息</span>
              </a>
            </div>

            <!-- 系统设置 -->
            <div class="menu-header px-4 py-2 flex items-center text-gray-700 cursor-pointer" data-target="settings-menu">
              <i class="ri-settings-line mr-3"></i>
              <span>系统设置</span>
              <i class="ri-arrow-right-s-line ml-auto menu-arrow"></i>
            </div>
            <div id="settings-menu" class="submenu">
              <div class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>基本设置</span>
              </div>
              <div class="sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
                <span>安全设置</span>
              </div>
            </div>
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 欢迎区域 -->
        <div class="mb-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-2">欢迎回来！</h2>
          <p class="text-gray-600">今天是 <span id="currentDate"></span>，祝您工作愉快</p>
        </div>

        <!-- 数据概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <!-- 总用户数 -->
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">总用户数</p>
                <p class="text-2xl font-bold text-gray-900" id="totalUsers">-</p>
                <p class="text-xs text-success">
                  <i class="ri-arrow-up-line"></i>
                  <span id="userGrowth">+0%</span> 较昨日
                </p>
              </div>
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="ri-user-line text-xl text-blue-600"></i>
              </div>
            </div>
          </div>

          <!-- 活跃用户 -->
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">今日活跃</p>
                <p class="text-2xl font-bold text-gray-900" id="activeUsers">-</p>
                <p class="text-xs text-success">
                  <i class="ri-arrow-up-line"></i>
                  <span id="activeGrowth">+0%</span> 较昨日
                </p>
              </div>
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="ri-user-star-line text-xl text-green-600"></i>
              </div>
            </div>
          </div>

          <!-- 群组数量 -->
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">群组数量</p>
                <p class="text-2xl font-bold text-gray-900" id="totalGroups">-</p>
                <p class="text-xs text-warning">
                  <i class="ri-arrow-up-line"></i>
                  <span id="groupGrowth">+0%</span> 较昨日
                </p>
              </div>
              <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="ri-group-line text-xl text-yellow-600"></i>
              </div>
            </div>
          </div>

          <!-- 今日消息 -->
          <div class="stat-card">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm font-medium text-gray-600">今日消息</p>
                <p class="text-2xl font-bold text-gray-900" id="todayMessages">-</p>
                <p class="text-xs text-danger">
                  <i class="ri-arrow-down-line"></i>
                  <span id="messageGrowth">-0%</span> 较昨日
                </p>
              </div>
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="ri-message-2-line text-xl text-red-600"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作和最新动态 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <!-- 快速操作 -->
          <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/user_list" class="quick-action-card text-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <i class="ri-user-line text-xl text-blue-600"></i>
                  </div>
                  <p class="text-sm font-medium text-gray-900">用户管理</p>
                </a>

                <a href="/sensitive_words" class="quick-action-card text-center">
                  <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <i class="ri-spam-2-line text-xl text-red-600"></i>
                  </div>
                  <p class="text-sm font-medium text-gray-900">敏感词</p>
                </a>

                <a href="/group_management" class="quick-action-card text-center">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <i class="ri-group-line text-xl text-green-600"></i>
                  </div>
                  <p class="text-sm font-medium text-gray-900">群组管理</p>
                </a>

                <a href="/user_messages" class="quick-action-card text-center">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <i class="ri-message-2-line text-xl text-purple-600"></i>
                  </div>
                  <p class="text-sm font-medium text-gray-900">消息管理</p>
                </a>
              </div>
            </div>
          </div>

          <!-- 最新动态 -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">最新动态</h3>
            <div id="recentActivities">
              <!-- 动态内容将通过JavaScript加载 -->
              <div class="text-center text-gray-500 py-8">
                <i class="ri-loader-4-line text-2xl animate-spin"></i>
                <p class="mt-2">加载中...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 用户增长趋势 -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">用户增长趋势</h3>
            <canvas id="userGrowthChart" width="400" height="200"></canvas>
          </div>

          <!-- 消息活跃度 -->
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">消息活跃度</h3>
            <canvas id="messageActivityChart" width="400" height="200"></canvas>
          </div>
        </div>
      </main>
    </div>

    <script>
      // 全局变量
      let userGrowthChart = null;
      let messageActivityChart = null;

      document.addEventListener("DOMContentLoaded", function () {
        // 初始化页面
        initializePage();

        // 菜单折叠/展开功能
        const menuHeaders = document.querySelectorAll('.menu-header');
        menuHeaders.forEach(header => {
          header.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const submenu = document.getElementById(targetId);
            const arrow = this.querySelector('.menu-arrow');

            if (submenu) {
              submenu.classList.toggle('show');
              arrow.classList.toggle('rotated');
            }
          });
        });

        // 侧边栏菜单项点击事件
        const sidebarMenuItems = document.querySelectorAll(".sidebar-menu-item");
        sidebarMenuItems.forEach((item) => {
          item.addEventListener("click", function () {
            // 移除所有项目的活动状态
            sidebarMenuItems.forEach((i) => i.classList.remove("active"));
            // 为当前点击项添加活动状态
            this.classList.add("active");
          });
        });
      });

      // 初始化页面
      function initializePage() {
        // 设置当前日期
        setCurrentDate();

        // 加载统计数据
        loadStatistics();

        // 加载最新动态
        loadRecentActivities();

        // 初始化图表
        initializeCharts();
      }

      // 设置当前日期
      function setCurrentDate() {
        const now = new Date();
        const options = {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          weekday: 'long'
        };
        document.getElementById('currentDate').textContent = now.toLocaleDateString('zh-CN', options);
      }

      // 加载统计数据
      async function loadStatistics() {
        try {
          const response = await fetch('/api/dashboard/statistics');
          if (response.ok) {
            const data = await response.json();
            updateStatistics(data);
          } else {
            // 如果API失败，使用模拟数据
            const mockData = {
              totalUsers: 1256,
              userGrowth: 12.5,
              activeUsers: 342,
              activeGrowth: 8.3,
              totalGroups: 89,
              groupGrowth: 5.2,
              todayMessages: 2847,
              messageGrowth: -3.1
            };
            updateStatistics(mockData);
          }
        } catch (error) {
          console.error('Error loading statistics:', error);
          // 使用模拟数据
          const mockData = {
            totalUsers: 1256,
            userGrowth: 12.5,
            activeUsers: 342,
            activeGrowth: 8.3,
            totalGroups: 89,
            groupGrowth: 5.2,
            todayMessages: 2847,
            messageGrowth: -3.1
          };
          updateStatistics(mockData);
        }
      }

      // 更新统计数据显示
      function updateStatistics(data) {
        // 总用户数
        document.getElementById('totalUsers').textContent = data.totalUsers.toLocaleString();
        document.getElementById('userGrowth').textContent = `+${data.userGrowth}%`;

        // 活跃用户
        document.getElementById('activeUsers').textContent = data.activeUsers.toLocaleString();
        document.getElementById('activeGrowth').textContent = `+${data.activeGrowth}%`;

        // 群组数量
        document.getElementById('totalGroups').textContent = data.totalGroups.toLocaleString();
        document.getElementById('groupGrowth').textContent = `+${data.groupGrowth}%`;

        // 今日消息
        document.getElementById('todayMessages').textContent = data.todayMessages.toLocaleString();
        const messageGrowthElement = document.getElementById('messageGrowth');
        if (data.messageGrowth < 0) {
          messageGrowthElement.textContent = `${data.messageGrowth}%`;
          messageGrowthElement.parentElement.className = 'text-xs text-danger';
          messageGrowthElement.parentElement.querySelector('i').className = 'ri-arrow-down-line';
        } else {
          messageGrowthElement.textContent = `+${data.messageGrowth}%`;
        }
      }

      // 加载最新动态
      async function loadRecentActivities() {
        try {
          const response = await fetch('/api/dashboard/activities');
          if (response.ok) {
            const activities = await response.json();
            displayActivities(activities);
          } else {
            // 使用模拟数据
            const mockActivities = [
              {
                type: 'user_register',
                message: '新用户 "玩家001" 注册',
                time: '2分钟前',
                icon: 'ri-user-add-line',
                color: 'text-green-600'
              },
              {
                type: 'sensitive_word',
                message: '检测到敏感词 "垃圾" 命中',
                time: '5分钟前',
                icon: 'ri-spam-2-line',
                color: 'text-red-600'
              },
              {
                type: 'group_create',
                message: '创建新群组 "竞技交流群"',
                time: '10分钟前',
                icon: 'ri-group-line',
                color: 'text-blue-600'
              },
              {
                type: 'message',
                message: '群组消息活跃度上升',
                time: '15分钟前',
                icon: 'ri-message-2-line',
                color: 'text-purple-600'
              },
              {
                type: 'system',
                message: '系统自动备份完成',
                time: '30分钟前',
                icon: 'ri-shield-check-line',
                color: 'text-gray-600'
              }
            ];
            displayActivities(mockActivities);
          }
        } catch (error) {
          console.error('Error loading activities:', error);
          displayActivities([]);
        }
      }

      // 显示最新动态
      function displayActivities(activities) {
        const container = document.getElementById('recentActivities');

        if (activities.length === 0) {
          container.innerHTML = `
            <div class="text-center text-gray-500 py-8">
              <i class="ri-inbox-line text-2xl"></i>
              <p class="mt-2">暂无动态</p>
            </div>
          `;
          return;
        }

        container.innerHTML = activities.map(activity => `
          <div class="activity-item">
            <div class="w-8 h-8 flex items-center justify-center mr-3">
              <i class="${activity.icon} ${activity.color}"></i>
            </div>
            <div class="flex-1">
              <p class="text-sm text-gray-900">${activity.message}</p>
              <p class="text-xs text-gray-500">${activity.time}</p>
            </div>
          </div>
        `).join('');
      }

      // 初始化图表
      function initializeCharts() {
        // 用户增长趋势图
        const userCtx = document.getElementById('userGrowthChart').getContext('2d');
        userGrowthChart = new Chart(userCtx, {
          type: 'line',
          data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
            datasets: [{
              label: '用户数量',
              data: [120, 190, 300, 500, 800, 1000, 1256],
              borderColor: '#3b82f6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: 'rgba(0, 0, 0, 0.1)'
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          }
        });

        // 消息活跃度图
        const messageCtx = document.getElementById('messageActivityChart').getContext('2d');
        messageActivityChart = new Chart(messageCtx, {
          type: 'bar',
          data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [{
              label: '消息数量',
              data: [1200, 1900, 3000, 2500, 2800, 3200, 2847],
              backgroundColor: [
                'rgba(16, 185, 129, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(16, 185, 129, 0.8)',
                'rgba(59, 130, 246, 0.8)'
              ],
              borderColor: [
                'rgba(16, 185, 129, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(16, 185, 129, 1)',
                'rgba(59, 130, 246, 1)'
              ],
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  color: 'rgba(0, 0, 0, 0.1)'
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    </script>
  </body>
</html>
