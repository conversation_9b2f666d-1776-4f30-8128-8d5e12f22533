<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-dashboard-line text-2xl text-blue-500 mr-2"></i>
                        <span class="text-lg font-medium text-gray-900">模板管理系统</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-gray-700 mr-4">欢迎，{{ current_user.username }}</span>
                    <a href="{{ url_for('admin_logout') }}" class="text-gray-700 hover:text-red-500 flex items-center">
                        <i class="ri-logout-box-line mr-1"></i>
                        <span>退出</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    管理员仪表盘
                </h2>
            </div>
        </div>

        <!-- 数据卡片 -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            <!-- 模板数量卡片 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-50 rounded-full p-3">
                            <i class="ri-layout-grid-line text-xl text-blue-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    模板总数
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">
                                        {{ stats.total_templates }}
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-4 sm:px-6">
                    <div class="text-sm">
                        <span class="font-medium text-blue-600">当前激活: </span>
                        <span class="text-gray-900">
                            {% if stats.active_template %}
                                {% if 'yehuo' in stats.active_template.template_name %}
                                    finviz数据提取后台
                                {% else %}
                                    {{ stats.active_template.template_name }}
                                {% endif %}
                            {% else %}
                                无
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 访问量卡片 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-green-50 rounded-full p-3">
                            <i class="ri-eye-line text-xl text-green-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    总访问量
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">
                                        {{ stats.total_visits }}
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-4 sm:px-6">
                    <div class="text-sm">
                        <span class="font-medium text-green-600">用户数: </span>
                        <span class="text-gray-900">{{ stats.total_users }}</span>
                    </div>
                </div>
            </div>

            <!-- 转化率卡片 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-purple-50 rounded-full p-3">
                            <i class="ri-percent-line text-xl text-purple-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    平均转化率
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">
                                        {{ stats.conversion_rate }}%
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-4 sm:px-6">
                    <div class="text-sm">
                        <span class="font-medium text-purple-600">平均停留时间: </span>
                        <span class="text-gray-900">{{ stats.avg_time }} 分钟</span>
                    </div>
                </div>
            </div>

            <!-- 自定义配置卡片 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-indigo-50 rounded-full p-3">
                            <i class="ri-settings-line text-xl text-indigo-500"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">
                                    自定义配置
                                </dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">
                                        {{ stats.total_customizations }}
                                    </div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-4 sm:px-6">
                    <div class="text-sm">
                        <span class="font-medium text-indigo-600">凭证数量: </span>
                        <span class="text-gray-900">{{ stats.total_credentials }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                    最近活动
                </h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">
                    系统最近的操作记录
                </p>
            </div>
            <div class="border-t border-gray-200">
                <ul role="list" class="divide-y divide-gray-200">
                    {% for activity in recent_activities %}
                    <li>
                        <div class="px-4 py-4 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        {% if activity.action == '模板切换' %}
                                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                            <i class="ri-refresh-line"></i>
                                        </div>
                                        {% elif activity.action == '凭证更新' %}
                                        <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-500">
                                            <i class="ri-key-line"></i>
                                        </div>
                                        {% elif activity.action == '模板自定义' %}
                                        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                                            <i class="ri-palette-line"></i>
                                        </div>
                                        {% else %}
                                        <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
                                            <i class="ri-user-line"></i>
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">
                                            {{ activity.action }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ activity.description }}
                                        </p>
                                    </div>
                                </div>
                                <div class="ml-2 flex-shrink-0 flex">
                                    <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                        {{ activity.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- 功能卡片 -->
        <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <!-- 模板管理卡片 -->
            <a href="{{ url_for('admin_templates') }}" class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-50 rounded-md p-3">
                            <i class="ri-layout-line text-2xl text-blue-500"></i>
                        </div>
                        <div class="ml-5">
                            <h3 class="text-lg font-medium text-gray-900">模板管理</h3>
                            <p class="text-sm text-gray-500">设置首页显示的模板</p>
                        </div>
                    </div>
                </div>
            </a>

            <!-- 增强版模板管理卡片 -->
            <a href="{{ url_for('admin_enhanced_templates') }}" class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-indigo-50 rounded-md p-3">
                            <i class="ri-layout-masonry-line text-2xl text-indigo-500"></i>
                        </div>
                        <div class="ml-5">
                            <h3 class="text-lg font-medium text-gray-900">增强版模板管理</h3>
                            <p class="text-sm text-gray-500">预览和自定义模板</p>
                        </div>
                    </div>
                </div>
            </a>

            <!-- 凭证管理卡片 -->
            <a href="{{ url_for('admin_credentials') }}" class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-purple-50 rounded-md p-3">
                            <i class="ri-key-line text-2xl text-purple-500"></i>
                        </div>
                        <div class="ml-5">
                            <h3 class="text-lg font-medium text-gray-900">凭证管理</h3>
                            <p class="text-sm text-gray-500">管理模板登录凭证</p>
                        </div>
                    </div>
                </div>
            </a>



            <!-- 查看首页卡片 -->
            <a href="{{ url_for('index') }}" target="_blank" class="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-green-50 rounded-md p-3">
                            <i class="ri-home-line text-2xl text-green-500"></i>
                        </div>
                        <div class="ml-5">
                            <h3 class="text-lg font-medium text-gray-900">查看首页</h3>
                            <p class="text-sm text-gray-500">在新窗口中查看当前首页</p>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>
</body>
</html>
