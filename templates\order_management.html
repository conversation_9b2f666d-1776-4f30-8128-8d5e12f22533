<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>订单管理 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a href="/" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">工作台</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">资金管理</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">对账中心</a>
            <a href="#" class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all">产品中心</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">数据中心</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">学习中心</a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input type="text" placeholder="搜索功能/应用/服务" class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-notification-3-line text-xl"></i>
            <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all">
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">订单管理</h1>
            <p class="mt-1 text-sm text-gray-500">管理所有交易订单，跟踪订单状态，处理退款申请</p>
          </div>

          <!-- 订单统计卡片 -->
          <div class="grid grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">待付款</span>
                <div class="w-8 h-8 rounded-full bg-orange-50 flex items-center justify-center text-orange-500">
                  <i class="ri-time-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">8</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">待发货</span>
                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center text-blue-500">
                  <i class="ri-inbox-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">12</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">待收货</span>
                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center text-green-500">
                  <i class="ri-truck-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">5</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">退款中</span>
                <div class="w-8 h-8 rounded-full bg-red-50 flex items-center justify-center text-red-500">
                  <i class="ri-refund-2-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">3</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">已完成</span>
                <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center text-purple-500">
                  <i class="ri-check-double-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">156</div>
            </div>
          </div>

          <!-- 订单搜索和筛选 -->
          <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center space-x-2">
                <div class="relative">
                  <input type="text" placeholder="订单号/商品名称/买家" class="pl-9 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-64 transition-all" />
                  <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <button class="px-4 py-2 bg-primary text-white rounded-lg text-sm hover:bg-primary/90 transition-all">
                  搜索
                </button>
              </div>
              <div class="flex items-center space-x-2">
                <button class="px-3 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-filter-3-line mr-1"></i>
                  筛选
                </button>
                <button class="px-3 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-download-line mr-1"></i>
                  导出
                </button>
                <button class="px-3 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                  <i class="ri-settings-3-line mr-1"></i>
                  设置
                </button>
              </div>
            </div>
            <div class="flex items-center space-x-4 text-sm">
              <div class="flex items-center">
                <span class="text-gray-500 mr-2">订单状态:</span>
                <select class="border border-gray-200 rounded-lg px-3 py-1.5 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20">
                  <option>全部</option>
                  <option>待付款</option>
                  <option>待发货</option>
                  <option>待收货</option>
                  <option>已完成</option>
                  <option>已关闭</option>
                  <option>退款中</option>
                </select>
              </div>
              <div class="flex items-center">
                <span class="text-gray-500 mr-2">下单时间:</span>
                <select class="border border-gray-200 rounded-lg px-3 py-1.5 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20">
                  <option>全部</option>
                  <option>今天</option>
                  <option>昨天</option>
                  <option>近7天</option>
                  <option>近30天</option>
                  <option>自定义</option>
                </select>
              </div>
              <div class="flex items-center">
                <span class="text-gray-500 mr-2">订单类型:</span>
                <select class="border border-gray-200 rounded-lg px-3 py-1.5 focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20">
                  <option>全部</option>
                  <option>普通订单</option>
                  <option>活动订单</option>
                  <option>会员订单</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 订单列表 -->
          <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
            <table class="w-full">
              <thead class="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单信息</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">买家</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单状态</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <!-- 订单项 1 -->
                <tr>
                  <td class="py-4 px-4">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-start">
                      <img src="https://via.placeholder.com/60" alt="商品图片" class="w-12 h-12 rounded object-cover mr-3">
                      <div>
                        <div class="text-sm font-medium text-gray-800 mb-1">高级商务办公椅人体工学椅</div>
                        <div class="text-xs text-gray-500">订单号: 2025050812345678</div>
                        <div class="text-xs text-gray-500">下单时间: 2025-05-08 14:30:25</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">张先生</div>
                    <div class="text-xs text-gray-500">138****6789</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm font-medium text-gray-800">¥1,299.00</div>
                    <div class="text-xs text-gray-500">含运费: ¥15.00</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-blue-50 text-blue-600 rounded-full">待发货</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">详情</button>
                      <button class="px-2 py-1 text-xs text-white bg-primary rounded hover:bg-primary/90 transition-all">发货</button>
                    </div>
                  </td>
                </tr>
                <!-- 订单项 2 -->
                <tr>
                  <td class="py-4 px-4">
                    <input type="checkbox" class="rounded text-primary focus:ring-primary/20">
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex items-start">
                      <img src="https://via.placeholder.com/60" alt="商品图片" class="w-12 h-12 rounded object-cover mr-3">
                      <div>
                        <div class="text-sm font-medium text-gray-800 mb-1">智能手表健康监测多功能运动手环</div>
                        <div class="text-xs text-gray-500">订单号: 2025050812345679</div>
                        <div class="text-xs text-gray-500">下单时间: 2025-05-08 10:15:36</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">李女士</div>
                    <div class="text-xs text-gray-500">159****4321</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm font-medium text-gray-800">¥899.00</div>
                    <div class="text-xs text-gray-500">含运费: ¥0.00</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-orange-50 text-orange-600 rounded-full">待付款</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">详情</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">提醒付款</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- 分页 -->
            <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div class="text-sm text-gray-500">
                显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">156</span> 条
              </div>
              <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">上一页</button>
                <button class="px-3 py-1 text-sm text-white bg-primary rounded hover:bg-primary/90 transition-all">1</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">2</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">3</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下一页</button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      // 这里可以添加订单管理相关的JavaScript代码
      document.addEventListener('DOMContentLoaded', function() {
        console.log('订单管理页面已加载');
      });
    </script>
  </body>
</html>
