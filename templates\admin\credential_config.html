<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板登录凭证管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-dashboard-line text-2xl text-blue-500 mr-2"></i>
                        <span class="text-lg font-medium text-gray-900">模板管理系统</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-dashboard-line mr-1"></i>
                        <span>仪表盘</span>
                    </a>
                    <a href="{{ url_for('admin_templates') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-layout-line mr-1"></i>
                        <span>模板管理</span>
                    </a>
                    <a href="{{ url_for('admin_enhanced_templates') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-layout-masonry-line mr-1"></i>
                        <span>模板自定义</span>
                    </a>
                    <a href="{{ url_for('admin_content') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-file-text-line mr-1"></i>
                        <span>内容管理</span>
                    </a>
                    <span class="text-gray-700 mr-4">欢迎，{{ current_user.username }}</span>
                    <a href="{{ url_for('admin_logout') }}" class="text-gray-700 hover:text-red-500 flex items-center">
                        <i class="ri-logout-box-line mr-1"></i>
                        <span>退出</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    模板登录凭证管理
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    管理各个模板的前端登录账号和密码
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ url_for('admin_templates') }}" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="ri-layout-line mr-1"></i>
                    模板管理
                </a>
                <a href="{{ url_for('index') }}" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="ri-eye-line mr-1"></i>
                    查看首页
                </a>
            </div>
        </div>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600">{{ messages[0] }}</p>
                </div>
            {% endif %}
        {% endwith %}

        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for template in templates %}
                <li>
                    <div class="px-4 py-5 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    {% if 'alipay' in template.template_name %}
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                                        <i class="ri-alipay-line text-xl"></i>
                                    </div>
                                    {% elif 'tiktok' in template.template_name %}
                                    <div class="h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center text-pink-500">
                                        <i class="ri-tiktok-line text-xl"></i>
                                    </div>
                                    {% elif 'qianniu' in template.template_name %}
                                    <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-500">
                                        <i class="ri-store-2-line text-xl"></i>
                                    </div>
                                    {% elif 'meituan' in template.template_name %}
                                    <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-600">
                                        <i class="ri-store-3-line text-xl"></i>
                                    </div>
                                    {% else %}
                                    <div class="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
                                        <i class="ri-layout-line text-xl"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {% if 'alipay' in template.template_name %}
                                            支付宝商家后台
                                        {% elif 'tiktok' in template.template_name %}
                                            TikTok主播数据后台
                                        {% elif 'qianniu' in template.template_name %}
                                            千牛商家后台
                                        {% elif 'meituan' in template.template_name %}
                                            美团商家后台
                                        {% else %}
                                            {{ template.template_name }}
                                        {% endif %}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        文件名: {{ template.template_name }}
                                    </div>
                                </div>
                            </div>
                            <div class="ml-4 flex-shrink-0">
                                {% if template.is_active %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    当前使用
                                </span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mt-6">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">登录凭证设置</h3>
                            <form method="post" action="{{ url_for('admin_credentials') }}" class="space-y-4">
                                <input type="hidden" name="template_id" value="{{ template.id }}">

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="username_{{ template.id }}" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                                        <input type="text" id="username_{{ template.id }}" name="username"
                                            value="{% if template.id in credentials %}{{ credentials[template.id].username }}{% endif %}"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>
                                    <div>
                                        <label for="password_{{ template.id }}" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                                        <input type="text" id="password_{{ template.id }}" name="password"
                                            value="{% if template.id in credentials %}{{ credentials[template.id].password }}{% endif %}"
                                            required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>
                                </div>

                                <div>
                                    <label for="description_{{ template.id }}" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                    <input type="text" id="description_{{ template.id }}" name="description"
                                        value="{% if template.id in credentials %}{{ credentials[template.id].description }}{% endif %}"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存凭证
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</body>
</html>
