/**
 * 统一的侧边栏菜单组件
 * 提供菜单折叠/展开、高亮等功能
 */

// 初始化侧边栏菜单
function initSidebarMenu() {
  // 侧边栏菜单项点击事件
  const sidebarMenuItems = document.querySelectorAll(".sidebar-menu-item");
  sidebarMenuItems.forEach((item) => {
    item.addEventListener("click", function () {
      // 移除所有项目的活动状态
      sidebarMenuItems.forEach((i) => i.classList.remove("active"));
      // 为当前点击项添加活动状态
      this.classList.add("active");
    });
  });

  // 菜单折叠/展开功能
  const menuHeaders = document.querySelectorAll('.menu-header');
  menuHeaders.forEach(header => {
    header.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const submenu = document.getElementById(targetId);
      const arrow = this.querySelector('.menu-arrow');
      const submenuItems = submenu ? submenu.querySelectorAll('.submenu-item') : [];

      if (submenu) {
        if (submenu.classList.contains('show')) {
          // 收起菜单
          submenu.classList.remove('show');
          arrow.classList.remove('ri-arrow-down-s-line');
          arrow.classList.add('ri-arrow-right-s-line');
        } else {
          // 展开菜单
          submenu.classList.add('show');
          arrow.classList.remove('ri-arrow-right-s-line');
          arrow.classList.add('ri-arrow-down-s-line');
        }
      }
    });
  });

  // 初始化箭头方向
  initMenuArrows();
}

// 初始化菜单箭头方向
function initMenuArrows() {
  const menuHeaders = document.querySelectorAll('.menu-header');
  menuHeaders.forEach(header => {
    const targetId = header.getAttribute('data-target');
    const submenu = document.getElementById(targetId);
    const arrow = header.querySelector('.menu-arrow');

    if (submenu && arrow) {
      if (submenu.classList.contains('show')) {
        arrow.classList.remove('ri-arrow-right-s-line');
        arrow.classList.add('ri-arrow-down-s-line');
      } else {
        arrow.classList.remove('ri-arrow-down-s-line');
        arrow.classList.add('ri-arrow-right-s-line');
      }
    }
  });
}

// 高亮当前页面菜单项
function highlightCurrentPage(currentPage) {
  // 模板中已经通过{% if current_page == 'xxx' %}active{% endif %}来处理高亮
  // 自动展开包含当前页面的子菜单，保持用户操作的连续性
  expandCurrentPageMenu(currentPage);
}

// 展开包含当前页面的子菜单
function expandCurrentPageMenu(currentPage) {
  const menuMappings = {
    'user_list': 'user-menu',
    'banned_users': 'user-menu',
    'sensitive_words': 'sensitive-menu',
    'sensitive_word_hits': 'sensitive-menu',
    'group_management': 'group-menu',
    'message_management': 'message-menu',
    'user_messages': 'message-menu'
  };

  const targetMenuId = menuMappings[currentPage];
  if (targetMenuId) {
    const submenu = document.getElementById(targetMenuId);
    const header = document.querySelector(`[data-target="${targetMenuId}"]`);
    const arrow = header ? header.querySelector('.menu-arrow') : null;

    if (submenu) {
      // 展开包含当前页面的子菜单
      submenu.classList.add('show');

      // 更新箭头方向
      if (arrow) {
        arrow.classList.remove('ri-arrow-right-s-line');
        arrow.classList.add('ri-arrow-down-s-line');
      }
    }
  }
}

// 页面加载完成后初始化菜单
document.addEventListener('DOMContentLoaded', function() {
  // 延迟初始化，确保所有页面脚本都已加载
  setTimeout(() => {
    initSidebarMenu();

    // 如果页面定义了当前页面标识，则高亮对应菜单
    if (typeof window.currentPage !== 'undefined') {
      highlightCurrentPage(window.currentPage);
    }
  }, 100);
});

// 导出函数供其他脚本使用
window.SidebarMenu = {
  init: initSidebarMenu,
  highlight: highlightCurrentPage,
  expand: expandCurrentPageMenu
};
