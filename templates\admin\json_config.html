<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON配置管理 - 模板管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.js"></script>
    <style>
        .jsoneditor {
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
        }
        .jsoneditor-menu {
            background-color: #4f46e5;
            border-bottom: 1px solid #4338ca;
        }
        .jsoneditor-poweredBy {
            display: none;
        }
        .jsoneditor-statusbar {
            border-top: 1px solid #e2e8f0;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    {% include 'admin/navbar.html' %}

    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-xl font-semibold text-gray-800">JSON配置管理</h1>
                <p class="text-sm text-gray-600 mt-1">使用JSON配置文件来定义模板的布局、内容和显示逻辑</p>
            </div>

            <div class="p-6">
                <div class="mb-6">
                    <label for="template-select" class="block text-sm font-medium text-gray-700 mb-2">选择要编辑的模板</label>
                    <div class="flex space-x-4">
                        <select id="template-select" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            {% for template in templates %}
                            <option value="{{ template.template_name.replace('.html', '') }}" {% if template.is_active %}selected{% endif %}>
                                {% if 'yehuo' in template.template_name %}
                                    finviz数据提取后台
                                {% else %}
                                    {{ template.template_name }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <button id="load-config" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="ri-download-line mr-2"></i>
                            加载配置
                        </button>
                    </div>
                </div>

                <div class="mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <h2 class="text-lg font-medium text-gray-800">JSON编辑器</h2>
                        <div class="flex space-x-2">
                            <button id="format-json" class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-code-line mr-1"></i>
                                格式化
                            </button>
                            <button id="compact-json" class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-code-s-slash-line mr-1"></i>
                                压缩
                            </button>
                            <button id="reset-json" class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="ri-refresh-line mr-1"></i>
                                重置
                            </button>
                        </div>
                    </div>
                    <div id="jsoneditor" class="w-full h-[600px]"></div>
                </div>

                <div class="flex justify-between">
                    <button id="preview-config" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="ri-eye-line mr-2"></i>
                        预览
                    </button>
                    <button id="save-config" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="ri-save-line mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知弹窗 -->
    <div id="notification" class="fixed top-4 right-4 max-w-sm bg-white border border-gray-200 rounded-lg shadow-lg transform transition-transform duration-300 translate-x-full">
        <div class="p-4">
            <div class="flex items-start">
                <div id="notification-icon" class="flex-shrink-0">
                    <i class="ri-check-line text-green-500 text-xl"></i>
                </div>
                <div class="ml-3 w-0 flex-1">
                    <p id="notification-title" class="text-sm font-medium text-gray-900">操作成功</p>
                    <p id="notification-message" class="mt-1 text-sm text-gray-500">配置已成功保存。</p>
                </div>
                <div class="ml-4 flex-shrink-0 flex">
                    <button id="close-notification" class="inline-flex text-gray-400 hover:text-gray-500">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化JSON编辑器
            const container = document.getElementById('jsoneditor');
            const options = {
                mode: 'tree',
                modes: ['tree', 'view', 'form', 'code', 'text'],
                onError: function(err) {
                    showNotification('错误', err.toString(), 'error');
                },
                onModeChange: function(newMode, oldMode) {
                    console.log('Mode changed from', oldMode, 'to', newMode);
                }
            };
            const editor = new JSONEditor(container, options);
            let originalJson = null;

            // 加载配置按钮
            document.getElementById('load-config').addEventListener('click', function() {
                const templateName = document.getElementById('template-select').value;
                loadConfig(templateName);
            });

            // 格式化JSON按钮
            document.getElementById('format-json').addEventListener('click', function() {
                try {
                    const json = editor.get();
                    editor.set(json);
                    editor.expandAll();
                } catch (err) {
                    showNotification('错误', '无法格式化JSON: ' + err.toString(), 'error');
                }
            });

            // 压缩JSON按钮
            document.getElementById('compact-json').addEventListener('click', function() {
                try {
                    const json = editor.get();
                    editor.set(json);
                    editor.collapseAll();
                } catch (err) {
                    showNotification('错误', '无法压缩JSON: ' + err.toString(), 'error');
                }
            });

            // 重置JSON按钮
            document.getElementById('reset-json').addEventListener('click', function() {
                if (originalJson) {
                    editor.set(originalJson);
                    showNotification('成功', 'JSON已重置为原始状态', 'success');
                } else {
                    showNotification('警告', '没有原始JSON可供重置', 'warning');
                }
            });

            // 预览配置按钮
            document.getElementById('preview-config').addEventListener('click', function() {
                try {
                    const json = editor.get();
                    const templateName = document.getElementById('template-select').value;
                    const previewUrl = `/?preview=${templateName}`;
                    window.open(previewUrl, '_blank');
                } catch (err) {
                    showNotification('错误', '无法预览配置: ' + err.toString(), 'error');
                }
            });

            // 保存配置按钮
            document.getElementById('save-config').addEventListener('click', function() {
                try {
                    const json = editor.get();
                    const templateName = document.getElementById('template-select').value;
                    saveConfig(templateName, json);
                } catch (err) {
                    showNotification('错误', '无法保存配置: ' + err.toString(), 'error');
                }
            });

            // 关闭通知按钮
            document.getElementById('close-notification').addEventListener('click', function() {
                hideNotification();
            });

            // 加载配置函数
            async function loadConfig(templateName) {
                try {
                    const response = await fetch(`/api/template_config/${templateName}`);
                    if (!response.ok) {
                        throw new Error(`Failed to load template config: ${response.statusText}`);
                    }
                    const config = await response.json();
                    editor.set(config);
                    originalJson = JSON.parse(JSON.stringify(config)); // 深拷贝
                    showNotification('成功', `已加载 ${templateName} 的配置`, 'success');
                } catch (error) {
                    console.error('Error loading template config:', error);
                    showNotification('错误', `加载配置失败: ${error.message}`, 'error');
                }
            }

            // 保存配置函数
            async function saveConfig(templateName, config) {
                try {
                    const response = await fetch(`/api/template_config/${templateName}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(config)
                    });

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || `Failed to save template config: ${response.statusText}`);
                    }

                    originalJson = JSON.parse(JSON.stringify(config)); // 更新原始JSON
                    showNotification('成功', `已保存 ${templateName} 的配置`, 'success');
                } catch (error) {
                    console.error('Error saving template config:', error);
                    showNotification('错误', `保存配置失败: ${error.message}`, 'error');
                }
            }

            // 显示通知函数
            function showNotification(title, message, type) {
                const notification = document.getElementById('notification');
                const notificationTitle = document.getElementById('notification-title');
                const notificationMessage = document.getElementById('notification-message');
                const notificationIcon = document.getElementById('notification-icon');

                notificationTitle.textContent = title;
                notificationMessage.textContent = message;

                // 设置图标和颜色
                if (type === 'success') {
                    notificationIcon.innerHTML = '<i class="ri-check-line text-green-500 text-xl"></i>';
                } else if (type === 'error') {
                    notificationIcon.innerHTML = '<i class="ri-error-warning-line text-red-500 text-xl"></i>';
                } else if (type === 'warning') {
                    notificationIcon.innerHTML = '<i class="ri-alert-line text-yellow-500 text-xl"></i>';
                } else if (type === 'info') {
                    notificationIcon.innerHTML = '<i class="ri-information-line text-blue-500 text-xl"></i>';
                }

                // 显示通知
                notification.classList.remove('translate-x-full');

                // 5秒后自动隐藏
                setTimeout(hideNotification, 5000);
            }

            // 隐藏通知函数
            function hideNotification() {
                const notification = document.getElementById('notification');
                notification.classList.add('translate-x-full');
            }

            // 自动加载当前选中模板的配置
            const templateName = document.getElementById('template-select').value;
            loadConfig(templateName);
        });
    </script>
</body>
</html>
