<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-dashboard-line text-2xl text-blue-500 mr-2"></i>
                        <span class="text-lg font-medium text-gray-900">模板管理系统</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <a href="{{ url_for('admin_dashboard') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-dashboard-line mr-1"></i>
                        <span>仪表盘</span>
                    </a>
                    <a href="{{ url_for('admin_enhanced_templates') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-layout-masonry-line mr-1"></i>
                        <span>模板自定义</span>
                    </a>
                    <a href="{{ url_for('admin_content') }}" class="text-gray-700 hover:text-blue-500 mr-4 flex items-center">
                        <i class="ri-file-text-line mr-1"></i>
                        <span>内容管理</span>
                    </a>
                    <span class="text-gray-700 mr-4">欢迎，{{ current_user.username }}</span>
                    <a href="{{ url_for('admin_logout') }}" class="text-gray-700 hover:text-red-500 flex items-center">
                        <i class="ri-logout-box-line mr-1"></i>
                        <span>退出</span>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    模板管理
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    选择要在首页显示的模板
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <a href="{{ url_for('index') }}" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="ri-eye-line mr-1"></i>
                    查看首页
                </a>
            </div>
        </div>

        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-green-600">{{ messages[0] }}</p>
                </div>
            {% endif %}
        {% endwith %}

        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <form method="post" action="{{ url_for('admin_templates') }}">
                <ul class="divide-y divide-gray-200">
                    {% for template in templates %}
                    <li>
                        <div class="px-4 py-4 sm:px-6 flex items-center">
                            <input type="radio" id="template_{{ template.id }}" name="template_id" value="{{ template.id }}"
                                {% if template.is_active %}checked{% endif %}
                                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                            <label for="template_{{ template.id }}" class="ml-3 flex-1 flex items-center">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full bg-slate-800 flex items-center justify-center text-white">
                                            <i class="ri-apps-2-line text-xl"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            finviz数据提取后台
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            文件名: {{ template.template_name }}
                                        </div>
                                    </div>
                                </div>
                            </label>
                            <div class="ml-4 flex-shrink-0">
                                {% if template.is_active %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    当前使用
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                <div class="px-4 py-4 sm:px-6 border-t border-gray-200">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        保存设置
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
