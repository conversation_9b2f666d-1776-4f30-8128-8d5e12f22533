#!/usr/bin/env python3
import sqlite3
import os

def show_all_titles():
    db_path = 'data.sqlite'
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    print("=" * 60)
    print("数据库中的所有标题相关数据")
    print("=" * 60)

    # 1. 查看 TemplateContent 表中所有与标题相关的内容
    print("\n1. TemplateContent 表中的标题数据:")
    print("-" * 40)
    cursor.execute('''
        SELECT tc.id, tc.template_id, t.template_name, tc.content_key, tc.content_value, tc.content_description 
        FROM template_content tc 
        LEFT JOIN template_config t ON tc.template_id = t.id 
        WHERE tc.content_key LIKE '%title%' OR tc.content_key LIKE '%header%'
        ORDER BY tc.template_id, tc.content_key
    ''')
    results = cursor.fetchall()
    if results:
        for row in results:
            print(f"ID: {row[0]}")
            print(f"  模板ID: {row[1]} ({row[2]})")
            print(f"  键名: {row[3]}")
            print(f"  值: {row[4]}")
            print(f"  描述: {row[5]}")
            print()
    else:
        print("没有找到标题相关数据")

    # 2. 查看 SystemSettings 表中的标题设置
    print("\n2. SystemSettings 表中的标题设置:")
    print("-" * 40)
    cursor.execute('''
        SELECT id, setting_category, setting_key, setting_value, setting_description 
        FROM system_settings 
        WHERE setting_key LIKE '%title%' OR setting_category = 'labels'
        ORDER BY setting_category, setting_key
    ''')
    results = cursor.fetchall()
    if results:
        for row in results:
            print(f"ID: {row[0]}")
            print(f"  分类: {row[1]}")
            print(f"  键名: {row[2]}")
            print(f"  值: {row[3]}")
            print(f"  描述: {row[4]}")
            print()
    else:
        print("没有找到系统标题设置")

    # 3. 查看当前激活的模板
    print("\n3. 当前激活的模板:")
    print("-" * 40)
    cursor.execute('SELECT id, template_name, is_active, updated_at FROM template_config WHERE is_active = 1')
    results = cursor.fetchall()
    if results:
        for row in results:
            print(f"模板ID: {row[0]}")
            print(f"  模板名称: {row[1]}")
            print(f"  是否激活: {row[2]}")
            print(f"  更新时间: {row[3]}")
            print()
    else:
        print("没有找到激活的模板")

    # 4. 查看所有模板
    print("\n4. 所有模板列表:")
    print("-" * 40)
    cursor.execute('SELECT id, template_name, is_active FROM template_config ORDER BY id')
    results = cursor.fetchall()
    if results:
        for row in results:
            status = "激活" if row[2] else "未激活"
            print(f"ID: {row[0]}, 名称: {row[1]}, 状态: {status}")
    else:
        print("没有找到任何模板")

    conn.close()

if __name__ == "__main__":
    show_all_titles()
