<nav class="bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <span class="text-white font-bold text-xl">模板管理系统</span>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="{{ url_for('admin_dashboard') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium {% if request.path == '/admin/dashboard' %}bg-gray-900 text-white{% endif %}">
                            仪表盘
                        </a>
                        <a href="{{ url_for('admin_enhanced_templates') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium {% if request.path == '/admin/enhanced_templates' %}bg-gray-900 text-white{% endif %}">
                            模板管理
                        </a>

                        <a href="{{ url_for('admin_json_config') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium {% if request.path == '/admin/json_config' %}bg-gray-900 text-white{% endif %}">
                            JSON配置
                        </a>
                        <a href="{{ url_for('admin_friendly_config') }}" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium {% if request.path == '/admin/friendly_config' %}bg-gray-900 text-white{% endif %}">
                            可视化配置
                        </a>
                    </div>
                </div>
            </div>
            <div class="hidden md:block">
                <div class="ml-4 flex items-center md:ml-6">
                    <div class="ml-3 relative">
                        <div class="flex items-center">
                            <span class="text-gray-300 text-sm mr-2">{{ current_user.username }}</span>
                            <a href="{{ url_for('admin_logout') }}" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                退出
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
