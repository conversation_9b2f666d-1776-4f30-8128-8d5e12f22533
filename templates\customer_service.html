<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>客服管理 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
      .chat-message {
        max-width: 80%;
        word-wrap: break-word;
      }
      .chat-message.customer {
        background-color: #f0f0f0;
        border-radius: 12px 12px 12px 0;
      }
      .chat-message.merchant {
        background-color: #e6f4ff;
        border-radius: 12px 12px 0 12px;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a href="/" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">工作台</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">资金管理</a>
            <a href="#" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">对账中心</a>
            <a href="/product_management" class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all">产品中心</a>
            <a href="/data_analysis" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">数据中心</a>
            <a href="/marketing_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">营销中心</a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input type="text" placeholder="搜索功能/应用/服务" class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-notification-3-line text-xl"></i>
            <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all">
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 bg-gray-50 overflow-hidden flex">
        <!-- 左侧会话列表 -->
        <div class="w-72 bg-white border-r border-gray-100 flex-shrink-0 flex flex-col">
          <div class="p-4 border-b border-gray-100">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-base font-medium text-gray-800">客服工作台</h2>
              <div class="flex items-center space-x-2">
                <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                  <i class="ri-settings-3-line"></i>
                </button>
                <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                  <i class="ri-more-line"></i>
                </button>
              </div>
            </div>
            <div class="relative">
              <input type="text" placeholder="搜索会话" class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all" />
              <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>
          <div class="flex-1 overflow-y-auto">
            <div class="py-2">
              <div class="flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700">
                <span>未处理会话 (5)</span>
                <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                  <i class="ri-arrow-down-s-line"></i>
                </button>
              </div>
              <!-- 会话项 1 - 选中状态 -->
              <div class="px-4 py-3 bg-blue-50 border-l-2 border-primary cursor-pointer">
                <div class="flex items-start">
                  <div class="relative mr-3">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
                    <span class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-800 truncate">张先生</h3>
                      <span class="text-xs text-gray-500">10:25</span>
                    </div>
                    <p class="text-xs text-gray-600 truncate mt-1">请问我的订单什么时候发货？已经等了两天了</p>
                  </div>
                </div>
              </div>
              <!-- 会话项 2 -->
              <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer">
                <div class="flex items-start">
                  <div class="relative mr-3">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
                    <span class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-800 truncate">李女士</h3>
                      <span class="text-xs text-gray-500">09:42</span>
                    </div>
                    <p class="text-xs text-gray-600 truncate mt-1">收到的商品有点问题，能退换吗？</p>
                  </div>
                </div>
              </div>
              <!-- 会话项 3 -->
              <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer">
                <div class="flex items-start">
                  <div class="relative mr-3">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
                    <span class="absolute bottom-0 right-0 w-3 h-3 bg-gray-300 border-2 border-white rounded-full"></span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-800 truncate">王先生</h3>
                      <span class="text-xs text-gray-500">昨天</span>
                    </div>
                    <p class="text-xs text-gray-600 truncate mt-1">请问这个商品有什么颜色可选？</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="py-2">
              <div class="flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700">
                <span>已处理会话 (8)</span>
                <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                  <i class="ri-arrow-down-s-line"></i>
                </button>
              </div>
              <!-- 已处理会话项 -->
              <div class="px-4 py-3 hover:bg-gray-50 cursor-pointer">
                <div class="flex items-start">
                  <div class="relative mr-3">
                    <img src="https://via.placeholder.com/40" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
                    <span class="absolute bottom-0 right-0 w-3 h-3 bg-gray-300 border-2 border-white rounded-full"></span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-800 truncate">赵女士</h3>
                      <span class="text-xs text-gray-500">昨天</span>
                    </div>
                    <p class="text-xs text-gray-600 truncate mt-1">谢谢，问题已解决</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="flex-1 flex flex-col">
          <!-- 聊天头部 -->
          <div class="h-16 bg-white border-b border-gray-100 px-6 flex items-center justify-between">
            <div class="flex items-center">
              <h2 class="text-base font-medium text-gray-800">张先生</h2>
              <span class="ml-2 px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full">订单问题</span>
              <span class="ml-2 text-xs text-gray-500">订单号: 2025050812345678</span>
            </div>
            <div class="flex items-center space-x-2">
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-user-line"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-file-list-line"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-more-line"></i>
              </button>
            </div>
          </div>

          <!-- 聊天内容区域 -->
          <div class="flex-1 overflow-y-auto p-6 bg-gray-50">
            <!-- 时间分割线 -->
            <div class="flex items-center justify-center mb-4">
              <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">今天 10:15</span>
            </div>

            <!-- 客户消息 -->
            <div class="flex items-start mb-4">
              <img src="https://via.placeholder.com/40" alt="用户头像" class="w-8 h-8 rounded-full object-cover mr-2" />
              <div class="chat-message customer p-3">
                <p class="text-sm text-gray-800">您好，我想咨询一下我的订单，订单号是2025050812345678，已经付款两天了，但是还没有发货，请问什么时候能发货呢？</p>
              </div>
            </div>

            <!-- 商家消息 -->
            <div class="flex items-start flex-row-reverse mb-4">
              <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-primary ml-2">
                <i class="ri-customer-service-2-line"></i>
              </div>
              <div class="chat-message merchant p-3">
                <p class="text-sm text-gray-800">您好，感谢您的耐心等待。我已经查询到您的订单信息，由于最近订单量较大，发货可能会有所延迟。您的订单已经在处理中，预计今天下午就能发出，请您再稍等一下。</p>
              </div>
            </div>

            <!-- 客户消息 -->
            <div class="flex items-start mb-4">
              <img src="https://via.placeholder.com/40" alt="用户头像" class="w-8 h-8 rounded-full object-cover mr-2" />
              <div class="chat-message customer p-3">
                <p class="text-sm text-gray-800">好的，谢谢您的回复。请问发货后大概多久能收到呢？</p>
              </div>
            </div>

            <!-- 商家消息 -->
            <div class="flex items-start flex-row-reverse mb-4">
              <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-primary ml-2">
                <i class="ri-customer-service-2-line"></i>
              </div>
              <div class="chat-message merchant p-3">
                <p class="text-sm text-gray-800">根据您的收货地址，正常情况下发货后1-2天内就能送达。我们使用的是顺丰快递，发货后您可以在订单详情页查看物流信息。如果有任何问题，随时可以联系我们。</p>
              </div>
            </div>

            <!-- 客户消息 -->
            <div class="flex items-start mb-4">
              <img src="https://via.placeholder.com/40" alt="用户头像" class="w-8 h-8 rounded-full object-cover mr-2" />
              <div class="chat-message customer p-3">
                <p class="text-sm text-gray-800">请问我的订单什么时候发货？已经等了两天了</p>
              </div>
            </div>
          </div>

          <!-- 聊天输入区域 -->
          <div class="bg-white border-t border-gray-100 p-4">
            <div class="flex items-center mb-2 space-x-2">
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-emotion-line"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-image-line"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-file-line"></i>
              </button>
              <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-gray-50 rounded-full">
                <i class="ri-reply-line"></i>
              </button>
              <div class="flex-1"></div>
              <button class="px-3 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                常用语
              </button>
            </div>
            <div class="relative">
              <textarea placeholder="输入消息..." class="w-full px-4 py-3 text-sm border border-gray-200 rounded-lg focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 resize-none h-24"></textarea>
              <div class="absolute right-3 bottom-3">
                <button class="px-4 py-2 bg-primary text-white rounded-lg text-sm hover:bg-primary/90 transition-all">
                  发送
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        console.log('客服管理页面已加载');
      });
    </script>
  </body>
</html>
