B

    `S%h�} �            G   @   s�  d dl mZmZmZmZmZmZmZmZ d dl	m
Z
 d dlmZm
Z
mZmZmZmZ d dlZd dlZd dlZd dlmZmZmZ ee�Ze�d�e_d dlZej�ej�e��Z dej�!e d� ej"d	< d
ej"d< e
e�Z#e� Z$e$�%e� de$_&G d
d� de
e#j'�Z(G dd� de#j'�Z)G dd� de#j'�Z*G dd� de#j'�Z+G dd� de#j'�Z,G dd� de#j'�Z-G dd� de#j'�Z.G dd� de#j'�Z/G dd� de#j'�Z0e$j1dd � �Z2e�3� �� e#�4�  e)j5�6� �sPd!d"d#d$gZ7g Z8xTe7D ]LZ9ej�:ej�!ej;e9���re9d!kZ<e8�=e)e9e<d%�� ne>d&e9� d'�� �q�W e8�r<e#j�?e8� ne#j�@e)d!d(d%�� e(j5jAd)d*��6� �s|e(d)d+d,�ZBe#j�@eB� e)j5�C� ZD�x*eDD �] ZEe*j5jAeEjFd-��6� �sHd.eEjGk�r�e*eEjFd/d0d1d2�ZHnrd3eEjGk�r�e*eEjFd4d0d5d2�ZHnRd6eEjGk�r
e*eEjFd7d0d8d2�ZHn2d9eEjGk�r*e*eEjFd:d0d;d2�ZHne*eEjFd<d0d=d2�ZHe#j�@eH� e+j5jAeEjFd-��6� �s�d.eEjGk�r|e+eEjFd>d?d@�ZInjd3eEjGk�r�e+eEjFdAd?d@�ZInLd6eEjGk�r�e+eEjFdBd?d@�ZIn.d9eEjGk�r�e+eEjFdCd?d@�ZIne+eEjFd>d?d@�ZIe#j�@eI� d.eEjGk�r�e,j5jAeEjFd-��6� �s�e,eEjFdDdEdFdGdH�e,eEjFdIdJdKdLdH�e,eEjFdIdMdNdOdH�e,eEjFdIdPdQdRdH�e,eEjFdIdSdTdUdH�e,eEjFdIdVdWdXdH�e,eEjFdIdYdZd[dH�e,eEjFd\d]d^d_dH�e,eEjFd`dadbdcdH�e,eEjFdddedfdgdH�e,eEjFdddhdidjdH�e,eEjFdddkdldmdH�e,eEjFdddndodpdH�e,eEjFdddqdrdsdH�e,eEjFd`dtdudvdH�e,eEjFdddwdxdydH�e,eEjFdddzd{d|dH�e,eEjFd}d~dd�dH�e,eEjFddd�d�d�dH�e,eEjFddd�d�d�dH�e,eEjFddd�d�d�dH�e,eEjFddd�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d�d�d�dH�e,eEjFd�d��d �ddH�e,eEjFd��d�d�ddH�e,eEjFd��d�d�ddH�e,eEjFd��d�d	�d
dH�e,eEjFd��d�d�d
dH�g?ZJe#j�?eJ� �q�W e#j�K�  W dQ R X e�L�d��d�d� �ZMejL�d�dg�d�e�d�d� ��ZNe�L�d��d�d� �ZOejL�d�d�dg�d��d�d� �ZPe�L�d��d�d� �ZQe�L�d��d �d!� �ZRe�L�d"��d#�d$� �ZSe�L�d%��d&�d'� �ZTe�L�d(��d)�d*� �ZUe�L�d+��d,�d-� �ZVe�L�d.��d/�d0� �ZWe�L�d1��d2�d3� �ZXe�L�d4��d5�d6� �ZYe�L�d7��d8�d9� �ZZe�L�d:��d;�d<� �Z[e�L�d=��d>�d?� �Z\ejL�d@�d�dg�d��dAd� �Z]e�L�dB�e�dC�dD� ��Z^e�L�dE�e�dF�dG� ��Z_e�L�dH�e�dI�dJ� ��Z`e�L�dK�e�dL�dM� ��ZaejL�dN�d�dg�d�e�dO�dP� ��ZbejL�dQ�d�dg�d�e�dR�dS� ��ZcejL�dT�d�dg�d�e�dU�dV� ��ZdejL�dW�d�dg�d�e�dX�dY� ��Zee�L�dZ��d[�d\� �Zfe�d]k�r�ejg�d^�d_d(d
�d`� dS (a  �    )�Flask�render_template�request�redirect�url_for�flash�session�jsonify)�
SQLAlchemy)�LoginManager�	UserMixin�
login_user�login_required�logout_user�current_userN)�datetime�timezone�	timedelta�   z
sqlite:///zdata.sqliteZSQLALCHEMY_DATABASE_URIFZSQLALCHEMY_TRACK_MODIFICATIONS�admin_loginc               @   sN   e Zd Zejejdd�Zeje�d�ddd�Zeje�d�dd�Z	dd	� Z
d
S )�UserT)�primary_key�P   F)Zunique�nullable�x   )r   c             C   s   d| j � d�S )Nz<User �>)�username)�self� r   �DC:\Users\<USER>\.PyCharm2018.3\config\scratches\houtai\app.py�__repr__!   s    z
User.__repr__N)�__name__�
__module__�__qualname__�db�Column�Integer�id�Stringr   �passwordr    r   r   r   r   r      s   r   c               @   s\   e Zd Zejejdd�Zeje�d�dd�Zejej	dd�Z
ejejdd� d�Zd	d
� Z
dS )�TemplateConfigT)r   �2   F)r   )�defaultc               C   s   t �tj�S )N)r   �nowr   �utcr   r   r   r   �<lambda>(   �    zTemplateConfig.<lambda>c             C   s   d| j � d�S )Nz<TemplateConfig r   )�
template_name)r   r   r   r   r    *   s    zTemplateConfig.__repr__N)r!   r"   r#   r$   r%   r&   r'   r(   r1   �Boolean�	is_active�DateTime�
updated_atr    r   r   r   r   r*   $   s
   r*   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�dd�Ze�e�	d��Zejej
d	d
� d�Zejej
dd
� dd
� d
�Zejdejddd�d�Zdd� ZdS )�TemplateCredentialT)r   ztemplate_config.idF)r   r+   �d   ��   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   3   r0   zTemplateCredential.<lambda>)r,   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   4   r0   )r,   �onupdater*   �credentials)�lazy)�backrefc             C   s   d| j � d| j� d�S )Nz<TemplateCredential z for template r   )r   �template_id)r   r   r   r   r    8   s    zTemplateCredential.__repr__N)r!   r"   r#   r$   r%   r&   r'   �
ForeignKeyr=   r(   r   r)   �descriptionr4   �
created_atr5   �relationshipr<   �templater    r   r   r   r   r6   -   s   r6   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�d	d�Zejejdd�Z
ejejdd�Zejejdd�Zejejdd�Ze�ej�Zejejd
d� d�Zejejdd� dd� d
�Zejdejddd�d�Zdd� ZdS )�TemplateCustomizationT)r   ztemplate_config.idF)r   �   z#1677FF)r,   r,   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   E   r0   zTemplateCustomization.<lambda>c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   F   r0   )r,   r9   r*   �
customization)Zuselist)r<   c             C   s   d| j � d�S )Nz$<TemplateCustomization for template r   )r=   )r   r   r   r   r    J   s    zTemplateCustomization.__repr__N)r!   r"   r#   r$   r%   r&   r'   r>   r=   r(   �
primary_color�layout_styler2   �show_shop_overview�show_balance�show_modules�show_statistics�Text�
custom_cssr4   r@   r5   rA   r<   rB   r    r   r   r   r   rC   ;   s   rC   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�dd�Zejejdd�Z
e�e�	d��Zejejd	d
� d�Zejejdd
� dd
� d
�Zejdejddd�d�Zejdddd�fZdd� ZdS )�TemplateContentT)r   ztemplate_config.idF)r   r+   r7   r8   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   T   r0   zTemplateContent.<lambda>)r,   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   U   r0   )r,   r9   r*   �contents)r;   )r<   r=   �content_keyZuix_template_content)�namec             C   s   d| j � d| j� d�S )Nz<TemplateContent z for template r   )rP   r=   )r   r   r   r   r    [   s    zTemplateContent.__repr__N)r!   r"   r#   r$   r%   r&   r'   r>   r=   r(   �content_typerP   rL   �
content_value�content_descriptionr4   r@   r5   rA   r<   rB   ZUniqueConstraintZ__table_args__r    r   r   r   r   rN   M   s   rN   c               @   s�   e Zd Zejejdd�Zejejdd�Zeje�	d�dd�Z
ejejdd�Zejejdd�Z
ejejdd�Zejejdd�Zejejdd� d�Zejejd	d� d	d� d
�Zdd� Zd
S )�
AnalyticsDataT)r   F)r   rD   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   g   r0   zAnalyticsData.<lambda>)r,   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   h   r0   )r,   r9   c             C   s   d| j � d| j� d�S )Nz<AnalyticsData � r   )�date�
time_range)r   r   r   r   r    j   s    zAnalyticsData.__repr__N)r!   r"   r#   r$   r%   r&   r'   ZDaterW   r(   rX   �Float�transaction_amount�order_count�
visitor_count�conversion_rater4   r@   r5   r    r   r   r   r   rU   _   s   rU   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zejejdd�Z	ejej
dd�Zejejdd�Zejej
dd� d�Zejd	ejd
dd�d�Zd
d� ZdS )�TransactionTrendT)r   zanalytics_data.idF)r   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   s   r0   zTransactionTrend.<lambda>)r,   rU   �transaction_trends)r;   )r<   c             C   s   d| j � d| j� d�S )Nz<TransactionTrend z hour:r   )�analytics_id�hour)r   r   r   r   r    w   s    zTransactionTrend.__repr__N)r!   r"   r#   r$   r%   r&   r'   r>   r`   ra   rY   �amountr[   r4   r@   rA   r<   �	analyticsr    r   r   r   r   r^   m   s   r^   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
ejejdd�Zejejdd�Z
ejejdd� d	�Zejd
ejddd�d
�Zdd� ZdS )�
UserSourceT)r   zanalytics_data.idF)r   r+   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   �   r0   zUserSource.<lambda>)r,   rU   �user_sources)r;   )r<   c             C   s   d| j � d| j� d�S )Nz<UserSource rV   r   )r`   �source_name)r   r   r   r   r    �   s    zUserSource.__repr__N)r!   r"   r#   r$   r%   r&   r'   r>   r`   r(   rf   r\   rY   r]   r4   r@   rA   r<   rc   r    r   r   r   r   rd   z   s   rd   c               @   s�   e Zd Zejejdd�Zejeje�d�dd�Zejejdd�Z	eje�
d�dd�Zeje�
d�dd�Zejej
dd�Zejejdd�Zejej
dd�Zejejdd	� d
�Zejdejddd
�d�Zdd� ZdS )�ProductSalesT)r   zanalytics_data.idF)r   r7   r8   c               C   s   t �tj�S )N)r   r-   r   r.   r   r   r   r   r/   �   r0   zProductSales.<lambda>)r,   rU   �
product_sales)r;   )r<   c             C   s   d| j � d| j� d�S )Nz<ProductSales z rank:r   )r`   �rank)r   r   r   r   r    �   s    zProductSales.__repr__N)r!   r"   r#   r$   r%   r&   r'   r>   r`   ri   r(   �product_name�
product_imagerY   �sales_amount�sales_countr]   r4   r@   rA   r<   rc   r    r   r   r   r   rg   �   s   rg   c             C   s   t j�tt| ��S )N)r$   r   �getr   �int)Zuser_idr   r   r   �	load_user�   s    rp   zalipay.htmlztiktok.htmlzqianniu.htmlzmeituan.html)r1   r3   u   警告: 模板文件 u
    不存在T�admin)r   Zadmin123)r   r)   )r=   �alipayZalipay_userZ123456u!   支付宝模板默认登录凭证)r=   r   r)   r?   �tiktokZtiktok_useru   TikTok模板默认登录凭证�qianniuZqianniu_useru   千牛模板默认登录凭证�meituanZmeituan_useru   美团模板默认登录凭证Zdefault_useru   默认登录凭证z#1677FFr,   )r=   rF   rG   z#FE2C55z#FF6A00z#FFD100�title�header_titleu   商家平台u   顶部导航栏标题)r=   rR   rP   rS   rT   �nav_item�
nav_workbenchu	   工作台u   导航栏工作台链接文字�nav_financeu   资金管理u!   导航栏资金管理链接文字�nav_reconciliationu   对账中心u!   导航栏对账中心链接文字�nav_productu   产品中心u!   导航栏产品中心链接文字�nav_datau   数据中心u!   导航栏数据中心链接文字�
nav_marketingu   营销中心u!   导航栏营销中心链接文字�placeholder�search_placeholderu   搜索功能/应用/服务u   顶部搜索框占位文字�sidebar_category�sidebar_app_centeru   应用中心u!   侧边栏应用中心分类标题�sidebar_item�sidebar_homeu   首页u   侧边栏首页菜单项�sidebar_shop_overviewu   店铺概况u   侧边栏店铺概况菜单项�sidebar_balanceu   余额u   侧边栏余额菜单项�sidebar_modulesu   模块u   侧边栏模块菜单项�sidebar_statisticsu   统计u   侧边栏统计菜单项�sidebar_product_serviceu   产品服务u!   侧边栏产品服务分类标题�sidebar_product_managementu   商品管理u   侧边栏商品管理菜单项�sidebar_order_managementu   订单管理u   侧边栏订单管理菜单项�
sidebar_badge�sidebar_order_badge�12u!   侧边栏订单管理徽章数字�sidebar_customer_serviceu   客服管理u   侧边栏客服管理菜单项�sidebar_trade_toolsu   交易工具u   侧边栏交易工具菜单项�sidebar_settingsu   设置中心u   侧边栏设置中心菜单项�sidebar_accountu   账户中心u   侧边栏账户中心菜单项�
section_title�
shop_titleu   支付宝小店铺u   支付宝小店铺区块标题�	link_text�promotion_linku   专属优惠活动进行中u   优惠活动链接文字r?   �shop_descriptionuE   公域流量变私域，私域用户沉淀，助力商家经营升级u   小店铺描述文字�subtitle�
method1_titleu   方式1：基于开发小程序u
   方式1标题�method1_descriptionu   适合有开发能力的商家u
   方式1描述�method1_guide_linku   小程序开发指南u   方式1指南链接文字�button�method1_buttonu   立即创建u   方式1按钮文字�
method2_titleu1   方式2：联系服务商，帮你搭建小程序u
   方式2标题�method2_descriptionuJ   联系小程序开发合作伙伴，有专门的服务商提供1对1服务u
   方式2描述�method2_buttonu   商家服务小程序u   方式2按钮文字�revenue_chart_titleu   收入趋势u   收入趋势图表标题�revenue_week_buttonu   本周u   收入趋势本周按钮文字�revenue_month_buttonu   本月u   收入趋势本月按钮文字�revenue_year_buttonu   全年u   收入趋势全年按钮文字�order_chart_titleu   订单分布u   订单分布图表标题�order_today_buttonu   今日u   订单分布今日按钮文字�order_yesterday_buttonu   昨日u   订单分布昨日按钮文字�order_week_buttonu   7天u   订单分布7天按钮文字�enterprise_account_titleu   支付宝企业账户u   支付宝企业账户标题�view_details_linku   查看明细u   查看明细链接文字�label�available_balance_labelu   可用余额(元)u   可用余额标签�unavailable_balance_labelu   不可用余额(元)u   不可用余额标签�deposit_balance_labelu   保证金余额(元)u   保证金余额标签�recharge_buttonu   充值u   充值按钮文字�transfer_buttonu   转账u   转账按钮文字�withdraw_buttonu   提现u   提现按钮文字�batch_payment_buttonu   批量付款（待开通）u   批量付款按钮文字�alipay_bill_titleu   支付宝账单u   支付宝账单标题�income_labelu   收入(元)u   收入标签�
expense_labelu   支出(元)u   支出标签�estimated_balance_labelu   预估余额(元)u   预估余额标签�bill_download_buttonu   账单下载u   账单下载按钮文字�invoice_management_buttonu   发票管理u   发票管理按钮文字�failure_appeal_buttonu   失败申诉u   失败申诉按钮文字�payment_coupon_titleu	   支付券u   支付券标题�payment_coupon_descriptionu$   优惠券，激发你的经营利器u   支付券描述�create_coupon_buttonu   创建开券u   创建开券按钮文字�edit_card_buttonu   编辑卡片u   编辑卡片按钮文字�return_old_version_buttonu   返回旧版首页u   返回旧版首页按钮文字�message_center_titleu   消息中心u   消息中心标题�view_all_messages_linku   全部 6u   查看全部消息链接文字z$/api/template_config/<template_name>c          
   C   s�   y�ddddg}| |krd} t j�tjd| � d��}t j�|�rht|ddd	��}t�|�}t	|�S Q R X nt	d
d| � d�i�d
fS W n2 t
k
r� } zt	d
t|�i�dfS d }~X Y nX d S )Nrr   rs   rt   ru   �configz_template.json�rzutf-8)�encoding�errorzTemplate configuration for z
 not foundi�  i�  )�os�path�join�app�
static_folder�exists�open�json�loadr	   �	Exception�str)r1   �valid_templates�config_path�fr�   �er   r   r   �get_template_config�  s    
r�   �POST)�methodsc          
   C   s  y�ddddg}| |kr,t dd| � �i�dfS tj}|sFt ddi�dfS d	d
ddd
ddg}x*|D ]"}||kr^t dd|� �i�dfS q^W tj�tjd| � d��}t|ddd��}tj	||ddd� W d Q R X t dd| � d�d��S  t
k
�r } zt dt|�i�dfS d }~X Y nX d S )Nrr   rs   rt   ru   r�   zInvalid template name: i�  zNo configuration data providedr=   r1   �versionZglobal_settingsZheader�sidebarZmain_contentzMissing required field: r�   z_template.json�wzutf-8)r�   F�   )Zensure_asciiZindentTzTemplate configuration for z saved successfully)Zsuccess�messagei�  )r	   r   r�   r�   r�   r�   r�   r�   r�   �dumpr�   r�   )r1   r�   �config_dataZrequired_fieldsZfieldr�   r�   r�   r   r   r   �save_template_config�  s"    
r�   �/c           
   C   s  �y�t j�d�} t�d�sb| sbtjjdd��� }|rR|j�	dd�}t
td|d��S t
tdd	d��S | �r�tj
�r�tj�t| �}|�r�tj�tj|j�}tj�|��r�d|jd
�}tjj|jd��� }|�r|j|d< |j|d
< |j|d< |j|d< |j|d< |j|d< |j|d< tjj|jd��� }|�rRi }x|D ]}	|	j ||	j!< �q4W ||d< |j�	dd�}tj�tj"d|� d��}
tj�|
��r�d|d< t#|jf|�S tjjdd��� }|�r�tj�tj|j�}tj�|��rxtjj|jd��� }i }|�r8|j|d< |j|d
< |j|d< |j|d< |j|d< |j|d< |j|d< tjj|jd��� }|�rzi }x|D ]}	|	j ||	j!< �q\W ||d< |j�	dd�}tj�tj"d|� d��}
tj�|
��rjd|d< y|t$|
ddd��d}t%�&|�}i }
x>|�di ��dg �D ]&}|�d�}|�r�|�dg �|
|< �q�W |
|d< d|d < W d Q R X W n6 t'k
�rh } zt(d!t)|�� �� W d d }~X Y nX t#|jf|�S t(d"|j� d#�� d$|_*tjjd%d��� }|�r�d|_*tj�+�  t#d%�S nt#d%�S W n: t'k
�r } zt(d&t)|�� �� t#d%�S d }~X Y nX d S )'NZpreview�template_logged_inT)r3   z.html� �template_login)r1   rr   )Z
is_previewZpreview_template_name)r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   z_template.jsonZhas_json_configr�   zutf-8)r�   r�   �
categoriesr'   �items�
sidebar_items�home�current_pagezError loading sidebar config: u   警告: 模板文件 u    不存在，使用默认模板Fzalipay.htmlu   错误: ),r   �argsrn   r   r*   �query�	filter_by�firstr1   �replacer   r   r   Zis_authenticatedr$   r�   r�   r�   r�   �template_folderr�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   �allrS   rP   r�   r   r�   r�   r�   r�   �printr�   r3   �commit)Zpreview_template_id�active_templater1   Zpreview_template�
template_path�template_contextrE   �template_contentsrO   �contentr�   r�   r�   r�   �category�category_idr�   �default_templater   r   r   �index�  s�    


















$

r�   z/login/<template_name>ZGETc             C   s�   ddddg}| |krd} t j�t j�| � d����� }|sdt jjdd��� }|sdtd� tt	d	��S t
jj|jd
��� }tj
dkr�tj�d�}tj�d
�}|r�||jkr�||jkr�dtd< | td< tt	d	��S td� td| � d��S )Nrr   rs   rt   ru   z.htmlzalipay.html)r1   u   模板配置错误r�   )r=   r�   r   r)   Tr�   r1   u   用户名或密码错误zlogin/z_login.html)r*   r�   �filterr1   Zliker�   r�   r   r   r   r6   r'   r   �method�formrn   r   r)   r   r   )r1   r�   rB   �
credentialr   r)   r   r   r   r�   g  s&    
r�   z/logoutc               C   s$   t �dd � t �dd � ttd��S )Nr�   r1   r�   )r   �popr   r   r   r   r   r   �template_logout�  s    r�   z/shop_overviewc               C   s   t �d�sttd��S td�S )Nr�   r�   zshop_overview.html)r   rn   r   r   r   r   r   r   r   �
shop_overview�  s    
r   z/balancec               C   s   t �d�sttd��S td�S )Nr�   r�   zbalance.html)r   rn   r   r   r   r   r   r   r   �balance�  s    
r  z/modulesc               C   s   t �d�sttd��S td�S )Nr�   r�   zmodules.html)r   rn   r   r   r   r   r   r   r   �modules�  s    
r  z/statisticsc               C   s   t �d�sttd��S td�S )Nr�   r�   zstatistics.html)r   rn   r   r   r   r   r   r   r   �
statistics�  s    
r  z/order_managementc        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �order_managementr�   �order_management.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  �  sF    










$r  z/product_managementc        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �product_managementr�   �product_management.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  �  sF    










$r  z/data_analysisc           
   C   s�  t �d�sttd��S tjjdd��� } i }y�tjjdd��� }|�r|j	|j
|j|jd�|d< t
jj|jd	��t
j��� }d
d� |D �|d< d
d� |D �|d< dd� |D �|d< tjj|jd	��� }dd� |D �|d< tjj|jd	��tj��� }dd� |D �|d< W n6 tk
�r> } ztdt|�� �� W d d }~X Y nX | �r�tjj| jd��� }|�r�|j|d< |j|d< |j|d< |j|d< |j|d< |j|d< |j|d< t jj| jd��� }|�r�i }	x|D ]}
|
j!|	|
j"< �q�W |	|d< y�t#j$�%t&j'dd �}t#j$�(|��rzt)|d!d"d#��\}t*�+|�}
i }x>|
�d$i ��d%g �D ]&}|�d&�}|�r>|�d'g �||< �q>W ||d(< W d Q R X W n6 tk
�r� } ztd)t|�� �� W d d }~X Y nX d*|d+< t,d-|�S ).Nr�   r�   T)r3   �today)rX   )rZ   r[   r\   r]   �analytics_data)r`   c             S   s   g | ]
}|j �qS r   )ra   )�.0�trendr   r   r   �
<listcomp>;  s    z!data_analysis.<locals>.<listcomp>Ztrend_hoursc             S   s   g | ]
}|j �qS r   )rb   )r
  r  r   r   r   r  <  s    Z
trend_amountsc             S   s   g | ]
}|j �qS r   )r[   )r
  r  r   r   r   r  =  s    Ztrend_ordersc             S   s   g | ]}|j |j|jd ��qS ))rQ   r\   r]   )rf   r\   r]   )r
  �sourcer   r   r   r  B  s   re   c          	   S   s*   g | ]"}|j |j|j|j|j|jd ��qS ))ri   rQ   �imagerb   �countr]   )ri   rj   rk   rl   rm   r]   )r
  �productr   r   r   r  L  s   rh   zError loading analytics data: )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �
data_analysisr�   �data_analysis.html)r  )-r   rn   r   r   r*   r�   r�   r�   rU   rZ   r[   r\   r]   r^   r'   �order_byra   r�   rd   rg   ri   r�   r�   r�   rC   rF   rG   rH   rI   rJ   rK   rM   rN   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )r�   r�   r	  r_   re   rh   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  #  sl    
$









$r  z/marketing_managementc        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �marketing_managementr�   �marketing_management.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  �  sF    










$r  z/customer_servicec        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �customer_servicer�   �customer_service.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  �  sF    










$r  z/financial_managementc        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �financial_managementr�   �financial_management.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  �  sF    










$r  z/reconciliation_centerc        
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }x|D ]}|j||j< q�W ||d
< y�tj�tjdd�}tj�|��r`t|ddd��\}t�|�}i }	x>|�di ��dg �D ]&}
|
�d�}|�r$|
�dg �|	|< �q$W |	|d< W d Q R X W n6 tk
�r� } ztdt |�� �� W d d }~X Y nX d|d< t!d|�S )Nr�   r�   T)r3   )r=   rF   rG   rH   rI   rJ   rK   rM   rO   r�   zalipay_template.jsonr�   zutf-8)r�   r�   r�   r'   r�   r�   zError loading sidebar config: �reconciliation_centerr�   �reconciliation_center.html)r  )"r   rn   r   r   r*   r�   r�   r�   rC   r'   rF   rG   rH   rI   rJ   rK   rM   rN   r�   rS   rP   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   )
r�   r�   rE   r�   rO   r�   r�   r�   r�   r�   r�   r�   r�   r   r   r   r  .  sF    










$r  z/admin/loginc              C   sf   t jdkr^t j�d�} t j�d�}tjj| d��� }|rV|j|krVt	|� t
td��S td� t
d�S )Nr�   r   r)   )r   �admin_dashboardu   用户名或密码错误zadmin/login.html)r   r�   r�   rn   r   r�   r�   r�   r)   r
   r   r   r   r   )r   r)   �userr   r   r   r   f  s    
z
/admin/logoutc               C   s   t �  ttd��S )Nr   )r   r   r   r   r   r   r   �admin_logoutw  s    r  z/admin/json_configc              C   s   t j�� } td| d�S )Nzadmin/json_config.html)�	templates)r*   r�   r�   r   )r  r   r   r   �admin_json_config~  s    
r   z/admin/friendly_configc           	   C   sn   t j�� } tj�d�}d }|rPyt|�}t j�|�}W n ttfk
rN   Y nX |s`| r`| d }t	d| |d�S )Nr=   r   zadmin/friendly_config.html)r  �selected_template)
r*   r�   r�   r   r�   rn   ro   �
ValueError�	TypeErrorr   )r  r=   r!  r   r   r   �admin_friendly_config�  s    
r$  z/admin/dashboardc              C   s�   t j�� t jjdd��� tj�� tj�� t�dd�t�dd�t	t�
dd�d	�t	t�
d
d�d	�d�} d
ddt�t
j�td	d� d�dddt�t
j�td
d� d�dddt�t
j�tdd� d�dddt�t
j�tddd� d�g}td| |d�S )NT)r3   i�  i�  r7   i�  �
   �   r�   �   �   )Ztotal_templatesr�   Ztotal_credentialsZtotal_customizationsZtotal_visitsZtotal_usersr]   Zavg_timeu   模板切换u-   将活跃模板切换为支付宝商家后台rq   )�hours)�actionr?   r  �	timestampu   凭证更新u$   更新了TikTok模板的登录凭证u   模板自定义u-   修改了支付宝模板的主题色和布局�   )�daysu   用户登录u   管理员登录系统�   )r-  r)  zadmin/dashboard.html)�stats�recent_activities)r*   r�   r  r�   r�   r6   rC   �random�randint�round�uniformr   r-   r   r.   r   r   )r/  r0  r   r   r   r  �  s2    

r  z/admin/credentialsc        	   
   C   s�  �y�t j�� } i }x.| D ]&}tjj|jd��� }|r|||j< qW tjdk�r�y�tj	�
d�}tj	�
d�}tj	�
d�}tj	�
d�}|r�|r�|s�td� tt
d��S tj�
t |�}|s�td	� tt
d��S tjj|jd��� }|�r||_||_||_t�tj�|_nt|j|||d
�}tj�|� tj��  td� tt
d��S  tk
�r� } z(tj��  tdt|�� �� tt
d��S d }~X Y nX td
| |d�S  tk
�r� } ztdt|�� �� tt
d��S d }~X Y nX d S )N)r=   r�   r=   r   r)   r?   u   请填写所有必填字段�admin_credentialsu   模板不存在)r=   r   r)   r?   u   凭证设置已更新u   错误: zadmin/credential_config.html)r  r:   u   系统错误: r  )r*   r�   r�   r6   r�   r'   r�   r   r�   r�   rn   r   r   r   r$   r   r   r)   r?   r   r-   r   r.   r5   �addr�   r�   �rollbackr�   r   )	r  r:   rB   r�   r=   r   r)   r?   r�   r   r   r   r5  �  sV    



r5  z/admin/templatesc           
      s  �y�t j�� } g � x\| D ]T}tj�tj|j�}tj�	|�s|j
rbd|_
t jjdd��� }|rbd|_
� �
|� qW x� D ]}tj�|� qvW | r�t� fdd�| D ��r�t ddd�}tj�|� tj��  t j�� } tjdk�r�y�tj�d	�}t j�t j
di� tj�t |�}|�rltj�tj|j�}tj�	|��rXd|_
t�tj�|_tj��  td
� ntd|j� d�� ntd
� ttd��S  t k
�r� } z(tj�!�  tdt"|�� �� ttd��S d }~X Y nX t#d| d�S  t k
�r } ztdt"|�� �� ttd��S d }~X Y nX d S )NFzalipay.html)r1   Tc             3   s   | ]}|� kV  qd S )Nr   )r
  rB   )�templates_to_remover   r   �	<genexpr>%  s    z"admin_templates.<locals>.<genexpr>)r1   r3   r�   r=   u   模板设置已更新u   错误: 模板文件 u
    不存在u   错误: 无效的模板ID�admin_templatesu   错误: zadmin/template_config.html)r  u   系统错误: r  )$r*   r�   r�   r�   r�   r�   r�   r�   r1   r�   r3   r�   r�   �appendr$   r   �deleter6  r�   r   r�   r�   rn   �updater   r-   r   r.   r5   r   r   r   r�   r7  r�   r   )r  rB   r�   r�   r=   r�   r   )r8  r   r:    sT    







r:  z/admin/contentc           F   C   s�  �yht j�� } i }tj�d�dk}|�r�tj��  tj	�
�  td� �x�| D �]�}d|jkrLt|j
ddddd	�t|j
d
ddd
d	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
dddd d	�t|j
d!d"d#d$d	�t|j
d%d&d'd(d	�t|j
d%d)d*d+d	�t|j
d%d,d-d.d	�t|j
d%d/d0d1d	�t|j
d%d2d3d4d	�t|j
d!d5d6d7d	�t|j
d%d8d9d:d	�t|j
d%d;d<d=d	�t|j
d>d?d@dAd	�t|j
d%dBdCdDd	�t|j
d%dEdFdGd	�t|j
d%dHdIdJd	�t|j
d%dKdLdMd	�t|j
dNdOdPdQd	�t|j
dRdSdTdUd	�t|j
dVdWdXdYd	�t|j
dZd[d\d]d	�t|j
dVd^d_d`d	�t|j
dRdadbdcd	�t|j
dddedfdgd	�t|j
dZdhdidjd	�t|j
dVdkdldmd	�t|j
dddndodpd	�t|j
dNdqdrdsd	�t|j
dddtdudvd	�t|j
dddwdxdyd	�t|j
dddzd{d|d	�t|j
dNd}d~dd	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dRd�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dVd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dRd�d�d�d	�g?}tj	�|� qLW tj	�
�  ttdσ�S x,| D ]$}tjj|j
dЍ�� }|||j
< �q�W tjd�k�r^tj�dҡ}|d�k�r^y�tj�dԡ}tj�dա}tj	�t |�}|�s�tdփ ttdσ�S xhtj�� D ]Z\}	}
|	�dס�r�|	�d�dء}tj	�t|�}|�r�|jt|�k�r�|
|_t�tj�|_�q�W tj	�
�  td�|� dڝ� ttdσ�S  tk
�r\ }
 z(tj	� �  td�t!|
�� �� ttdσ�S d }
~
X Y nX t"d�| |dݍS  tk
�r� }
 ztd�t!|
�� �� ttd߃�S d }
~
X Y nX d S )�NZrecreate�1u?   所有模板内容已重置，正在重新创建默认内容...rr   rv   rw   u   商家平台u   顶部导航栏标题)r=   rR   rP   rS   rT   rx   ry   u	   工作台u   导航栏工作台链接文字rz   u   资金管理u!   导航栏资金管理链接文字r{   u   对账中心u!   导航栏对账中心链接文字r|   u   产品中心u!   导航栏产品中心链接文字r}   u   数据中心u!   导航栏数据中心链接文字r~   u   营销中心u!   导航栏营销中心链接文字r   r�   u   搜索功能/应用/服务u   顶部搜索框占位文字r�   r�   u   应用中心u!   侧边栏应用中心分类标题r�   r�   u   首页u   侧边栏首页菜单项r�   u   店铺概况u   侧边栏店铺概况菜单项r�   u   余额u   侧边栏余额菜单项r�   u   模块u   侧边栏模块菜单项r�   u   统计u   侧边栏统计菜单项r�   u   产品服务u!   侧边栏产品服务分类标题r�   u   商品管理u   侧边栏商品管理菜单项r�   u   订单管理u   侧边栏订单管理菜单项r�   r�   r�   u!   侧边栏订单管理徽章数字r�   u   客服管理u   侧边栏客服管理菜单项r�   u   交易工具u   侧边栏交易工具菜单项r�   u   设置中心u   侧边栏设置中心菜单项r�   u   账户中心u   侧边栏账户中心菜单项r�   r�   u   支付宝小店铺u   支付宝小店铺区块标题r�   r�   u   专属优惠活动进行中u   优惠活动链接文字r?   r�   uE   公域流量变私域，私域用户沉淀，助力商家经营升级u   小店铺描述文字r�   r�   u   方式1：基于开发小程序u
   方式1标题r�   u   适合有开发能力的商家u
   方式1描述r�   u   小程序开发指南u   方式1指南链接文字r�   r�   u   立即创建u   方式1按钮文字r�   u1   方式2：联系服务商，帮你搭建小程序u
   方式2标题r�   uJ   联系小程序开发合作伙伴，有专门的服务商提供1对1服务u
   方式2描述r�   u   商家服务小程序u   方式2按钮文字r�   u   收入趋势u   收入趋势图表标题r�   u   本周u   收入趋势本周按钮文字r�   u   本月u   收入趋势本月按钮文字r�   u   全年u   收入趋势全年按钮文字r�   u   订单分布u   订单分布图表标题r�   u   今日u   订单分布今日按钮文字r�   u   昨日u   订单分布昨日按钮文字r�   u   7天u   订单分布7天按钮文字r�   u   支付宝企业账户u   支付宝企业账户标题r�   u   查看明细u   查看明细链接文字r�   r�   u   可用余额(元)u   可用余额标签r�   u   不可用余额(元)u   不可用余额标签r�   u   保证金余额(元)u   保证金余额标签r�   u   充值u   充值按钮文字r�   u   转账u   转账按钮文字r�   u   提现u   提现按钮文字r�   u   批量付款（待开通）u   批量付款按钮文字r�   u   支付宝账单u   支付宝账单标题r�   u   收入(元)u   收入标签r�   u   支出(元)u   支出标签r�   u   预估余额(元)u   预估余额标签r�   u   账单下载u   账单下载按钮文字r�   u   发票管理u   发票管理按钮文字r�   u   失败申诉u   失败申诉按钮文字r�   u	   支付券u   支付券标题r�   u$   优惠券，激发你的经营利器u   支付券描述r�   u   创建开券u   创建开券按钮文字r�   u   编辑卡片u   编辑卡片按钮文字r�   u   返回旧版首页u   返回旧版首页按钮文字r�   u   消息中心u   消息中心标题r�   u   全部 6u   查看全部消息链接文字�
admin_content)r=   r�   r*  Zupdate_contentr=   �content_sectionu   错误: 无效的模板IDZcontent_r�   u   模板内容已更新 (�)u   错误: zadmin/content_config.html)r  rO   u   系统错误: r  )#r*   r�   r�   r   r�   rn   rN   r<  r$   r   r�   r   r1   r'   �add_allr   r   r�   r�   r�   r�   �
startswithr�   r=   ro   rS   r   r-   r   r.   r5   r�   r7  r�   r   )r  rO   Zrecreate_contentrB   �default_contentsr�   r*  r=   r@  �key�valueZ
content_idr�   r�   r   r   r   r?  P  sT   







































































r?  z/admin/enhanced_templatesc           
      sH  �yt j�� } i }g � x\| D ]T}tj�tj|j�}tj�	|�s|j
rfd|_
t jjdd��� }|rfd|_
� �
|� qW x� D ]}tj�|� qzW | r�t� fdd�| D ��r�t ddd�}tj�|� tj��  t j�� } x�| D ]�}tjj|jd��� }|�r|||j< q�d	|jk�rd
}n:d|jk�r(d}n(d
|jk�r:d}nd|jk�rLd}nd
}t|j|dd�}tj�|� tj��  |||j< q�W tjdk�r�tj�dd�}|dk�r�y�tj�d�}t j�t j
di� tj�t |�}|�r8tj�tj|j�}tj�	|��r$d|_
t�tj�|_tj��  td� ntd|j� d�� ntd� t t!d��S  t"k
�r� }	 z(tj�#�  tdt$|	�� �� t t!d��S d }	~	X Y nX �n`|dk�r��y
tj�d�}tj�d�}tj�d�}
d tjk}d!tjk}d"tjk}
d#tjk}tj�d$d%�}tj�t |�}|�s(td� t t!d��S tjj|jd��� }|�sZt|jd�}tj�|� ||_%|
|_&||_'||_(|
|_)||_*||_+t�tj�|_tj��  td&� t t!d��S  t"k
�r� }	 z(tj�#�  tdt$|	�� �� t t!d��S d }	~	X Y nX t,d'| |d(�S  t"k
�rB }	 ztd)t$|	�� �� t t!d*��S d }	~	X Y nX d S )+NFzalipay.html)r1   Tc             3   s   | ]}|� kV  qd S )Nr   )r
  rB   )r8  r   r   r9  M  s    z+admin_enhanced_templates.<locals>.<genexpr>)r1   r3   )r=   rr   z#1677FFrs   z#FE2C55rt   z#FF6A00ru   z#FFD100r,   )r=   rF   rG   r�   r*  Zactivater=   u   模板设置已更新u   错误: 模板文件 u
    不存在u   错误: 无效的模板ID�admin_enhanced_templatesu   错误: Z	customizerF   rG   rH   rI   rJ   rK   rM   r�   u   模板自定义配置已更新z#admin/enhanced_template_config.html)r  �customizationsu   系统错误: r  )-r*   r�   r�   r�   r�   r�   r�   r�   r1   r�   r3   r�   r�   r;  r$   r   r<  r6  r�   rC   r'   r   r�   r�   rn   r=  r   r-   r   r.   r5   r   r   r   r�   r7  r�   rF   rG   rH   rI   rJ   rK   rM   r   )r  rH  rB   r�   r�   rE   rF   r*  r=   r�   rG   rH   rI   rJ   rK   rM   r   )r8  r   rG  3  s�    

















rG  z/api/analytics/<time_range>c          
   C   sZ  t �d�stddi�dfS ddddg}| |kr>tdd	i�d
fS y�tjj| d��� }|sftddi�d
fS tjj|jd��	tj
��� }tjj|jd��� }t
jj|jd��	t
j��� }|j|j|j|jd�dd� |D �dd� |D �dd� |D �d�dd� |D �dd� |D �d�}t|�S  tk
�rT } z"tdt|�� �� tddi�dfS d }~X Y nX d S )Nr�   r�   ZUnauthorizedi�  r  �	yesterday�week�monthzInvalid time rangei�  )rX   zData not foundi�  )r`   )rZ   r[   r\   r]   c             S   s   g | ]
}|j �qS r   )ra   )r
  r  r   r   r   r  �  s    z!get_analytics.<locals>.<listcomp>c             S   s   g | ]
}|j �qS r   )rb   )r
  r  r   r   r   r  �  s    c             S   s   g | ]
}|j �qS r   )r[   )r
  r  r   r   r   r  �  s    )r)  Zamounts�ordersc             S   s   g | ]}|j |j|jd ��qS ))rQ   r\   r]   )rf   r\   r]   )r
  r
  r   r   r   r  �  s   c          	   S   s*   g | ]"}|j |j|j|j|j|jd ��qS ))ri   rQ   r  rb   r  r]   )ri   rj   rk   rl   rm   r]   )r
  r  r   r   r   r  �  s   )ZoverviewZtransaction_trendre   rh   zError fetching analytics data: zInternal server errori�  )r   rn   r	   rU   r�   r�   r�   r^   r'   r  ra   r�   rd   rg   ri   rZ   r[   r\   r]   r�   r�   r�   )rX   Zvalid_rangesr	  r_   re   rh   Zresponser�   r   r   r   �
get_analytics�  s6    
rM  �__main__z0.0.0.0i�  )ZhostZportZthreaded�debug)hZflaskr   r   r   r   r   r   r   r	   Zflask_sqlalchemyr
   Zflask_loginr   r   r
   r   r   r   r�   r1  r�   r   r   r   r!   r�   �urandomZ
secret_keyr�   �abspath�dirname�__file__Zbasedirr�   r�   r$   Z
login_managerZinit_appZ
login_viewZModelr   r*   r6   rC   rN   rU   r^   rd   rg   Zuser_loaderrp   �app_context�
create_allr�   r�   Ztemplate_filesZtemplates_to_addZ
template_filer�   r�   r3   r;  r�   rB  r6  r�   rq   r�   r  rB   r'   r1   r�   rE   rD  r�   Zrouter�   r�   r�   r�   r�   r   r  r  r  r  r  r  r  r  r  r  r   r  r   r$  r  r5  r:  r?  rG  rM  �runr   r   r   r   �<module>   s  ( 

	











"


























































 &$88c8888$



->C c A