<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>finviz数据提取后台 - 登录</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: 'Noto Sans SC', "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f8fafc;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(30, 41, 59, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(30, 41, 59, 0.05) 0%, transparent 50%);
            background-size: 100% 100%;
            background-attachment: fixed;
        }
        .login-card {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            animation: fadeIn 0.6s ease-out;
        }
        .login-header {
            animation: slideDown 0.5s ease-out;
        }
        .login-form {
            animation: slideUp 0.5s ease-out;
        }
        .login-button {
            background-color: #1e293b;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .login-button:hover {
            background-color: #334155;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .login-button:active {
            transform: translateY(0);
        }
        .login-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.4);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%, -50%);
            transform-origin: 50% 50%;
        }
        .login-button:focus::after {
            animation: ripple 1s ease-out;
        }
        .social-login-btn {
            transition: all 0.3s ease;
        }
        .social-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .social-login-btn:active {
            transform: translateY(0);
        }
        .input-field {
            transition: all 0.3s ease;
        }
        .input-field:focus {
            border-color: #1e293b;
            box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
        }
        .logo-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideDown {
            from { transform: translateY(-20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 1;
            }
            20% {
                transform: scale(25, 25);
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(30, 41, 59, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(30, 41, 59, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(30, 41, 59, 0);
            }
        }

        .bg-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.03;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231e293b' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4 relative">
    <div class="bg-pattern"></div>

    <div class="login-card w-full max-w-md bg-white/90 rounded-xl overflow-hidden">
        <div class="p-8">
            <div class="login-header flex items-center justify-center mb-8">
                <div class="w-12 h-12 bg-slate-800 rounded-xl flex items-center justify-center text-white mr-3 logo-pulse shadow-lg">
                    <i class="ri-apps-2-line text-xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-slate-800">finviz数据提取后台</h1>
            </div>

            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg shadow-sm animate-pulse">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="ri-error-warning-line text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-red-600 text-sm font-medium">{{ messages[0] }}</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endwith %}

            <form method="post" action="{{ url_for('template_login', template_name='yehuo') }}" class="login-form">
                <div class="mb-6">
                    <label for="username" class="block text-sm font-medium text-slate-700 mb-2">用户名</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-user-line text-slate-400"></i>
                        </div>
                        <input type="text" id="username" name="username" required
                            class="input-field block w-full pl-10 pr-3 py-2.5 border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-0 sm:text-sm"
                            placeholder="请输入用户名">
                    </div>
                </div>

                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-slate-700 mb-2">密码</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="ri-lock-line text-slate-400"></i>
                        </div>
                        <input type="password" id="password" name="password" required
                            class="input-field block w-full pl-10 pr-3 py-2.5 border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-0 sm:text-sm"
                            placeholder="请输入密码">
                    </div>
                </div>

                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox"
                            class="h-4 w-4 text-slate-800 focus:ring-slate-500 border-slate-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-slate-600">
                            记住我
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-slate-700 hover:text-slate-900 transition-colors">
                            忘记密码?
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit"
                        class="login-button w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-md text-sm font-medium text-white focus:outline-none">
                        登录
                    </button>
                </div>
            </form>

            <div class="mt-8">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-slate-200"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-slate-500">
                            其他登录方式
                        </span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-3 gap-3">
                    <div>
                        <a href="#"
                            class="social-login-btn w-full inline-flex justify-center py-2.5 px-4 border border-slate-200 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-500 hover:bg-slate-50">
                            <i class="ri-wechat-line text-green-500 text-lg"></i>
                        </a>
                    </div>
                    <div>
                        <a href="#"
                            class="social-login-btn w-full inline-flex justify-center py-2.5 px-4 border border-slate-200 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-500 hover:bg-slate-50">
                            <i class="ri-qq-line text-blue-500 text-lg"></i>
                        </a>
                    </div>
                    <div>
                        <a href="#"
                            class="social-login-btn w-full inline-flex justify-center py-2.5 px-4 border border-slate-200 rounded-lg shadow-sm bg-white text-sm font-medium text-slate-500 hover:bg-slate-50">
                            <i class="ri-weibo-line text-red-500 text-lg"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="px-8 py-4 bg-slate-50 border-t border-slate-200 flex items-center justify-between">
            <span class="text-sm text-slate-500">还没有账号? <a href="#" class="text-slate-700 font-medium hover:text-slate-900 transition-colors">立即注册</a></span>
            <a href="{{ url_for('index') }}" class="text-sm text-slate-700 font-medium hover:text-slate-900 transition-colors">返回首页</a>
        </div>
    </div>

    <script>
        // 添加输入框焦点效果
        const inputs = document.querySelectorAll('.input-field');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('ring-2', 'ring-slate-200', 'ring-opacity-50');
            });
            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('ring-2', 'ring-slate-200', 'ring-opacity-50');
            });
        });
    </script>
</body>
</html>
