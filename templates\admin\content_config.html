<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板内容管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .content-preview {
            transition: all 0.2s ease;
        }
        .content-preview:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="ri-admin-line text-blue-600 text-2xl mr-2"></i>
                        <span class="font-bold text-xl text-gray-900">支付宝管理后台</span>
                    </div>
                    <nav class="ml-6 flex space-x-8">
                        <a href="{{ url_for('admin_dashboard') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            仪表盘
                        </a>
                        <a href="{{ url_for('admin_templates') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            模板管理
                        </a>
                        <a href="{{ url_for('admin_credentials') }}" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            凭证管理
                        </a>
                        <a href="{{ url_for('admin_content') }}" class="border-blue-500 text-blue-600 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            内容管理
                        </a>
                    </nav>
                </div>
                <div class="flex items-center">
                    <a href="{{ url_for('index') }}" target="_blank" class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="ri-eye-line mr-1"></i>
                        查看首页
                    </a>
                    <a href="{{ url_for('template_logout') }}" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="ri-logout-box-line mr-1"></i>
                        退出登录
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    模板内容管理
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    自定义模板中的文字、按钮和数字内容
                </p>
            </div>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">模板内容设置</h3>
                <div class="mt-2 max-w-xl text-sm text-gray-500">
                    <p>自定义模板中的文字、按钮和数字内容</p>
                </div>

                <div class="mt-5 border-t border-gray-200 pt-5">
                    <!-- 模板选择 -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <label for="template-select" class="block text-sm font-medium text-gray-700">选择要编辑的模板</label>
                            <a href="{{ url_for('admin_content', recreate=1) }}" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <i class="ri-refresh-line mr-1"></i>
                                重置所有内容
                            </a>
                        </div>
                        <select id="template-select" class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            {% for template in templates %}
                            <option value="{{ template.id }}" {% if template.is_active %}selected{% endif %}>
                                {% if 'yehuo' in template.template_name %}
                                    finviz数据提取后台
                                {% else %}
                                    {{ template.template_name }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- 内容分类标签页 -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-8">
                            <button id="tab-header" class="border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm content-tab-button">
                                顶部导航
                            </button>
                            <button id="tab-sidebar" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm content-tab-button">
                                侧边栏
                            </button>
                            <button id="tab-main" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm content-tab-button">
                                主内容区
                            </button>
                            <button id="tab-buttons" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm content-tab-button">
                                按钮文字
                            </button>
                            <button id="tab-numbers" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm content-tab-button">
                                数字内容
                            </button>
                        </nav>
                    </div>

                    {% for template in templates %}
                    <div id="content-form-{{ template.id }}" class="content-form {% if not template.is_active %}hidden{% endif %}">
                        <!-- 顶部导航内容 -->
                        <div id="content-header-{{ template.id }}" class="content-tab-content active">
                            <form method="post" action="{{ url_for('admin_content') }}" class="space-y-6">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <input type="hidden" name="content_section" value="header">

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    {% if template.id in contents and contents[template.id] %}
                                        {% for content in contents[template.id] if content.content_type in ['title', 'nav_item', 'placeholder'] %}
                                        <div class="sm:col-span-3">
                                            <label for="{{ content.content_key }}" class="block text-sm font-medium text-gray-700">{{ content.content_description }}</label>
                                            <div class="mt-1">
                                                <input type="text" id="{{ content.content_key }}" name="content_{{ content.id }}" value="{{ content.content_value }}"
                                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="sm:col-span-6">
                                            <p class="text-gray-500 text-center py-4">没有找到顶部导航内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class="sm:col-span-6">
                                        <p class="text-gray-500 text-center py-4">没有找到顶部导航内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存顶部导航内容
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 侧边栏内容 -->
                        <div id="content-sidebar-{{ template.id }}" class="content-tab-content hidden">
                            <form method="post" action="{{ url_for('admin_content') }}" class="space-y-6">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <input type="hidden" name="content_section" value="sidebar">

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    {% if template.id in contents and contents[template.id] %}
                                        {% for content in contents[template.id] if content.content_type in ['sidebar_category', 'sidebar_item', 'sidebar_badge'] %}
                                        <div class="sm:col-span-3">
                                            <label for="{{ content.content_key }}" class="block text-sm font-medium text-gray-700">{{ content.content_description }}</label>
                                            <div class="mt-1">
                                                <input type="text" id="{{ content.content_key }}" name="content_{{ content.id }}" value="{{ content.content_value }}"
                                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="sm:col-span-6">
                                            <p class="text-gray-500 text-center py-4">没有找到侧边栏内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class="sm:col-span-6">
                                        <p class="text-gray-500 text-center py-4">没有找到侧边栏内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存侧边栏内容
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 主内容区内容 -->
                        <div id="content-main-{{ template.id }}" class="content-tab-content hidden">
                            <form method="post" action="{{ url_for('admin_content') }}" class="space-y-6">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <input type="hidden" name="content_section" value="main">

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    {% if template.id in contents and contents[template.id] %}
                                        {% for content in contents[template.id] if content.content_type in ['section_title', 'subtitle', 'description', 'link_text', 'label'] %}
                                        <div class="sm:col-span-3">
                                            <label for="{{ content.content_key }}" class="block text-sm font-medium text-gray-700">{{ content.content_description }}</label>
                                            <div class="mt-1">
                                                <input type="text" id="{{ content.content_key }}" name="content_{{ content.id }}" value="{{ content.content_value }}"
                                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="sm:col-span-6">
                                            <p class="text-gray-500 text-center py-4">没有找到主内容区内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class="sm:col-span-6">
                                        <p class="text-gray-500 text-center py-4">没有找到主内容区内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存主内容区内容
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 按钮文字内容 -->
                        <div id="content-buttons-{{ template.id }}" class="content-tab-content hidden">
                            <form method="post" action="{{ url_for('admin_content') }}" class="space-y-6">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <input type="hidden" name="content_section" value="buttons">

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    {% if template.id in contents and contents[template.id] %}
                                        {% for content in contents[template.id] if content.content_type == 'button' %}
                                        <div class="sm:col-span-3">
                                            <label for="{{ content.content_key }}" class="block text-sm font-medium text-gray-700">{{ content.content_description }}</label>
                                            <div class="mt-1">
                                                <input type="text" id="{{ content.content_key }}" name="content_{{ content.id }}" value="{{ content.content_value }}"
                                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="sm:col-span-6">
                                            <p class="text-gray-500 text-center py-4">没有找到按钮文字内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class="sm:col-span-6">
                                        <p class="text-gray-500 text-center py-4">没有找到按钮文字内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存按钮文字内容
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 数字内容 -->
                        <div id="content-numbers-{{ template.id }}" class="content-tab-content hidden">
                            <form method="post" action="{{ url_for('admin_content') }}" class="space-y-6">
                                <input type="hidden" name="action" value="update_content">
                                <input type="hidden" name="template_id" value="{{ template.id }}">
                                <input type="hidden" name="content_section" value="numbers">

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                                    {% if template.id in contents and contents[template.id] %}
                                        {% for content in contents[template.id] if content.content_type == 'sidebar_badge' %}
                                        <div class="sm:col-span-3">
                                            <label for="{{ content.content_key }}" class="block text-sm font-medium text-gray-700">{{ content.content_description }}</label>
                                            <div class="mt-1">
                                                <input type="number" id="{{ content.content_key }}" name="content_{{ content.id }}" value="{{ content.content_value }}"
                                                    class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                            </div>
                                        </div>
                                        {% else %}
                                        <div class="sm:col-span-6">
                                            <p class="text-gray-500 text-center py-4">没有找到数字内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                    <div class="sm:col-span-6">
                                        <p class="text-gray-500 text-center py-4">没有找到数字内容配置。请点击 <a href="{{ url_for('admin_content', recreate=1) }}" class="text-blue-500 hover:underline">这里</a> 重新创建默认内容。</p>
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        保存数字内容
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 标签页切换功能
            const tabButtons = document.querySelectorAll('.content-tab-button');
            const tabContents = document.querySelectorAll('.content-tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有标签页的激活状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-blue-500', 'text-blue-600');
                        btn.classList.add('border-transparent', 'text-gray-500');
                    });

                    // 激活当前标签页
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-blue-500', 'text-blue-600');

                    // 获取当前选中的模板ID
                    const templateId = document.getElementById('template-select').value;

                    // 隐藏所有内容
                    document.querySelectorAll(`#content-form-${templateId} .content-tab-content`).forEach(content => {
                        content.classList.add('hidden');
                    });

                    // 显示对应内容
                    const contentId = 'content-' + this.id.split('-')[1] + '-' + templateId;
                    document.getElementById(contentId).classList.remove('hidden');
                });
            });

            // 模板选择功能
            const templateSelect = document.getElementById('template-select');
            const contentForms = document.querySelectorAll('.content-form');

            if (templateSelect) {
                templateSelect.addEventListener('change', function() {
                    const selectedTemplateId = this.value;

                    // 隐藏所有表单
                    contentForms.forEach(form => {
                        form.classList.add('hidden');
                    });

                    // 显示选中的模板表单
                    const selectedForm = document.getElementById('content-form-' + selectedTemplateId);
                    if (selectedForm) {
                        selectedForm.classList.remove('hidden');
                    }

                    // 重置标签页状态
                    tabButtons.forEach((btn, index) => {
                        if (index === 0) {
                            btn.classList.remove('border-transparent', 'text-gray-500');
                            btn.classList.add('border-blue-500', 'text-blue-600');
                        } else {
                            btn.classList.remove('border-blue-500', 'text-blue-600');
                            btn.classList.add('border-transparent', 'text-gray-500');
                        }
                    });

                    // 显示第一个标签页内容
                    document.querySelectorAll(`#content-form-${selectedTemplateId} .content-tab-content`).forEach((content, index) => {
                        if (index === 0) {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            }
        });
    </script>
</body>
</html>
