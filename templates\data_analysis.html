<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数据分析 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
      .chart-container {
        width: 100%;
        height: 300px;
      }
      button {
        transition: all 0.2s ease-in-out;
      }
      button:hover {
        transform: translateY(-1px);
      }
      button:active {
        transform: translateY(1px);
      }
      .text-3xl {
        transition: all 0.3s ease-in-out;
      }
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
      .pulse {
        animation: pulse 0.5s ease-in-out;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a href="/" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">工作台</a>
            <a href="/financial_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">资金管理</a>
            <a href="/reconciliation_center" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">对账中心</a>
            <a href="/product_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">产品中心</a>
            <a href="/data_analysis" class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all">数据中心</a>
            <a href="/marketing_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">营销中心</a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input type="text" placeholder="搜索功能/应用/服务" class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-notification-3-line text-xl"></i>
            <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all">
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">数据概览</h1>
            <p class="mt-1 text-sm text-gray-500">全面了解店铺运营情况，掌握关键业务指标</p>
          </div>

          <!-- 时间选择器 -->
          <div class="mb-6 flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 bg-primary text-white rounded-full text-sm">今日</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">昨日</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">近7天</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">近30天</button>
              <div class="relative">
                <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all flex items-center">
                  <span>自定义</span>
                  <i class="ri-arrow-down-s-line ml-1"></i>
                </button>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-download-line mr-1"></i>
                <span>导出数据</span>
              </button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-settings-3-line mr-1"></i>
                <span>设置</span>
              </button>
            </div>
          </div>

          <!-- 核心指标卡片 -->
          <div class="grid grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">交易金额</span>
                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center text-blue-500">
                  <i class="ri-money-cny-circle-line"></i>
                </div>
              </div>
              <div class="text-3xl font-medium text-gray-800">¥1,258,600.00</div>
              <div class="mt-2 text-sm flex items-center">
                <span class="text-green-500 flex items-center mr-2">
                  <i class="ri-arrow-up-s-line"></i>
                  18.7%
                </span>
                <span class="text-gray-500">较昨日</span>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">订单数</span>
                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center text-green-500">
                  <i class="ri-file-list-3-line"></i>
                </div>
              </div>
              <div class="text-3xl font-medium text-gray-800">15,642</div>
              <div class="mt-2 text-sm flex items-center">
                <span class="text-green-500 flex items-center mr-2">
                  <i class="ri-arrow-up-s-line"></i>
                  12.8%
                </span>
                <span class="text-gray-500">较昨日</span>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">访客数</span>
                <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center text-purple-500">
                  <i class="ri-user-line"></i>
                </div>
              </div>
              <div class="text-3xl font-medium text-gray-800">234,567</div>
              <div class="mt-2 text-sm flex items-center">
                <span class="text-green-500 flex items-center mr-2">
                  <i class="ri-arrow-up-s-line"></i>
                  5.6%
                </span>
                <span class="text-gray-500">较昨日</span>
              </div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">转化率</span>
                <div class="w-8 h-8 rounded-full bg-orange-50 flex items-center justify-center text-orange-500">
                  <i class="ri-percent-line"></i>
                </div>
              </div>
              <div class="text-3xl font-medium text-gray-800">8.75%</div>
              <div class="mt-2 text-sm flex items-center">
                <span class="text-green-500 flex items-center mr-2">
                  <i class="ri-arrow-up-s-line"></i>
                  1.2%
                </span>
                <span class="text-gray-500">较昨日</span>
              </div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="grid grid-cols-2 gap-6 mb-6">
            <!-- 交易趋势图 -->
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-base font-medium text-gray-800">交易趋势</h2>
                <div class="flex items-center space-x-2">
                  <button class="px-2 py-1 text-xs bg-primary text-white rounded-full">金额</button>
                  <button class="px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-full hover:bg-gray-50 transition-all">订单数</button>
                </div>
              </div>
              <div id="transactionChart" class="chart-container"></div>
            </div>
            <!-- 用户来源图 -->
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-base font-medium text-gray-800">用户来源</h2>
                <div class="flex items-center space-x-2">
                  <button class="px-2 py-1 text-xs bg-primary text-white rounded-full">访客数</button>
                  <button class="px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-full hover:bg-gray-50 transition-all">转化率</button>
                </div>
              </div>
              <div id="userSourceChart" class="chart-container"></div>
            </div>
          </div>

          <!-- 商品销售排行 -->
          <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-base font-medium text-gray-800">商品销售排行</h2>
              <a href="#" class="text-primary text-sm flex items-center">
                <span>查看更多</span>
                <i class="ri-arrow-right-s-line ml-1"></i>
              </a>
            </div>
            <table class="w-full">
              <thead class="border-b border-gray-200">
                <tr>
                  <th class="py-2 px-4 text-left text-xs font-medium text-gray-500">排名</th>
                  <th class="py-2 px-4 text-left text-xs font-medium text-gray-500">商品信息</th>
                  <th class="py-2 px-4 text-left text-xs font-medium text-gray-500">销售额</th>
                  <th class="py-2 px-4 text-left text-xs font-medium text-gray-500">销量</th>
                  <th class="py-2 px-4 text-left text-xs font-medium text-gray-500">转化率</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-100">
                <!-- 表格内容将由JavaScript动态生成 -->
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>

    <script>
      // 显示提示信息的函数
      function showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-md shadow-lg z-50 transition-opacity duration-300';

        // 根据类型设置样式
        if (type === 'success') {
          toast.classList.add('bg-green-500', 'text-white');
        } else if (type === 'error') {
          toast.classList.add('bg-red-500', 'text-white');
        } else {
          toast.classList.add('bg-gray-800', 'text-white');
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // 2秒后淡出并移除
        setTimeout(() => {
          toast.classList.add('opacity-0');
          setTimeout(() => {
            toast.remove();
          }, 300);
        }, 2000);
      }

      document.addEventListener('DOMContentLoaded', function() {
        // 交易趋势图
        const transactionChart = echarts.init(document.getElementById('transactionChart'));
        const transactionOption = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#e2e8f0',
            borderWidth: 1,
            textStyle: { color: '#1f2937' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
            axisLine: { lineStyle: { color: '#e5e7eb' } },
            axisLabel: { color: '#4b5563' }
          },
          yAxis: {
            type: 'value',
            axisLine: { show: false },
            axisLabel: { color: '#4b5563' },
            splitLine: { lineStyle: { color: '#f3f4f6' } }
          },
          series: [{
            name: '交易金额',
            type: 'line',
            smooth: true,
            data: [],  // 初始为空，将从API获取数据
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(22, 119, 255, 0.3)' },
                { offset: 1, color: 'rgba(22, 119, 255, 0.1)' }
              ])
            },
            itemStyle: { color: '#1677ff' },
            lineStyle: { width: 3, color: '#1677ff' }
          }]
        };
        transactionChart.setOption(transactionOption);

        // 用户来源图
        const userSourceChart = echarts.init(document.getElementById('userSourceChart'));
        const userSourceOption = {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#e2e8f0',
            borderWidth: 1,
            textStyle: { color: '#1f2937' }
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            textStyle: { color: '#4b5563' }
          },
          series: [{
            name: '访客来源',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: { show: false },
            emphasis: {
              label: { show: true, fontSize: '14', fontWeight: 'bold' }
            },
            labelLine: { show: false },
            data: []  // 初始为空，将从API获取数据
          }]
        };
        userSourceChart.setOption(userSourceOption);

        // 页面加载时自动获取今日数据
        fetch('/api/analytics/today')
          .then(response => {
            if (!response.ok) {
              throw new Error('网络请求失败');
            }
            return response.json();
          })
          .then(data => {
            // 更新图表数据
            transactionChart.setOption({
              xAxis: {
                data: data.transaction_trend.hours.map(hour => `${hour}:00`)
              },
              series: [{
                data: data.transaction_trend.amounts
              }]
            });

            // 更新用户来源图
            const sourceData = data.user_sources.map(source => {
              let color;
              switch(source.name) {
                case 'search_engine': color = '#1677ff'; break;
                case 'direct': color = '#4ade80'; break;
                case 'social': color = '#f97316'; break;
                case 'ads': color = '#8b5cf6'; break;
                case 'other': color = '#64748b'; break;
                default: color = '#1677ff';
              }

              // 转换API中的名称为中文显示
              let displayName;
              switch(source.name) {
                case 'search_engine': displayName = '搜索引擎'; break;
                case 'direct': displayName = '直接访问'; break;
                case 'social': displayName = '社交媒体'; break;
                case 'ads': displayName = '广告推广'; break;
                case 'other': displayName = '其他来源'; break;
                default: displayName = source.name;
              }

              return {
                value: source.visitor_count,
                name: displayName,
                itemStyle: { color: color }
              };
            });

            userSourceChart.setOption({
              series: [{
                data: sourceData
              }]
            });

            // 更新商品销售表格
            const tableBody = document.querySelector('tbody');
            if (tableBody && data.product_sales && data.product_sales.length > 0) {
              // 清空现有行
              tableBody.innerHTML = '';

              // 添加新行
              data.product_sales.forEach((product, index) => {
                const row = document.createElement('tr');

                // 创建排名单元格，使用不同的背景色
                let rankBgColor = '#1677ff';
                if (index === 1) rankBgColor = '#4096ff';
                if (index === 2) rankBgColor = '#69b1ff';

                row.innerHTML = `
                  <td class="py-3 px-4">
                    <div class="w-6 h-6 rounded-full text-white flex items-center justify-center text-xs" style="background-color: ${rankBgColor}">${index + 1}</div>
                  </td>
                  <td class="py-3 px-4">
                    <div class="flex items-center">
                      <img src="${product.image}" alt="${product.name}" class="w-10 h-10 rounded object-cover mr-3">
                      <div class="text-sm text-gray-800">${product.name}</div>
                    </div>
                  </td>
                  <td class="py-3 px-4">
                    <div class="text-sm font-medium text-gray-800">¥${product.amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                  </td>
                  <td class="py-3 px-4">
                    <div class="text-sm text-gray-800">${product.count.toLocaleString('zh-CN')}</div>
                  </td>
                  <td class="py-3 px-4">
                    <div class="text-sm text-gray-800">${product.conversion_rate.toFixed(1)}%</div>
                  </td>
                `;
                tableBody.appendChild(row);
              });
            }

            // 格式化数字
            const formatNumber = (num) => {
              return num.toLocaleString('zh-CN', {
                minimumFractionDigits: num % 1 === 0 ? 0 : 2,
                maximumFractionDigits: 2
              });
            };

            // 更新核心指标卡片数据
            const dataElements = document.querySelectorAll('.grid.grid-cols-4.gap-4.mb-6 .text-3xl');
            if (dataElements.length >= 4) {
              dataElements[0].textContent = `¥${formatNumber(data.overview.transaction_amount)}`;
              dataElements[1].textContent = formatNumber(data.overview.order_count);
              dataElements[2].textContent = formatNumber(data.overview.visitor_count);
              dataElements[3].textContent = `${data.overview.conversion_rate.toFixed(2)}%`;
            }
          })
          .catch(error => {
            console.error('获取数据失败:', error);
            showToast('获取数据失败，请稍后重试', 'error');
          });

        // 窗口大小变化时重新调整图表大小
        window.addEventListener('resize', function() {
          transactionChart.resize();
          userSourceChart.resize();
        });

        // 为时间选择器按钮添加点击事件
        // 使用更精确的选择器，确保只选择日期按钮
        const dateButtonsContainer = document.querySelector('.mb-6 .flex.items-center.justify-between .flex.items-center.space-x-2');
        if (dateButtonsContainer) {
          const timeButtons = dateButtonsContainer.querySelectorAll('button');
          console.log('找到日期按钮:', timeButtons.length);

          timeButtons.forEach(button => {
            button.addEventListener('click', function() {
              console.log('点击了日期按钮:', this.textContent.trim());

              // 移除所有按钮的选中状态
              timeButtons.forEach(btn => {
                btn.classList.remove('bg-primary', 'text-white');
                if (!btn.classList.contains('border-gray-200')) {
                  btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-700');
                }
              });

              // 设置当前按钮为选中状态
              this.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-700');
              this.classList.add('bg-primary', 'text-white');

              // 显示加载中提示
              showToast('正在加载数据...');

              // 立即调用更新函数，不使用setTimeout
              updateDataByTimeRange(this.textContent.trim());
            });
          });
        } else {
          console.error('未找到日期按钮容器');
        }

        // 为导出数据按钮添加点击事件
        document.querySelector('button:has(.ri-download-line)').addEventListener('click', function() {
          showToast('正在导出数据...');
          setTimeout(() => {
            showToast('数据导出成功！', 'success');
          }, 1500);
        });

        // 为设置按钮添加点击事件
        document.querySelector('button:has(.ri-settings-3-line)').addEventListener('click', function() {
          showToast('设置面板将在未来版本中推出');
        });

        // 为图表切换按钮添加点击事件
        const chartButtons = document.querySelectorAll('.bg-white.rounded-lg.shadow-sm.p-4 .flex.items-center.space-x-2 button');
        chartButtons.forEach(button => {
          button.addEventListener('click', function() {
            // 获取当前按钮所在的图表容器
            const chartContainer = this.closest('.bg-white.rounded-lg.shadow-sm.p-4');

            // 获取该容器内的所有按钮
            const buttons = chartContainer.querySelectorAll('button');

            // 移除所有按钮的选中状态
            buttons.forEach(btn => {
              btn.classList.remove('bg-primary', 'text-white');
              if (!btn.classList.contains('border-gray-200')) {
                btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-700');
              }
            });

            // 设置当前按钮为选中状态
            this.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-700');
            this.classList.add('bg-primary', 'text-white');

            // 显示加载中提示
            showToast('正在切换图表数据...');

            // 模拟数据加载
            setTimeout(() => {
              // 根据选择的图表类型更新数据
              if (chartContainer.contains(document.getElementById('transactionChart'))) {
                updateTransactionChart(this.textContent.trim());
              } else if (chartContainer.contains(document.getElementById('userSourceChart'))) {
                updateUserSourceChart(this.textContent.trim());
              }
            }, 500);
          });
        });

        // 为商品销售排行中的"查看更多"链接添加点击事件
        document.querySelector('.bg-white.rounded-lg.shadow-sm.p-4 a.text-primary').addEventListener('click', function(e) {
          e.preventDefault();
          showToast('商品销售详情将在未来版本中推出');
        });

        // showToast函数已在全局范围内定义

        // 根据时间范围更新数据的函数
        function updateDataByTimeRange(timeRange) {
          // 将中文时间范围转换为API参数
          let apiTimeRange;
          switch(timeRange) {
            case '今日':
              apiTimeRange = 'today';
              break;
            case '昨日':
              apiTimeRange = 'yesterday';
              break;
            case '近7天':
              apiTimeRange = 'week';
              break;
            case '近30天':
              apiTimeRange = 'month';
              break;
            default:
              console.error('未知的时间范围:', timeRange);
              showToast('无效的时间范围', 'error');
              return;
          }

          console.log('正在获取数据:', apiTimeRange);

          // 从API获取数据
          fetch(`/api/analytics/${apiTimeRange}`)
            .then(response => {
              console.log('API响应状态:', response.status);
              if (!response.ok) {
                throw new Error(`网络请求失败: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              console.log('获取到的数据:', data);

              // 更新图表数据
              transactionChart.setOption({
                xAxis: {
                  data: data.transaction_trend.hours.map(hour => `${hour}:00`)
                },
                series: [{
                  data: data.transaction_trend.amounts
                }]
              });

              // 更新用户来源图
              const sourceData = data.user_sources.map(source => {
                let color;
                switch(source.name) {
                  case 'search_engine': color = '#1677ff'; break;
                  case 'direct': color = '#4ade80'; break;
                  case 'social': color = '#f97316'; break;
                  case 'ads': color = '#8b5cf6'; break;
                  case 'other': color = '#64748b'; break;
                  default: color = '#1677ff';
                }

                // 转换API中的名称为中文显示
                let displayName;
                switch(source.name) {
                  case 'search_engine': displayName = '搜索引擎'; break;
                  case 'direct': displayName = '直接访问'; break;
                  case 'social': displayName = '社交媒体'; break;
                  case 'ads': displayName = '广告推广'; break;
                  case 'other': displayName = '其他来源'; break;
                  default: displayName = source.name;
                }

                return {
                  value: source.visitor_count,
                  name: displayName,
                  itemStyle: { color: color }
                };
              });

              userSourceChart.setOption({
                series: [{
                  data: sourceData
                }]
              });

              // 更新商品销售表格
              const tableBody = document.querySelector('tbody');
              if (tableBody && data.product_sales && data.product_sales.length > 0) {
                // 清空现有行
                tableBody.innerHTML = '';

                // 添加新行
                data.product_sales.forEach((product, index) => {
                  const row = document.createElement('tr');

                  // 创建排名单元格，使用不同的背景色
                  let rankBgColor = '#1677ff';
                  if (index === 1) rankBgColor = '#4096ff';
                  if (index === 2) rankBgColor = '#69b1ff';

                  row.innerHTML = `
                    <td class="py-3 px-4">
                      <div class="w-6 h-6 rounded-full text-white flex items-center justify-center text-xs" style="background-color: ${rankBgColor}">${index + 1}</div>
                    </td>
                    <td class="py-3 px-4">
                      <div class="flex items-center">
                        <img src="${product.image}" alt="${product.name}" class="w-10 h-10 rounded object-cover mr-3">
                        <div class="text-sm text-gray-800">${product.name}</div>
                      </div>
                    </td>
                    <td class="py-3 px-4">
                      <div class="text-sm font-medium text-gray-800">¥${product.amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                    </td>
                    <td class="py-3 px-4">
                      <div class="text-sm text-gray-800">${product.count.toLocaleString('zh-CN')}</div>
                    </td>
                    <td class="py-3 px-4">
                      <div class="text-sm text-gray-800">${product.conversion_rate.toFixed(1)}%</div>
                    </td>
                  `;
                  tableBody.appendChild(row);
                });
              }

              // 更新核心指标卡片数据
              const dataElements = document.querySelectorAll('.grid.grid-cols-4.gap-4.mb-6 .text-3xl');

              // 格式化数字
              const formatNumber = (num) => {
                return num.toLocaleString('zh-CN', {
                  minimumFractionDigits: num % 1 === 0 ? 0 : 2,
                  maximumFractionDigits: 2
                });
              };

              // 更新交易金额并添加动画效果
              dataElements[0].textContent = `¥${formatNumber(data.overview.transaction_amount)}`;
              dataElements[0].classList.add('pulse');
              setTimeout(() => { dataElements[0].classList.remove('pulse'); }, 500);

              // 更新订单数并添加动画效果
              dataElements[1].textContent = formatNumber(data.overview.order_count);
              dataElements[1].classList.add('pulse');
              setTimeout(() => { dataElements[1].classList.remove('pulse'); }, 500);

              // 更新访客数并添加动画效果
              dataElements[2].textContent = formatNumber(data.overview.visitor_count);
              dataElements[2].classList.add('pulse');
              setTimeout(() => { dataElements[2].classList.remove('pulse'); }, 500);

              // 更新转化率并添加动画效果
              dataElements[3].textContent = `${data.overview.conversion_rate.toFixed(2)}%`;
              dataElements[3].classList.add('pulse');
              setTimeout(() => { dataElements[3].classList.remove('pulse'); }, 500);

              showToast(`已更新为${timeRange}数据`, 'success');
            })
            .catch(error => {
              console.error('获取数据失败:', error);
              showToast('获取数据失败，请稍后重试', 'error');
            });
        }

        // 更新交易趋势图的函数
        function updateTransactionChart(chartType) {
          // 获取当前选中的时间范围
          const activeTimeButton = document.querySelector('.mb-6 .flex .space-x-2 button.bg-primary');
          if (!activeTimeButton) return;

          const timeRange = activeTimeButton.textContent.trim();
          let apiTimeRange;

          switch(timeRange) {
            case '今日':
              apiTimeRange = 'today';
              break;
            case '昨日':
              apiTimeRange = 'yesterday';
              break;
            case '近7天':
              apiTimeRange = 'week';
              break;
            case '近30天':
              apiTimeRange = 'month';
              break;
            default:
              return;
          }

          // 从API获取数据
          fetch(`/api/analytics/${apiTimeRange}`)
            .then(response => {
              if (!response.ok) {
                throw new Error('网络请求失败');
              }
              return response.json();
            })
            .then(data => {
              if (chartType === '金额') {
                transactionChart.setOption({
                  series: [{
                    name: '交易金额',
                    data: data.transaction_trend.amounts
                  }]
                });
              } else if (chartType === '订单数') {
                transactionChart.setOption({
                  series: [{
                    name: '订单数',
                    data: data.transaction_trend.orders
                  }]
                });
              }

              showToast(`已切换为${chartType}图表`, 'success');
            })
            .catch(error => {
              console.error('获取数据失败:', error);
              showToast('获取数据失败，请稍后重试', 'error');
            });
        }

        // 更新用户来源图的函数
        function updateUserSourceChart(chartType) {
          // 获取当前选中的时间范围
          const activeTimeButton = document.querySelector('.mb-6 .flex .space-x-2 button.bg-primary');
          if (!activeTimeButton) return;

          const timeRange = activeTimeButton.textContent.trim();
          let apiTimeRange;

          switch(timeRange) {
            case '今日':
              apiTimeRange = 'today';
              break;
            case '昨日':
              apiTimeRange = 'yesterday';
              break;
            case '近7天':
              apiTimeRange = 'week';
              break;
            case '近30天':
              apiTimeRange = 'month';
              break;
            default:
              return;
          }

          // 从API获取数据
          fetch(`/api/analytics/${apiTimeRange}`)
            .then(response => {
              if (!response.ok) {
                throw new Error('网络请求失败');
              }
              return response.json();
            })
            .then(data => {
              if (chartType === '访客数') {
                // 准备访客数据
                const sourceData = data.user_sources.map(source => {
                  let color;
                  let displayName;

                  switch(source.name) {
                    case 'search_engine':
                      color = '#1677ff';
                      displayName = '搜索引擎';
                      break;
                    case 'direct':
                      color = '#4ade80';
                      displayName = '直接访问';
                      break;
                    case 'social':
                      color = '#f97316';
                      displayName = '社交媒体';
                      break;
                    case 'ads':
                      color = '#8b5cf6';
                      displayName = '广告推广';
                      break;
                    case 'other':
                      color = '#64748b';
                      displayName = '其他来源';
                      break;
                    default:
                      color = '#1677ff';
                      displayName = source.name;
                  }

                  return {
                    value: source.visitor_count,
                    name: displayName,
                    itemStyle: { color: color }
                  };
                });

                userSourceChart.setOption({
                  series: [{
                    name: '访客来源',
                    data: sourceData
                  }]
                });
              } else if (chartType === '转化率') {
                // 准备转化率数据
                const sourceData = data.user_sources.map(source => {
                  let color;
                  let displayName;

                  switch(source.name) {
                    case 'search_engine':
                      color = '#1677ff';
                      displayName = '搜索引擎';
                      break;
                    case 'direct':
                      color = '#4ade80';
                      displayName = '直接访问';
                      break;
                    case 'social':
                      color = '#f97316';
                      displayName = '社交媒体';
                      break;
                    case 'ads':
                      color = '#8b5cf6';
                      displayName = '广告推广';
                      break;
                    case 'other':
                      color = '#64748b';
                      displayName = '其他来源';
                      break;
                    default:
                      color = '#1677ff';
                      displayName = source.name;
                  }

                  return {
                    value: source.conversion_rate,
                    name: displayName,
                    itemStyle: { color: color }
                  };
                });

                userSourceChart.setOption({
                  series: [{
                    name: '转化率',
                    data: sourceData
                  }]
                });
              }

              showToast(`已切换为${chartType}图表`, 'success');
            })
            .catch(error => {
              console.error('获取数据失败:', error);
              showToast('获取数据失败，请稍后重试', 'error');
            });
        }
      });
    </script>
  </body>
</html>
