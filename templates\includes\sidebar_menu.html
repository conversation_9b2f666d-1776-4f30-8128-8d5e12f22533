<!-- 统一的左侧菜单组件 -->
<style>
  /* 子菜单默认隐藏 */
  .submenu {
    display: none;
  }

  /* 子菜单展开时显示 */
  .submenu.show {
    display: block;
  }

  /* 菜单项高亮样式 */
  .sidebar-menu-item.active {
    background-color: #f3f4f6;
    color: #1f2937;
    font-weight: 500;
  }
</style>
<aside class="w-48 bg-white shadow-md flex-shrink-0">
  <div class="py-4">
    <!-- 首页 -->
    <a href="{{ url_for('index') }}" class="no-underline">
      <div class="px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer {% if current_page == 'dashboard' %}active{% endif %}">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-home-line"></i>
        </div>
        <span>首页</span>
      </div>
    </a>

    <!-- 用户管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="user-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-user-line"></i>
        </div>
        <span>用户管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="user-menu" class="submenu">
        <a href="{{ url_for('user_list') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'user_list' %}active{% endif %}">
            <span>用户列表</span>
          </div>
        </a>
        <a href="{{ url_for('banned_users') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'banned_users' %}active{% endif %}">
            <span>封禁用户列表</span>
          </div>
        </a>
      </div>
    </div>

    <!-- 敏感词管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="sensitive-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-spam-2-line"></i>
        </div>
        <span>敏感词管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="sensitive-menu" class="submenu">
        <a href="{{ url_for('sensitive_words') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'sensitive_words' %}active{% endif %}">
            <span>敏感词列表</span>
          </div>
        </a>
        <a href="{{ url_for('sensitive_word_hits') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'sensitive_word_hits' %}active{% endif %}">
            <span>敏感词命中</span>
          </div>
        </a>
      </div>
    </div>

    <!-- 群组管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="group-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-group-line"></i>
        </div>
        <span>群组管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="group-menu" class="submenu">
        <a href="{{ url_for('group_management') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'group_management' %}active{% endif %}">
            <span>群组列表</span>
          </div>
        </a>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>群组设置</span>
        </div>
      </div>
    </div>

    <!-- 消息管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="message-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-message-2-line"></i>
        </div>
        <span>消息管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="message-menu" class="submenu">
        <a href="{{ url_for('message_management') }}" class="no-underline">
          <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'message_management' %}active{% endif %}">
            <span>消息列表</span>
          </div>
        </a>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8 {% if current_page == 'user_messages' %}active{% endif %}">
          <span>用户消息</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>群发消息</span>
        </div>
      </div>
    </div>

    <!-- 机器人管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="robot-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-robot-line"></i>
        </div>
        <span>机器人管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="robot-menu" class="submenu">
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>机器人列表</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>机器人配置</span>
        </div>
      </div>
    </div>

    <!-- 频道管理 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="channel-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-broadcast-line"></i>
        </div>
        <span>频道管理</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="channel-menu" class="submenu">
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>频道列表</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>订阅者管理</span>
        </div>
      </div>
    </div>

    <!-- 设置 -->
    <div class="mt-2">
      <div class="menu-header px-4 py-2 flex items-center text-gray-700 hover:bg-gray-100 cursor-pointer" data-target="settings-menu">
        <div class="w-6 h-6 flex items-center justify-center mr-2">
          <i class="ri-settings-line"></i>
        </div>
        <span>设置</span>
        <div class="w-4 h-4 flex items-center justify-center ml-auto">
          <i class="ri-arrow-right-s-line menu-arrow"></i>
        </div>
      </div>
      <div id="settings-menu" class="submenu">
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>修改密码</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>基本设置</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>通知设置</span>
        </div>
        <div class="submenu-item sidebar-menu-item px-4 py-2 flex items-center text-gray-700 cursor-pointer pl-8">
          <span>安全设置</span>
        </div>
      </div>
    </div>
  </div>
</aside>
