<div class="flex min-h-[calc(100vh-64px)]">
  <!-- 左侧菜单 -->
  {% include 'layout_parts/sidebar.html' %}

  <!-- 主内容区 -->
  <main class="flex-1 p-6 bg-gray-50 overflow-auto">
    <div class="main-content-container max-w-7xl mx-auto">
      <!-- 支付宝小店铺 -->
      <div class="bg-white rounded shadow-sm mb-6">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-800">{{ contents.shop_title|default('支付宝小店铺') }}</h2>
            <a href="#" class="text-primary text-sm flex items-center">
              <span>{{ contents.promotion_link|default('专属优惠活动进行中') }}</span>
              <i class="ri-arrow-right-s-line ml-1"></i>
            </a>
          </div>
          <p class="text-gray-600 text-sm mb-4">
            {{ contents.shop_description|default('公域流量变私域，私域用户沉淀，助力商家经营升级') }}
          </p>
          <div class="grid grid-cols-2 gap-6">
            <div class="border border-gray-100 rounded p-4">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-base font-medium text-gray-700">
                  {{ contents.method1_title|default('方式1：基于开发小程序') }}
                </h3>
              </div>
              <p class="text-gray-600 text-sm mb-3">{{ contents.method1_description|default('适合有开发能力的商家') }}</p>
              <a href="#" class="text-primary text-sm flex items-center">
                <span>{{ contents.method1_guide_link|default('小程序开发指南') }}</span>
                <i class="ri-arrow-right-s-line ml-1"></i>
              </a>
              <div class="mt-4">
                <button
                  class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
                >
                  {{ contents.method1_button|default('立即创建') }}
                </button>
              </div>
            </div>
            <div class="border border-gray-100 rounded p-4">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-base font-medium text-gray-700">
                  {{ contents.method2_title|default('方式2：联系服务商，帮你搭建小程序') }}
                </h3>
              </div>
              <p class="text-gray-600 text-sm mb-3">
                {{ contents.method2_description|default('联系小程序开发合作伙伴，有专门的服务商提供1对1服务') }}
              </p>
              <div class="mt-4">
                <button
                  class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
                >
                  {{ contents.method2_button|default('商家服务小程序') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 数据概览图表 -->
      <div class="grid grid-cols-2 gap-6 mb-6">
        <div
          class="bg-white rounded shadow-sm hover:shadow-md transition-shadow duration-300"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-medium text-gray-800">{{ contents.revenue_chart_title|default('收入趋势') }}</h2>
              <div class="flex items-center space-x-2 revenue-chart-buttons">
                <button
                  class="px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90"
                  data-range="week"
                >
                  {{ contents.revenue_week_button|default('本周') }}
                </button>
                <button
                  class="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200"
                  data-range="month"
                >
                  {{ contents.revenue_month_button|default('本月') }}
                </button>
                <button
                  class="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200"
                  data-range="year"
                >
                  {{ contents.revenue_year_button|default('全年') }}
                </button>
              </div>
            </div>
            <div id="revenueChart" class="w-full h-64"></div>
          </div>
        </div>
        <div
          class="bg-white rounded shadow-sm hover:shadow-md transition-shadow duration-300"
        >
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-medium text-gray-800">{{ contents.order_chart_title|default('订单分布') }}</h2>
              <div class="flex items-center space-x-2 order-chart-buttons">
                <button
                  class="px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90"
                  data-range="today"
                >
                  {{ contents.order_today_button|default('今日') }}
                </button>
                <button
                  class="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200"
                  data-range="yesterday"
                >
                  {{ contents.order_yesterday_button|default('昨日') }}
                </button>
                <button
                  class="px-3 py-1 text-sm text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200"
                  data-range="week"
                >
                  {{ contents.order_week_button|default('7天') }}
                </button>
              </div>
            </div>
            <div id="orderChart" class="w-full h-64"></div>
          </div>
        </div>
      </div>
      <!-- 支付宝企业账户和支付宝账单 -->
      <div class="grid grid-cols-2 gap-6 mb-6">
        <!-- 支付宝企业账户 -->
        <div class="bg-white rounded shadow-sm">
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center">
                <h2 class="text-lg font-medium text-gray-800">
                  {{ contents.enterprise_account_title|default('支付宝企业账户') }}
                </h2>
                <div
                  class="w-5 h-5 flex items-center justify-center ml-1 text-gray-400"
                >
                  <i class="ri-information-line"></i>
                </div>
              </div>
              <a href="#" class="text-primary text-sm flex items-center">
                <span>{{ contents.view_details_link|default('查看明细') }}</span>
                <i class="ri-arrow-right-s-line ml-1"></i>
              </a>
            </div>
            <div class="grid grid-cols-3 gap-4 mb-6">
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1"
                    >{{ contents.available_balance_label|default('可用余额(元)') }}</span
                  >
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1"
                    >{{ contents.unavailable_balance_label|default('不可用余额(元)') }}</span
                  >
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1"
                    >{{ contents.deposit_balance_label|default('保证金余额(元)') }}</span
                  >
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
            </div>
            <div class="flex flex-wrap gap-2">
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.recharge_button|default('充值') }}
              </button>
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.transfer_button|default('转账') }}
              </button>
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.withdraw_button|default('提现') }}
              </button>
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.batch_payment_button|default('批量付款（待开通）') }}
              </button>
            </div>
          </div>
        </div>
        <!-- 支付宝账单 -->
        <div class="bg-white rounded shadow-sm">
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center">
                <h2 class="text-lg font-medium text-gray-800">
                  {{ contents.alipay_bill_title|default('支付宝账单') }}
                </h2>
                <div
                  class="w-5 h-5 flex items-center justify-center ml-1 text-gray-400"
                >
                  <i class="ri-information-line"></i>
                </div>
              </div>
              <a href="#" class="text-primary text-sm flex items-center">
                <span>{{ contents.view_details_link|default('查看明细') }}</span>
                <i class="ri-arrow-right-s-line ml-1"></i>
              </a>
            </div>
            <div class="grid grid-cols-3 gap-4 mb-6">
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1">{{ contents.income_label|default('收入(元)') }}</span>
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1">{{ contents.expense_label|default('支出(元)') }}</span>
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
              <div class="text-center">
                <div class="flex items-center justify-center mb-1">
                  <span class="text-gray-600 text-sm mr-1"
                    >{{ contents.estimated_balance_label|default('预估余额(元)') }}</span
                  >
                  <div
                    class="w-4 h-4 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-question-line"></i>
                  </div>
                </div>
                <div class="text-lg font-medium text-gray-800">******</div>
              </div>
            </div>
            <div class="flex flex-wrap gap-2">
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.bill_download_button|default('账单下载') }}
              </button>
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.invoice_management_button|default('发票管理') }}
              </button>
              <button
                class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
              >
                {{ contents.failure_appeal_button|default('失败申诉') }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 支付券 -->
      <div class="bg-white rounded shadow-sm mb-6">
        <div class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-800">{{ contents.payment_coupon_title|default('支付券') }}</h2>
          </div>
          <p class="text-gray-600 text-sm mb-4">{{ contents.payment_coupon_description|default('优惠券，激发你的经营利器') }}</p>
          <div class="mt-4">
            <button
              class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
            >
              {{ contents.create_coupon_button|default('创建开券') }}
            </button>
          </div>
        </div>
      </div>
      <!-- 底部卡片 -->
      <div class="bg-white rounded shadow-sm">
        <div class="p-6 flex items-center justify-between">
          <button
            class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
          >
            {{ contents.edit_card_button|default('编辑卡片') }}
          </button>
          <button
            class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-button text-sm hover:bg-gray-50 !rounded-button whitespace-nowrap"
          >
            {{ contents.return_old_version_button|default('返回旧版首页') }}
          </button>
        </div>
      </div>
    </div>
  </main>
  <!-- 右侧信息栏 -->
  {% include 'layout_parts/rightbar.html' %}
</div>