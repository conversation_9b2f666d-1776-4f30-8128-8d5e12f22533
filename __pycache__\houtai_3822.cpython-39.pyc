a

    ��0h"� �                G   @   s*  d dl mZmZmZmZmZmZmZmZ d dl	m
Z
 d dlmZm
Z
mZmZmZmZ d dlZd dlZd dlZd dlmZmZmZ ee�Ze�d�e_d dlZej�ej�e��Z dej�!e d� ej"d	< d
ej"d< e
e�Z#e� Z$e$�%e� de$_&G d
d� de
e#j'�Z(G dd� de#j'�Z)G dd� de#j'�Z*G dd� de#j'�Z+G dd� de#j'�Z,G dd� de#j'�Z-G dd� de#j'�Z.G dd� de#j'�Z/G dd� de#j'�Z0e$j1dd � �Z2e�3� ��T e#�4�  e)j5�6� �sHg d!�Z7g Z8e7D ]LZ9ej�:ej�!ej;e9���re9d"kZ<e8�=e)e9e<d#�� ne>d$e9� d%�� �q�e8�r4e#j�?e8� ne#j�@e)d"d&d#�� e(j5jAd'd(��6� �ste(d'd)d*�ZBe#j�@eB� e)j5�C� ZDeDD �]bZEe*j5jAeEjFd+��6� �s\d,eEjGv �r�e*eEjFd-d.d/d0�ZHn�d1eEjGv �r�e*eEjFd2d.d3d0�ZHnrd4eEjGv �r�e*eEjFd5d.d6d0�ZHnRd7eEjGv �re*eEjFd8d.d9d0�ZHn2d:eEjGv �r>e*eEjFd;d.d<d0�ZHne*eEjFd=d.d>d0�ZHe#j�@eH� e+j5jAeEjFd+��6� �s�d,eEjGv �r�e+eEjFd?d@dA�ZIn�d1eEjGv �r�e+eEjFdBd@dA�ZInjd4eEjGv �r�e+eEjFdCd@dA�ZInLd7eEjGv �r�e+eEjFdDd@dA�ZIn.d:eEjGv �re+eEjFdEd@dA�ZIne+eEjFd?d@dA�ZIe#j�@eI� d,eEjGv �r�e,j5jAeEjFd+��6� �s�e,eEjFdFdGdHdIdJ�e,eEjFdKdLdMdNdJ�e,eEjFdKdOdPdQdJ�e,eEjFdKdRdSdTdJ�e,eEjFdKdUdVdWdJ�e,eEjFdKdXdYdZdJ�e,eEjFdKd[d\d]dJ�e,eEjFd^d_d`dadJ�e,eEjFdbdcdddedJ�e,eEjFdfdgdhdidJ�e,eEjFdfdjdkdldJ�e,eEjFdfdmdndodJ�e,eEjFdfdpdqdrdJ�e,eEjFdfdsdtdudJ�e,eEjFdbdvdwdxdJ�e,eEjFdfdydzd{dJ�e,eEjFdfd|d}d~dJ�e,eEjFdd�d�d�dJ�e,eEjFdfd�d�d�dJ�e,eEjFdfd�d�d�dJ�e,eEjFdfd�d�d�dJ�e,eEjFdfd�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d�d�dJ�e,eEjFd�d�d��d dJ�e,eEjFd��d�d�ddJ�e,eEjFd��d�d�ddJ�e,eEjFd��d�d�d	dJ�e,eEjFd��d
�d�ddJ�e,eEjFd��d
�d�ddJ�g?ZJe#j�?eJ� �q�e#j�K�  W d  � n1 �	s0    Y  e�L�d��d�d� �ZMejL�d�dg�d�e�d�d� ��ZNe�L�d��d�d� �ZOejL�d�d�dg�d��d�d� �ZPe�L�d��d�d � �ZQe�L�d!��d"�d#� �ZRe�L�d$��d%�d&� �ZSe�L�d'��d(�d)� �ZTe�L�d*��d+�d,� �ZUe�L�d-��d.�d/� �ZVe�L�d0��d1�d2� �ZWe�L�d3��d4�d5� �ZXe�L�d6��d7�d8� �ZYe�L�d9��d:�d;� �ZZe�L�d<��d=�d>� �Z[e�L�d?��d@�dA� �Z\ejL�dB�d�dg�d��dCd� �Z]e�L�dD�e�dE�dF� ��Z^e�L�dG�e�dH�dI� ��Z_e�L�dJ�e�dK�dL� ��Z`e�L�dM�e�dN�dO� ��ZaejL�dP�d�dg�d�e�dQ�dR� ��ZbejL�dS�d�dg�d�e�dT�dU� ��ZcejL�dV�d�dg�d�e�dW�dX� ��ZdejL�dY�d�dg�d�e�dZ�d[� ��Zee�L�d\��d]�d^� �Zfe�d_k�r&ejg�d`�dad&d
�db� dS (c  �    )�Flask�render_template�request�redirect�url_for�flash�session�jsonify)�
SQLAlchemy)�LoginManager�	UserMixin�
login_user�login_required�logout_user�current_userN)�datetime�timezone�	timedelta�   z
sqlite:///zdata.sqliteZSQLALCHEMY_DATABASE_URIFZSQLALCHEMY_TRACK_MODIFICATIONS�admin_loginc                   @   sN   e Zd Zejejdd�Zeje�d�ddd�Zeje�d�dd�Z	dd	� Z
d
S )�UserT�Zprimary_key�P   F)�unique�nullable�x   �r   c                 C   s   d| j � d�S )Nz<User �>��username��self� r"   �LC:\Users\<USER>\.PyCharm2018.3\config\scratches\houtai\houtai_3822.py�__repr__!   s    z
User.__repr__N)�__name__�
__module__�__qualname__�db�Column�Integer�id�Stringr   �passwordr$   r"   r"   r"   r#   r      s   r   c                   @   s\   e Zd Zejejdd�Zeje�d�dd�Zejej	dd�Z
ejejdd� d�Zd	d
� Z
dS )�TemplateConfigTr   �2   Fr   ��defaultc                   C   s   t �tj�S �N�r   �nowr   �utcr"   r"   r"   r#   �<lambda>(   �    zTemplateConfig.<lambda>c                 C   s   d| j � d�S )Nz<TemplateConfig r   ��
template_namer    r"   r"   r#   r$   *   s    zTemplateConfig.__repr__N)r%   r&   r'   r(   r)   r*   r+   r,   r9   �Boolean�	is_active�DateTime�
updated_atr$   r"   r"   r"   r#   r.   $   s
   r.   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�dd�Ze�e�	d��Zejej
d	d
� d�Zejej
dd
� dd
� d
�Zejdejddd�d�Zdd� ZdS )�TemplateCredentialTr   �template_config.idFr   r/   �d   ��   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   3   r7   zTemplateCredential.<lambda>r0   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   4   r7   �r1   Zonupdater.   �credentials�Zlazy��backrefc                 C   s   d| j � d| j� d�S )Nz<TemplateCredential � for template r   )r   �template_idr    r"   r"   r#   r$   8   s    zTemplateCredential.__repr__N)r%   r&   r'   r(   r)   r*   r+   �
ForeignKeyrH   r,   r   r-   �descriptionr<   �
created_atr=   �relationshiprF   �templater$   r"   r"   r"   r#   r>   -   s   r>   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�d	d�Zejejdd�Z
ejejdd�Zejejdd�Zejejdd�Ze�ej�Zejejd
d� d�Zejejdd� dd� d
�Zejdejddd�d�Zdd� ZdS )�TemplateCustomizationTr   r?   Fr   �   �#1677FFr0   r1   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   E   r7   zTemplateCustomization.<lambda>c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   F   r7   rB   r.   �
customization)ZuselistrE   c                 C   s   d| j � d�S )Nz$<TemplateCustomization for template r   �rH   r    r"   r"   r#   r$   J   s    zTemplateCustomization.__repr__N)r%   r&   r'   r(   r)   r*   r+   rI   rH   r,   �
primary_color�layout_styler:   �show_shop_overview�show_balance�show_modules�show_statistics�Text�
custom_cssr<   rK   r=   rL   rF   rM   r$   r"   r"   r"   r#   rN   ;   s   rN   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
eje�	d�dd�Zejejdd�Z
e�e�	d��Zejejd	d
� d�Zejejdd
� dd
� d
�Zejdejddd�d�Zejdddd�fZdd� ZdS )�TemplateContentTr   r?   Fr   r/   r@   rA   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   T   r7   zTemplateContent.<lambda>r0   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   U   r7   rB   r.   �contentsrD   rE   rH   �content_keyZuix_template_content)�namec                 C   s   d| j � d| j� d�S )Nz<TemplateContent rG   r   )r]   rH   r    r"   r"   r#   r$   [   s    zTemplateContent.__repr__N)r%   r&   r'   r(   r)   r*   r+   rI   rH   r,   �content_typer]   rY   �
content_value�content_descriptionr<   rK   r=   rL   rF   rM   ZUniqueConstraintZ__table_args__r$   r"   r"   r"   r#   r[   M   s   r[   c                   @   s�   e Zd Zejejdd�Zejejdd�Zeje�	d�dd�Z
ejejdd�Zejejdd�Z
ejejdd�Zejejdd�Zejejdd� d�Zejejd	d� d	d� d
�Zdd� Zd
S )�
AnalyticsDataTr   Fr   rO   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   g   r7   zAnalyticsData.<lambda>r0   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   h   r7   rB   c                 C   s   d| j � d| j� d�S )Nz<AnalyticsData � r   )�date�
time_ranger    r"   r"   r#   r$   j   s    zAnalyticsData.__repr__N)r%   r&   r'   r(   r)   r*   r+   ZDaterd   r,   re   �Float�transaction_amount�order_count�
visitor_count�conversion_rater<   rK   r=   r$   r"   r"   r"   r#   rb   _   s   rb   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zejejdd�Z	ejej
dd�Zejejdd�Zejej
dd� d�Zejd	ejd
dd�d�Zd
d� ZdS )�TransactionTrendTr   �analytics_data.idFr   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   s   r7   zTransactionTrend.<lambda>r0   rb   �transaction_trendsrD   rE   c                 C   s   d| j � d| j� d�S )Nz<TransactionTrend z hour:r   )�analytics_id�hourr    r"   r"   r#   r$   w   s    zTransactionTrend.__repr__N)r%   r&   r'   r(   r)   r*   r+   rI   rn   ro   rf   �amountrh   r<   rK   rL   rF   �	analyticsr$   r"   r"   r"   r#   rk   m   s   rk   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zeje�	d�dd�Z
ejejdd�Zejejdd�Z
ejejdd� d	�Zejd
ejddd�d
�Zdd� ZdS )�
UserSourceTr   rl   Fr   r/   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   �   r7   zUserSource.<lambda>r0   rb   �user_sourcesrD   rE   c                 C   s   d| j � d| j� d�S )Nz<UserSource rc   r   )rn   �source_namer    r"   r"   r#   r$   �   s    zUserSource.__repr__N)r%   r&   r'   r(   r)   r*   r+   rI   rn   r,   rt   ri   rf   rj   r<   rK   rL   rF   rq   r$   r"   r"   r"   r#   rr   z   s   rr   c                   @   s�   e Zd Zejejdd�Zejeje�d�dd�Zejejdd�Z	eje�
d�dd�Zeje�
d�dd�Zejej
dd�Zejejdd�Zejej
dd�Zejejdd	� d
�Zejdejddd
�d�Zdd� ZdS )�ProductSalesTr   rl   Fr   r@   rA   c                   C   s   t �tj�S r2   r3   r"   r"   r"   r#   r6   �   r7   zProductSales.<lambda>r0   rb   �
product_salesrD   rE   c                 C   s   d| j � d| j� d�S )Nz<ProductSales z rank:r   )rn   �rankr    r"   r"   r#   r$   �   s    zProductSales.__repr__N)r%   r&   r'   r(   r)   r*   r+   rI   rn   rw   r,   �product_name�
product_imagerf   �sales_amount�sales_countrj   r<   rK   rL   rF   rq   r$   r"   r"   r"   r#   ru   �   s   ru   c                 C   s   t j�tt| ��S r2   )r(   r   �getr   �int)Zuser_idr"   r"   r#   �	load_user�   s    r~   )�alipay.htmlztiktok.htmlzqianniu.htmlzmeituan.htmlz
yehuo.htmlr   �r9   r;   �   警告: 模板文件 �
    不存在T�adminr   Zadmin123)r   r-   rR   �alipayZalipay_userZ123456u!   支付宝模板默认登录凭证�rH   r   r-   rJ   �tiktokZtiktok_useru   TikTok模板默认登录凭证�qianniuZqianniu_useru   千牛模板默认登录凭证�meituanZmeituan_useru   美团模板默认登录凭证�yehuoZ
yehuo_useru    野火IM模板默认登录凭证Zdefault_useru   默认登录凭证rP   r1   �rH   rS   rT   �#FE2C55�#FF6A00�#FFD100z#1e293b�title�header_title�   商家平台�   顶部导航栏标题�rH   r_   r]   r`   ra   �nav_item�
nav_workbench�	   工作台�   导航栏工作台链接文字�nav_finance�   资金管理�!   导航栏资金管理链接文字�nav_reconciliation�   对账中心�!   导航栏对账中心链接文字�nav_product�   产品中心�!   导航栏产品中心链接文字�nav_data�   数据中心�!   导航栏数据中心链接文字�
nav_marketing�   营销中心�!   导航栏营销中心链接文字�placeholder�search_placeholder�   搜索功能/应用/服务�   顶部搜索框占位文字�sidebar_category�sidebar_app_center�   应用中心�!   侧边栏应用中心分类标题�sidebar_item�sidebar_home�   首页�   侧边栏首页菜单项�sidebar_shop_overview�   店铺概况�   侧边栏店铺概况菜单项�sidebar_balance�   余额�   侧边栏余额菜单项�sidebar_modules�   模块�   侧边栏模块菜单项�sidebar_statistics�   统计�   侧边栏统计菜单项�sidebar_product_service�   产品服务�!   侧边栏产品服务分类标题�sidebar_product_management�   商品管理�   侧边栏商品管理菜单项�sidebar_order_management�   订单管理�   侧边栏订单管理菜单项�
sidebar_badge�sidebar_order_badge�12�!   侧边栏订单管理徽章数字�sidebar_customer_service�   客服管理�   侧边栏客服管理菜单项�sidebar_trade_tools�   交易工具�   侧边栏交易工具菜单项�sidebar_settings�   设置中心�   侧边栏设置中心菜单项�sidebar_account�   账户中心�   侧边栏账户中心菜单项�
section_title�
shop_title�   支付宝小店铺�   支付宝小店铺区块标题�	link_text�promotion_link�   专属优惠活动进行中�   优惠活动链接文字rJ   �shop_description�E   公域流量变私域，私域用户沉淀，助力商家经营升级�   小店铺描述文字�subtitle�
method1_title�   方式1：基于开发小程序�
   方式1标题�method1_description�   适合有开发能力的商家�
   方式1描述�method1_guide_link�   小程序开发指南�   方式1指南链接文字�button�method1_button�   立即创建�   方式1按钮文字�
method2_title�1   方式2：联系服务商，帮你搭建小程序�
   方式2标题�method2_description�J   联系小程序开发合作伙伴，有专门的服务商提供1对1服务�
   方式2描述�method2_button�   商家服务小程序�   方式2按钮文字�revenue_chart_title�   收入趋势�   收入趋势图表标题�revenue_week_button�   本周�   收入趋势本周按钮文字�revenue_month_button�   本月�   收入趋势本月按钮文字�revenue_year_button�   全年�   收入趋势全年按钮文字�order_chart_title�   订单分布�   订单分布图表标题�order_today_button�   今日�   订单分布今日按钮文字�order_yesterday_button�   昨日�   订单分布昨日按钮文字�order_week_button�   7天�   订单分布7天按钮文字�enterprise_account_title�   支付宝企业账户�   支付宝企业账户标题�view_details_link�   查看明细�   查看明细链接文字�label�available_balance_label�   可用余额(元)�   可用余额标签�unavailable_balance_label�   不可用余额(元)�   不可用余额标签�deposit_balance_label�   保证金余额(元)�   保证金余额标签�recharge_button�   充值�   充值按钮文字�transfer_button�   转账�   转账按钮文字�withdraw_button�   提现�   提现按钮文字�batch_payment_button�   批量付款（待开通）�   批量付款按钮文字�alipay_bill_title�   支付宝账单�   支付宝账单标题�income_label�   收入(元)�   收入标签�
expense_label�   支出(元)�   支出标签�estimated_balance_label�   预估余额(元)�   预估余额标签�bill_download_button�   账单下载�   账单下载按钮文字�invoice_management_button�   发票管理�   发票管理按钮文字�failure_appeal_button�   失败申诉�   失败申诉按钮文字�payment_coupon_title�	   支付券�   支付券标题�payment_coupon_description�$   优惠券，激发你的经营利器�   支付券描述�create_coupon_button�   创建开券�   创建开券按钮文字�edit_card_button�   编辑卡片�   编辑卡片按钮文字�return_old_version_button�   返回旧版首页�   返回旧版首页按钮文字�message_center_title�   消息中心�   消息中心标题�view_all_messages_link�   全部 6�   查看全部消息链接文字z$/api/template_config/<template_name>c              
   C   s�   z�g d�}| |vrd} t j�tjd| � d��}t j�|�r~t|ddd��$}t�|�}t	|�W  d   � W S 1 sr0    Y  nt	dd	| � d
�i�dfW S W n8 t
y� } z t	dt|�i�dfW  Y d }~S d }~0 0 d S )
N�r�   r�   r�   r�   r�   r�   �config�_template.json�r�utf-8��encoding�error�Template configuration for z
 not found�  ��  )�os�path�join�app�
static_folder�exists�open�json�loadr	   �	Exception�str)r9   �valid_templates�config_path�frX  �er"   r"   r#   �get_template_config�  s    
*rq  �POST)�methodsc              
   C   s*  z�g d�}| |vr*t dd| � �i�dfW S tj}|sFt ddi�dfW S g d�}|D ](}||vrRt dd|� �i�df  W S qRtj�tjd| � d	��}t|d
dd��"}tj	||d
dd� W d   � n1 s�0    Y  t dd| � d�d��W S  t
�y$ } z t dt|�i�dfW  Y d }~S d }~0 0 d S )N)r�   r�   r�   r�   r^  zInvalid template name: �  zNo configuration data provided)rH   r9   �versionZglobal_settings�header�sidebarZmain_contentzMissing required field: rX  rY  �wr[  r\  F�   )Zensure_asciiZindentTr_  z saved successfully)Zsuccess�messagera  )r	   r   ri  rb  rc  rd  re  rf  rh  �dumprk  rl  )r9   rm  �config_dataZrequired_fieldsZfieldrn  ro  rp  r"   r"   r#   �save_template_config�  s"    0r}  �/c               
   C   s2  �z�t j�d�} t�d�sf| sftjjdd��� }|rT|j�	dd�}t
td|d��W S t
tdd	d��W S | �r�tj
�r�tj�t| �}|�r�tj�tj|j�}tj�|��r�d|jd
�}tjj|jd��� }|�r|j|d< |j|d
< |j|d< |j|d< |j|d< |j|d< |j|d< tjj|jd��� }|�rRi }|D ]}	|	j ||	j!< �q6||d< |j�	dd�}tj�tj"d|� d��}
tj�|
��r�d|d< t#|jfi |��W S tjjdd��� }|�r�tj�tj|j�}tj�|��r�tjj|jd��� }i }|�r>|j|d< |j|d
< |j|d< |j|d< |j|d< |j|d< |j|d< tjj|jd��� }|�r|i }|D ]}	|	j ||	j!< �q`||d< |j�	dd�}tj�tj"d|� d��}
tj�|
��r�d|d< z�t$|
ddd��j}t%�&|�}i }
|�di ��dg �D ]&}|�d�}|�r�|�dg �|
|< �q�|
|d< d|d < W d   � n1 �s:0    Y  W n8 t'�y~ } zt(d!t)|�� �� W Y d }~n
d }~0 0 t#|jfi |��W S t(d"|j� d#�� d$|_*tjjd%d��� }|�r�d|_*tj�+�  t#d%�W S n
t#d%�W S W n@ t'�y, } z&t(d&t)|�� �� t#d%�W  Y d }~S d }~0 0 d S )'NZpreview�template_logged_inT�r;   �.html� �template_loginr8   r�   )Z
is_previewZpreview_template_namerR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  rY  Zhas_json_configrZ  r[  r\  rw  �
categoriesr+   �items�
sidebar_items�home�current_page�Error loading sidebar config: r�   u    不存在，使用默认模板Fr   �   错误: ),r   �argsr|   r   r.   �query�	filter_by�firstr9   �replacer   r   r   Zis_authenticatedr(   rb  rc  rd  re  �template_folderrg  rN   r+   rS   rT   rU   rV   rW   rX   rZ   r[   �allr`   r]   rf  r   rh  ri  rj  rk  �printrl  r;   �commit)Zpreview_template_id�active_templater9   Zpreview_template�
template_path�template_contextrQ   �template_contentsr\   �contentrn  ro  r|  r�  �category�category_idrp  �default_templater"   r"   r#   �index�  s�    �















,(
r�  z/login/<template_name>ZGETc                 C   s�   g d�}| |vrd} t j�t j�| � d����� }|s`t jjdd��� }|s`td� tt	d��S t
jj|jd��� }tj
d	kr�tj�d
�}tj�d�}|r�||jkr�||jkr�dtd
< | td< tt	d��S td� td| � d��S )NrW  r�   r�  r   r8   u   模板配置错误r�  rR   rr  r   r-   Tr  r9   �   用户名或密码错误zlogin/z_login.html)r.   r�  �filterr9   �liker�  r�  r   r   r   r>   r+   r   �method�formr|   r   r-   r   r   )r9   rm  rM   �
credentialr   r-   r"   r"   r#   r�  t  s&    
r�  z/logoutc                   C   s$   t �dd � t �dd � ttd��S )Nr  r9   r�  )r   �popr   r   r"   r"   r"   r#   �template_logout�  s    r�  z/shop_overviewc                   C   s   t �d�sttd��S td�S )Nr  r�  zshop_overview.html�r   r|   r   r   r   r"   r"   r"   r#   �
shop_overview�  s    
r�  z/balancec                   C   s   t �d�sttd��S td�S )Nr  r�  zbalance.htmlr�  r"   r"   r"   r#   �balance�  s    
r�  z/modulesc                   C   s   t �d�sttd��S td�S )Nr  r�  zmodules.htmlr�  r"   r"   r"   r#   �modules�  s    
r�  z/statisticsc                   C   s   t �d�sttd��S td�S )Nr  r�  zstatistics.htmlr�  r"   r"   r"   r#   �
statistics�  s    
r�  z/order_managementc            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  �alipay_template.jsonrZ  r[  r\  rw  r�  r+   r�  r�  r�  �order_managementr�  �order_management.html)r�  �"r   r|   r   r   r.   r�  r�  r�  rN   r+   rS   rT   rU   rV   rW   rX   rZ   r[   r�  r`   r]   rb  rc  rd  re  rf  rg  rh  ri  rj  rk  r�  rl  r   �
r�  r�  rQ   r�  r\   r�  rn  ro  r|  r�  r�  r�  rp  r"   r"   r#   r�  �  sF    









,(r�  z/product_managementc            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �product_managementr�  �product_management.html)r�  r�  r�  r"   r"   r#   r�  �  sF    









,(r�  z/data_analysisc               
   C   s�  t �d�sttd��S tjjdd��� } i }z�tjjdd��� }|�r|j	|j
|j|jd�|d< t
jj|jd	��t
j��� }d
d� |D �|d< d
d� |D �|d< dd� |D �|d< tjj|jd	��� }dd� |D �|d< tjj|jd	��tj��� }dd� |D �|d< W n8 t�y@ } ztdt|�� �� W Y d }~n
d }~0 0 | �r�tjj| jd��� }|�r�|j|d< |j|d< |j|d< |j|d< |j|d< |j|d< |j|d< t jj| jd��� }|�r�i }	|D ]}
|
j!|	|
j"< �q�|	|d< z�t#j$�%t&j'dd �}t#j$�(|��r�t)|d!d"d#��b}t*�+|�}
i }|
�d$i ��d%g �D ]&}|�d&�}|�r:|�d'g �||< �q:||d(< W d   � n1 �s�0    Y  W n8 t�y� } ztd)t|�� �� W Y d }~n
d }~0 0 d*|d+< t,d-i |��S ).Nr  r�  Tr�  �today�re   �rg   rh   ri   rj   �analytics_data�rn   c                 S   s   g | ]
}|j �qS r"   �ro   ��.0Ztrendr"   r"   r#   �
<listcomp>H  r7   z!data_analysis.<locals>.<listcomp>Ztrend_hoursc                 S   s   g | ]
}|j �qS r"   �rp   r�  r"   r"   r#   r�  I  r7   Z
trend_amountsc                 S   s   g | ]
}|j �qS r"   �rh   r�  r"   r"   r#   r�  J  r7   Ztrend_ordersc                 S   s   g | ]}|j |j|jd ��qS �)r^   ri   rj   �rt   ri   rj   �r�  �sourcer"   r"   r#   r�  N  s
   ��rs   c              	   S   s*   g | ]"}|j |j|j|j|j|jd ��qS �)rw   r^   Zimagerp   �countrj   �rw   rx   ry   rz   r{   rj   �r�  �productr"   r"   r#   r�  X  s   ��rv   zError loading analytics data: rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �
data_analysisr�  �data_analysis.html)r�  )-r   r|   r   r   r.   r�  r�  r�  rb   rg   rh   ri   rj   rk   r+   �order_byro   r�  rr   ru   rw   rk  r�  rl  rN   rS   rT   rU   rV   rW   rX   rZ   r[   r`   r]   rb  rc  rd  re  rf  rg  rh  ri  rj  r   )r�  r�  r�  rm   rs   rv   rp  rQ   r�  r\   r�  rn  ro  r|  r�  r�  r�  r"   r"   r#   r�  0  sr    
�
�
	�
(








,(r�  z/marketing_managementc            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �marketing_managementr�  �marketing_management.html)r�  r�  r�  r"   r"   r#   r�  �  sF    









,(r�  z/customer_servicec            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �customer_servicer�  �customer_service.html)r�  r�  r�  r"   r"   r#   r�  �  sF    









,(r�  z/financial_managementc            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �financial_managementr�  �financial_management.html)r�  r�  r�  r"   r"   r#   r�    sF    









,(r�  z/reconciliation_centerc            
   
   C   s�  t �d�sttd��S tjjdd��� } i }| �r�tjj| j	d��� }|r�|j
|d< |j|d< |j|d< |j
|d	< |j|d
< |j|d< |j|d< tjj| j	d��� }|r�i }|D ]}|j||j< q�||d
< z�tj�tjdd�}tj�|��rnt|ddd��b}t�|�}i }	|�di ��dg �D ]&}
|
�d�}|�r|
�dg �|	|< �q|	|d< W d   � n1 �sd0    Y  W n8 t�y� } ztdt |�� �� W Y d }~n
d }~0 0 d|d< t!di |��S )Nr  r�  Tr�  rR   rS   rT   rU   rV   rW   rX   rZ   r\   rX  r�  rZ  r[  r\  rw  r�  r+   r�  r�  r�  �reconciliation_centerr�  �reconciliation_center.html)r�  r�  r�  r"   r"   r#   r�  ;  sF    









,(r�  z/admin/loginc                  C   sf   t jdkr^t j�d�} t j�d�}tjj| d��� }|rV|j|krVt	|� t
td��S td� t
d�S )Nrr  r   r-   r   �admin_dashboardr�  zadmin/login.html)r   r�  r�  r|   r   r�  r�  r�  r-   r
   r   r   r   r   )r   r-   �userr"   r"   r#   r   s  s    
z
/admin/logoutc                   C   s   t �  ttd��S )Nr   )r   r   r   r"   r"   r"   r#   �admin_logout�  s    r�  z/admin/json_configc                  C   s   t j�� } td| d�S )Nzadmin/json_config.html��	templates)r.   r�  r�  r   r�  r"   r"   r#   �admin_json_config�  s    
r�  z/admin/friendly_configc               	   C   sl   t j�� } tj�d�}d }|rNzt|�}t j�|�}W n ttfyL   Y n0 |s^| r^| d }t	d| |d�S )NrH   r   zadmin/friendly_config.html)r�  �selected_template)
r.   r�  r�  r   r�  r|   r}   �
ValueError�	TypeErrorr   )r�  rH   r�  r"   r"   r#   �admin_friendly_config�  s    
r�  z/admin/dashboardc                  C   s�   t j�� t jjdd��� tj�� tj�� t�dd�t�dd�t	t�
dd�d	�t	t�
d
d�d	�d�} d
ddt�t
j�td	d� d�dddt�t
j�td
d� d�dddt�t
j�tdd� d�dddt�t
j�tddd� d�g}td| |d�S )NTr�  i�  i�  r@   ra  �
   �   ry  �   �   )Ztotal_templatesr�  Ztotal_credentialsZtotal_customizationsZtotal_visitsZtotal_usersrj   Zavg_timeu   模板切换u-   将活跃模板切换为支付宝商家后台r�   )�hours)�actionrJ   r�  Z	timestampu   凭证更新u$   更新了TikTok模板的登录凭证u   模板自定义u-   修改了支付宝模板的主题色和布局�   )�daysu   用户登录u   管理员登录系统�   )r�  r�  zadmin/dashboard.html�ZstatsZrecent_activities)r.   r�  r�  r�  r�  r>   rN   �randomZrandint�roundZuniformr   r4   r   r5   r   r   r�  r"   r"   r#   r�  �  s>    

������r�  z/admin/credentialsc            	   
   C   s�  �z�t j�� } i }| D ]&}tjj|jd��� }|r|||j< qtjdk�r�z�tj	�
d�}tj	�
d�}tj	�
d�}tj	�
d�}|r�|r�|s�td� tt
d��W W S tj�
t |�}|s�td	� tt
d��W W S tjj|jd��� }|�r||_||_||_t�tj�|_nt|j|||d
�}tj�|� tj��  td� tt
d��W W S  t�y� } z6tj��  tdt|�� �� tt
d��W  Y d }~W S d }~0 0 td
| |d�W S  t�y� } z*tdt|�� �� tt
d��W  Y d }~S d }~0 0 d S )NrR   rr  rH   r   r-   rJ   u   请填写所有必填字段�admin_credentialsu   模板不存在r�   u   凭证设置已更新r�  zadmin/credential_config.html)r�  rC   �   系统错误: r�  )r.   r�  r�  r>   r�  r+   r�  r   r�  r�  r|   r   r   r   r(   r   r   r-   rJ   r   r4   r   r5   r=   �addr�  rk  �rollbackrl  r   )	r�  rC   rM   r�  rH   r   r-   rJ   rp  r"   r"   r#   r�  �  sX    
�

$r�  z/admin/templatesc               
      s"  �z�t j�� } g � | D ]T}tj�tj|j�}tj�	|�s|j
r`d|_
t jjdd��� }|r`d|_
� �
|� q� D ]}tj�|� qp| r�t� fdd�| D ��r�t ddd�}tj�|� tj��  t j�� } tjdk�r�z�tj�d	�}t j�t j
di� tj�t |�}|�rdtj�tj|j�}tj�	|��rPd|_
t�tj�|_tj��  td
� ntd|j� d�� ntd
� ttd��W W S  t �y� } z6tj�!�  tdt"|�� �� ttd��W  Y d }~W S d }~0 0 t#d| d�W S  t �y } z*tdt"|�� �� ttd��W  Y d }~S d }~0 0 d S )NFr   r8   Tc                 3   s   | ]}|� v V  qd S r2   r"   �r�  rM   �Ztemplates_to_remover"   r#   �	<genexpr>2  r7   z"admin_templates.<locals>.<genexpr>r�   rr  rH   �   模板设置已更新�   错误: 模板文件 r�   �   错误: 无效的模板ID�admin_templatesr�  zadmin/template_config.htmlr�  r�  r�  )$r.   r�  r�  rb  rc  rd  re  r�  r9   rg  r;   r�  r�  �appendr(   r   �deleter�  r�  r   r�  r�  r|   �updater   r4   r   r5   r=   r   r   r   rk  r�  rl  r   )r�  rM   r�  r�  rH   rp  r"   r�  r#   r�    sT    





$r�  z/admin/contentc               F   C   s�  �znt j�� } i }tj�d�dk}|�r�tj��  tj	�
�  td� | D �]�}d|jv rHt|j
ddddd	�t|j
d
ddd
d	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
d
dddd	�t|j
dddd d	�t|j
d!d"d#d$d	�t|j
d%d&d'd(d	�t|j
d%d)d*d+d	�t|j
d%d,d-d.d	�t|j
d%d/d0d1d	�t|j
d%d2d3d4d	�t|j
d!d5d6d7d	�t|j
d%d8d9d:d	�t|j
d%d;d<d=d	�t|j
d>d?d@dAd	�t|j
d%dBdCdDd	�t|j
d%dEdFdGd	�t|j
d%dHdIdJd	�t|j
d%dKdLdMd	�t|j
dNdOdPdQd	�t|j
dRdSdTdUd	�t|j
dVdWdXdYd	�t|j
dZd[d\d]d	�t|j
dVd^d_d`d	�t|j
dRdadbdcd	�t|j
dddedfdgd	�t|j
dZdhdidjd	�t|j
dVdkdldmd	�t|j
dddndodpd	�t|j
dNdqdrdsd	�t|j
dddtdudvd	�t|j
dddwdxdyd	�t|j
dddzd{d|d	�t|j
dNd}d~dd	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dRd�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
d�d�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dVd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
ddd�d�d�d	�t|j
dNd�d�d�d	�t|j
dRd�d�d�d	�g?}tj	�|� qHtj	�
�  ttdσ�W S | D ]$}tjj|j
dЍ�� }|||j
< �q�tjd�k�rbtj�dҡ}|d�k�rbz�tj�dԡ}tj�dա}tj	�t |�}|�s�tdփ ttdσ�W W S tj�� D ]Z\}	}
|	�dס�r�|	�d�dء}tj	�t|�}|�r�|jt|�k�r�|
|_t�tj�|_�q�tj	�
�  td�|� dڝ� ttdσ�W W S  t�y` }
 z6tj	� �  td�t!|
�� �� ttdσ�W  Y d }
~
W S d }
~
0 0 t"d�| |dݍW S  t�y� }
 z*td�t!|
�� �� ttd߃�W  Y d }
~
S d }
~
0 0 d S )�NZrecreate�1u?   所有模板内容已重置，正在重新创建默认内容...r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   rJ   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r�   r   r  r  r  r  r  r  r  r  r	  r
  r  r  r
  r  r  r  r  r  r  r  r  r  r  r  r  r  r  r  r  r  r  r   r!  r"  r#  r$  r%  r&  r'  r(  r)  r*  r+  r,  r-  r.  r/  r0  r1  r2  r3  r4  r5  r6  r7  r8  r9  r:  r;  r<  r=  r>  r?  r@  rA  rB  rC  rD  rE  rF  rG  rH  rI  rJ  rK  rL  rM  rN  rO  rP  rQ  rR  rS  rT  rU  rV  �
admin_contentrR   rr  r�  Zupdate_contentrH   �content_sectionr�  Zcontent_r�  u   模板内容已更新 (�)r�  zadmin/content_config.html)r�  r\   r�  r�  )#r.   r�  r�  r   r�  r|   r[   r�  r(   r   r�  r   r9   r+   �add_allr   r   r�  r�  r�  r�  �
startswithr�  rH   r}   r`   r   r4   r   r5   r=   rk  r�  rl  r   )r�  r\   Zrecreate_contentrM   �default_contentsr�  r�  rH   r�  �key�valueZ
content_idr�  rp  r"   r"   r#   r�  ]  s�   





�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�� � !



$r�  z/admin/enhanced_templatesc               
      s^  �zt j�� } i }g � | D ]T}tj�tj|j�}tj�	|�s|j
rdd|_
t jjdd��� }|rdd|_
� �
|� q� D ]}tj�|� qt| r�t� fdd�| D ��r�t ddd�}tj�|� tj��  t j�� } | D ]�}tjj|jd��� }|r�|||j< q�d	|jv �r
d
}n:d|jv �rd}n(d
|jv �r.d}nd|jv �r@d}nd
}t|j|dd�}tj�|� tj��  |||j< q�tjdk�rtj�dd�}|dk�r�z�tj�d�}t j�t j
di� tj�t |�}|�r*tj�tj|j�}tj�	|��rd|_
t�tj�|_tj��  td� ntd|j� d�� ntd� t t!d��W W S  t"�y� }	 z6tj�#�  tdt$|	�� �� t t!d��W  Y d }	~	W S d }	~	0 0 �np|dk�r�ztj�d�}tj�d�}tj�d�}
d tjv }d!tjv }d"tjv }
d#tjv }tj�d$d%�}tj�t |�}|�s*td� t t!d��W W S tjj|jd��� }|�s\t|jd�}tj�|� ||_%|
|_&||_'||_(|
|_)||_*||_+t�tj�|_tj��  td&� t t!d��W W S  t"�y }	 z6tj�#�  tdt$|	�� �� t t!d��W  Y d }	~	W S d }	~	0 0 t,d'| |d(�W S  t"�yX }	 z*td)t$|	�� �� t t!d*��W  Y d }	~	S d }	~	0 0 d S )+NFr   r8   Tc                 3   s   | ]}|� v V  qd S r2   r"   r�  r�  r"   r#   r�  Z  r7   z+admin_enhanced_templates.<locals>.<genexpr>r�   rR   r�   rP   r�   r�   r�   r�   r�   r�   r1   r�   rr  r�  ZactivaterH   r�  r�  r�   r�  �admin_enhanced_templatesr�  Z	customizerS   rT   rU   rV   rW   rX   rZ   r�  u   模板自定义配置已更新z#admin/enhanced_template_config.html)r�  �customizationsr�  r�  )-r.   r�  r�  rb  rc  rd  re  r�  r9   rg  r;   r�  r�  r�  r(   r   r�  r�  r�  rN   r+   r   r�  r�  r|   r�  r   r4   r   r5   r=   r   r   r   rk  r�  rl  rS   rT   rU   rV   rW   rX   rZ   r   )r�  r�  rM   r�  r�  rQ   rS   r�  rH   rp  rT   rU   rV   rW   rX   rZ   r"   r�  r#   r�  @  s�    


�




(






$r�  z/api/analytics/<time_range>c              
   C   s`  t �d�stddi�dfS g d�}| |vr:tddi�dfS z�tjj| d��� }|sdtdd	i�d
fW S tjj|jd��	tj
��� }tjj|jd��� }t
jj|jd��	t
j��� }|j|j|j|jd�d
d� |D �dd� |D �dd� |D �d�dd� |D �dd� |D �d�}t|�W S  t�yZ } z.tdt|�� �� tddi�dfW  Y d }~S d }~0 0 d S )Nr  r^  ZUnauthorizedi�  )r�  Z	yesterdayZweekZmonthzInvalid time rangert  r�  zData not foundr`  r�  r�  c                 S   s   g | ]
}|j �qS r"   r�  r�  r"   r"   r#   r�  �  r7   z!get_analytics.<locals>.<listcomp>c                 S   s   g | ]
}|j �qS r"   r�  r�  r"   r"   r#   r�  �  r7   c                 S   s   g | ]
}|j �qS r"   r�  r�  r"   r"   r#   r�  �  r7   )r�  ZamountsZordersc                 S   s   g | ]}|j |j|jd ��qS r�  r�  r�  r"   r"   r#   r�  �  s
   ��c              	   S   s*   g | ]"}|j |j|j|j|j|jd ��qS r�  r�  r�  r"   r"   r#   r�  �  s   ��)ZoverviewZtransaction_trendrs   rv   zError fetching analytics data: zInternal server errorra  )r   r|   r	   rb   r�  r�  r�  rk   r+   r�  ro   r�  rr   ru   rw   rg   rh   ri   rj   rk  r�  rl  )re   Zvalid_rangesr�  rm   rs   rv   Zresponserp  r"   r"   r#   �
get_analytics�  s@    
�����
r�  �__main__z0.0.0.0i�  )ZhostZportZthreaded�debug)hZflaskr   r   r   r   r   r   r   r	   Zflask_sqlalchemyr
   Zflask_loginr   r   r
   r   r   r   rb  r�  ri  r   r   r   r%   re  �urandomZ
secret_keyrc  �abspath�dirname�__file__Zbasedirrd  rX  r(   Z
login_managerZinit_appZ
login_viewZModelr   r.   r>   rN   r[   rb   rk   rr   ru   Zuser_loaderr~   �app_contextZ
create_allr�  r�  Ztemplate_filesZtemplates_to_addZ
template_filerg  r�  r;   r�  r�  r�  r�  r�  r�   r�  r�  rM   r+   r9   r�  rQ   r�  r�  Zrouterq  r}  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r�  r   r�  r�  r�  r�  r�  r�  r�  r�  r�  �runr"   r"   r"   r#   �<module>   s  ( 

	




������������"
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
�
������� � !*

 #





7
7
b
7
7
7
7



,=B b 
@