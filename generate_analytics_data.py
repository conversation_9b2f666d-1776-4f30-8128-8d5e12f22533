#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生成数据分析模拟数据并存储到SQLite数据库中
"""

import os
import random
from datetime import datetime, timezone, timedelta
from app import app, db, AnalyticsData, TransactionTrend, UserSource, ProductSales

def generate_analytics_data():
    """生成数据分析模拟数据并存储到SQLite数据库中"""

    with app.app_context():
        # 检查是否已经有数据
        if AnalyticsData.query.first():
            print("数据库中已存在数据分析数据，跳过生成...")
            return

        print("正在生成模拟数据分析数据...")

        # 今天的日期
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        week_start = today - timedelta(days=7)
        month_start = today - timedelta(days=30)

        # 创建四种时间范围的数据
        time_ranges = [
            {'name': 'today', 'date': today, 'factor': 1.0},
            {'name': 'yesterday', 'date': yesterday, 'factor': 0.9},
            {'name': 'week', 'date': week_start, 'factor': 7.0},
            {'name': 'month', 'date': month_start, 'factor': 30.0}
        ]

        analytics_data_list = []

        for time_range in time_ranges:
            # 创建基础分析数据
            transaction_amount = round(1258600.0 * time_range['factor'], 2)
            order_count = int(15642 * time_range['factor'])
            visitor_count = int(234567 * time_range['factor'])
            conversion_rate = round(8.75 * (1.0 + random.uniform(-0.1, 0.1)), 2)

            analytics_data = AnalyticsData(
                date=time_range['date'],
                time_range=time_range['name'],
                transaction_amount=transaction_amount,
                order_count=order_count,
                visitor_count=visitor_count,
                conversion_rate=conversion_rate
            )

            db.session.add(analytics_data)
            db.session.flush()  # 获取ID

            analytics_data_list.append(analytics_data)

            # 创建交易趋势数据
            hours = [0, 4, 8, 12, 16, 20, 23]
            base_amounts = [120000, 180000, 260000, 320000, 380000, 320000, 280000]
            base_orders = [1200, 1800, 2600, 3200, 3800, 3200, 2800]

            for i, hour in enumerate(hours):
                amount = round(base_amounts[i] * time_range['factor'] * (1.0 + random.uniform(-0.1, 0.1)), 2)
                orders = int(base_orders[i] * time_range['factor'] * (1.0 + random.uniform(-0.1, 0.1)))

                trend = TransactionTrend(
                    analytics_id=analytics_data.id,
                    hour=hour,
                    amount=amount,
                    order_count=orders
                )

                db.session.add(trend)

            # 创建用户来源数据
            sources = [
                {'name': 'search_engine', 'visitors': 104800, 'conversion': 12.8},
                {'name': 'direct', 'visitors': 73500, 'conversion': 9.5},
                {'name': 'social', 'visitors': 58000, 'conversion': 7.2},
                {'name': 'ads', 'visitors': 48400, 'conversion': 10.4},
                {'name': 'other', 'visitors': 30000, 'conversion': 5.6}
            ]

            for source in sources:
                visitors = int(source['visitors'] * time_range['factor'] * (1.0 + random.uniform(-0.1, 0.1)))
                conversion = round(source['conversion'] * (1.0 + random.uniform(-0.1, 0.1)), 2)

                user_source = UserSource(
                    analytics_id=analytics_data.id,
                    source_name=source['name'],
                    visitor_count=visitors,
                    conversion_rate=conversion
                )

                db.session.add(user_source)

            # 创建商品销售数据
            products = [
                {'name': '智能手表健康监测多功能运动手环', 'image': '/static/img/products/watch.png', 'amount': 7012200, 'count': 7856, 'conversion': 12.8},
                {'name': '高级商务办公椅人体工学椅', 'image': '/static/img/products/chair.png', 'amount': 5455800, 'count': 4235, 'conversion': 9.7},
                {'name': '多功能无线充电器手机支架', 'image': '/static/img/products/charger.png', 'amount': 2507400, 'count': 12678, 'conversion': 14.5}
            ]

            for i, product in enumerate(products):
                amount = round(product['amount'] * time_range['factor'] * (1.0 + random.uniform(-0.1, 0.1)), 2)
                count = int(product['count'] * time_range['factor'] * (1.0 + random.uniform(-0.1, 0.1)))
                conversion = round(product['conversion'] * (1.0 + random.uniform(-0.1, 0.1)), 2)

                product_sale = ProductSales(
                    analytics_id=analytics_data.id,
                    rank=i+1,
                    product_name=product['name'],
                    product_image=product['image'],
                    sales_amount=amount,
                    sales_count=count,
                    conversion_rate=conversion
                )

                db.session.add(product_sale)

        db.session.commit()
        print("模拟数据分析数据生成完成！")

if __name__ == '__main__':
    # 确保数据库表已创建
    with app.app_context():
        db.create_all()

    # 生成模拟数据
    generate_analytics_data()
