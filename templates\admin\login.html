<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="w-full max-w-md">
        <div class="bg-white rounded-lg shadow-md p-8">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full mb-4">
                    <i class="ri-admin-line text-3xl text-blue-500"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">管理员登录</h1>
                <p class="text-gray-500 mt-2">请输入您的管理员账号和密码</p>
            </div>
            
            {% with messages = get_flashed_messages() %}
                {% if messages %}
                    <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                        <p class="text-red-600 text-sm">{{ messages[0] }}</p>
                    </div>
                {% endif %}
            {% endwith %}
            
            <form method="post" action="{{ url_for('admin_login') }}">
                <div class="mb-4">
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                    <input type="text" id="username" name="username" required
                        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                    <input type="password" id="password" name="password" required
                        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit"
                    class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    登录
                </button>
            </form>
            
            <div class="mt-6 text-center">
                <a href="{{ url_for('index') }}" class="text-sm text-blue-500 hover:text-blue-600">
                    返回首页
                </a>
            </div>
        </div>
        <div class="text-center mt-4 text-gray-500 text-sm">
            &copy; 2023 模板管理系统
        </div>
    </div>
</body>
</html>
