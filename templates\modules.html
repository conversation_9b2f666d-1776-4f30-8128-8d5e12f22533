<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>模块 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
      .module-card {
        transition: all 0.2s ease;
      }
      .module-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a
              href="/"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >工作台</a
            >
            <a
              href="/financial_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >资金管理</a
            >
            <a
              href="/reconciliation_center"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >对账中心</a
            >
            <a
              href="/product_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >产品中心</a
            >
            <a
              href="/data_analysis"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >数据中心</a
            >
            <a
              href="/marketing_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >营销中心</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input
              type="text"
              placeholder="搜索功能/应用/服务"
              class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
            >
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button
            class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-notification-3-line text-xl"></i>
            <span
              class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"
            ></span>
          </button>
          <button
            class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div
              class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all"
            >
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      <aside class="w-56 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
        <div class="py-3">
          <div class="px-4 mb-4">
            <div class="relative w-full">
              <input
                type="text"
                placeholder="搜索菜单"
                class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
              />
              <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>
      
          <div class="px-4 py-2">
            <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
              <span>应用中心</span>
              <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                <i class="ri-arrow-down-s-line"></i>
              </button>
            </div>
            <ul class="space-y-0.5">
              <li>
                <a
                  href="/shop_overview"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-store-line text-lg"></i>
                  </span>
                  <span>店铺概况</span>
                </a>
              </li>
              <li>
                <a
                  href="/balance"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-wallet-3-line text-lg"></i>
                  </span>
                  <span>余额</span>
                </a>
              </li>
              <li>
                <a
                  href="/modules"
                  class="flex items-center px-3 py-2.5 text-sm text-primary bg-blue-50 rounded-lg sidebar-menu-item active"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 sidebar-icon">
                    <i class="ri-apps-line text-lg"></i>
                  </span>
                  <span class="font-medium">模块</span>
                </a>
              </li>
              <li>
                <a
                  href="/statistics"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-bar-chart-line text-lg"></i>
                  </span>
                  <span>统计</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      
      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">模块管理</h1>
            <p class="mt-1 text-sm text-gray-500">管理和配置您的商家平台功能模块</p>
          </div>
          
          <!-- 搜索和筛选 -->
          <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:space-x-4">
              <div class="relative flex-1 mb-4 md:mb-0">
                <input
                  type="text"
                  placeholder="搜索模块名称"
                  class="w-full pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all"
                />
                <div
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
                >
                  <i class="ri-search-line"></i>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <select class="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20">
                  <option>全部分类</option>
                  <option>营销工具</option>
                  <option>数据分析</option>
                  <option>客户管理</option>
                  <option>订单处理</option>
                </select>
                <select class="border border-gray-200 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20">
                  <option>全部状态</option>
                  <option>已启用</option>
                  <option>已停用</option>
                </select>
              </div>
            </div>
          </div>
          
          <!-- 模块卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <!-- 模块卡片1 -->
            <div class="bg-white rounded-lg shadow-sm p-4 module-card">
              <div class="flex items-start">
                <div class="w-12 h-12 rounded-lg bg-blue-50 flex items-center justify-center text-primary flex-shrink-0">
                  <i class="ri-customer-service-2-line text-2xl"></i>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <h3 class="text-base font-medium text-gray-800">客服管理</h3>
                    <div class="relative inline-block">
                      <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">管理客服团队和客户沟通</p>
                  <div class="mt-3 flex items-center justify-between">
                    <span class="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded-full">已启用</span>
                    <a href="#" class="text-xs text-primary">设置</a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 模块卡片2 -->
            <div class="bg-white rounded-lg shadow-sm p-4 module-card">
              <div class="flex items-start">
                <div class="w-12 h-12 rounded-lg bg-orange-50 flex items-center justify-center text-orange-500 flex-shrink-0">
                  <i class="ri-coupon-line text-2xl"></i>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <h3 class="text-base font-medium text-gray-800">优惠券管理</h3>
                    <div class="relative inline-block">
                      <label class="switch">
                        <input type="checkbox" checked>
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">创建和管理各类优惠券</p>
                  <div class="mt-3 flex items-center justify-between">
                    <span class="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded-full">已启用</span>
                    <a href="#" class="text-xs text-primary">设置</a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 模块卡片3 -->
            <div class="bg-white rounded-lg shadow-sm p-4 module-card">
              <div class="flex items-start">
                <div class="w-12 h-12 rounded-lg bg-purple-50 flex items-center justify-center text-purple-500 flex-shrink-0">
                  <i class="ri-mail-send-line text-2xl"></i>
                </div>
                <div class="ml-4 flex-1">
                  <div class="flex items-center justify-between">
                    <h3 class="text-base font-medium text-gray-800">邮件营销</h3>
                    <div class="relative inline-block">
                      <label class="switch">
                        <input type="checkbox">
                        <span class="slider round"></span>
                      </label>
                    </div>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">创建和发送营销邮件</p>
                  <div class="mt-3 flex items-center justify-between">
                    <span class="text-xs text-gray-600 bg-gray-100 px-2 py-0.5 rounded-full">已停用</span>
                    <a href="#" class="text-xs text-primary">设置</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <style>
      /* 开关样式 */
      .switch {
        position: relative;
        display: inline-block;
        width: 36px;
        height: 20px;
      }
      
      .switch input { 
        opacity: 0;
        width: 0;
        height: 0;
      }
      
      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
      }
      
      .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: .4s;
      }
      
      input:checked + .slider {
        background-color: #1677FF;
      }
      
      input:focus + .slider {
        box-shadow: 0 0 1px #1677FF;
      }
      
      input:checked + .slider:before {
        transform: translateX(16px);
      }
      
      .slider.round {
        border-radius: 34px;
      }
      
      .slider.round:before {
        border-radius: 50%;
      }
    </style>
  </body>
</html>
