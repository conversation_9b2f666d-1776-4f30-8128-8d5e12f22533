# 支付宝商家后台仿真项目

这是一个使用Flask框架实现的支付宝商家后台仿真前端项目。该项目仅供学习和演示目的，模拟了支付宝商家后台的界面和基本功能。

## 功能特点

- 响应式布局，支持各种设备屏幕
- 使用Tailwind CSS进行样式设计
- 包含图表展示（使用ECharts）
- 模拟商家后台常见功能界面

## 技术栈

- Python 3.7.4
- Flask 2.0.1
- Tailwind CSS
- ECharts 图表库
- Remix Icon 图标库

## 安装和运行

1. 克隆此仓库到本地
2. 创建虚拟环境（推荐）
   ```
   python -m venv venv
   ```
3. 激活虚拟环境
   - Windows:
     ```
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```
     source venv/bin/activate
     ```
4. 安装依赖
   ```
   pip install -r requirements.txt
   ```
5. 运行应用
   ```
   python app.py
   ```
6. 打开浏览器访问 `http://localhost:5000`

## 项目结构

```
/
├── app.py                  # Flask应用主文件
├── requirements.txt        # 项目依赖
├── README.md               # 项目说明文档
├── static/                 # 静态资源
│   └── js/
│       └── charts.js       # 图表脚本
└── templates/              # 模板文件
    ├── alipay.html         # 主模板
    └── layout_parts/       # 布局组件
        ├── main_content.html  # 主内容区
        ├── sidebar.html       # 侧边栏
        └── rightbar.html      # 右侧信息栏
```

## 注意事项

- 此项目仅供学习和演示使用，不包含实际的后端功能实现
- 界面设计参考了支付宝商家后台，但进行了简化和修改
- 图表数据为模拟数据，不代表真实业务情况 