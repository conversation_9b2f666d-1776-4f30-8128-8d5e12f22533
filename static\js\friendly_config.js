/**
 * 用户友好的配置界面交互脚本
 */

document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    let currentConfig = null;
    let originalConfig = null;
    let colorPicker = null;

    // 初始化颜色选择器
    initColorPicker();

    // 初始化标签页切换
    initTabs();

    // 加载配置按钮
    document.getElementById('load-config').addEventListener('click', function() {
        const templateId = document.getElementById('template-select').value;
        const templateOption = document.querySelector(`#template-select option[value="${templateId}"]`);
        const templateName = templateOption.textContent.trim().includes('finviz数据提取后台') ? 'yehuo' : 'yehuo';
        loadConfig(templateName);
    });

    // 预览配置按钮
    document.getElementById('preview-config').addEventListener('click', function() {
        try {
            const templateId = document.getElementById('template-select').value;
            const previewUrl = `/?preview=${templateId}`;
            window.open(previewUrl, '_blank');
        } catch (err) {
            showNotification('错误', '无法预览配置: ' + err.toString(), 'error');
        }
    });

    // 保存配置按钮
    document.getElementById('save-config').addEventListener('click', function() {
        try {
            const templateId = document.getElementById('template-select').value;
            const templateOption = document.querySelector(`#template-select option[value="${templateId}"]`);
            const templateName = templateOption.textContent.trim().includes('finviz数据提取后台') ? 'yehuo' : 'yehuo';
            const config = buildConfigFromForm();
            saveConfig(templateName, config);
        } catch (err) {
            showNotification('错误', '无法保存配置: ' + err.toString(), 'error');
        }
    });

    // 关闭通知按钮
    document.getElementById('close-notification').addEventListener('click', function() {
        hideNotification();
    });

    // 添加导航项按钮
    document.getElementById('add-nav-item').addEventListener('click', function() {
        addNavItem({
            id: 'new_item_' + Date.now(),
            text: '新导航项',
            url: '#',
            active: false
        });
    });

    // 添加侧边栏分类按钮
    document.getElementById('add-sidebar-category').addEventListener('click', function() {
        addSidebarCategory({
            id: 'new_category_' + Date.now(),
            title: '新分类',
            collapsible: true,
            expanded: true,
            items: []
        });
    });

    // 添加主内容区块按钮
    document.getElementById('add-main-section').addEventListener('click', function() {
        addMainSection({
            id: 'new_section_' + Date.now(),
            type: 'info_card',
            title: '新内容区块',
            description: '',
            columns: []
        });
    });

    // 添加消息按钮
    document.getElementById('add-message').addEventListener('click', function() {
        addMessage({
            id: 'new_message_' + Date.now(),
            title: '新消息',
            icon: 'ri-notification-3-line',
            highlight: false,
            stats: '阅读量: 0'
        });
    });

    /**
     * 初始化颜色选择器
     */
    function initColorPicker() {
        // 检查颜色选择器元素是否存在
        const colorPickerElement = document.getElementById('primary-color-picker');
        if (!colorPickerElement) {
            console.error('Color picker element not found');
            return;
        }

        try {
            colorPicker = Pickr.create({
                el: '#primary-color-picker',
                theme: 'classic',
                default: '#1677FF',
                components: {
                    preview: true,
                    opacity: true,
                    hue: true,
                    interaction: {
                        hex: true,
                        rgba: true,
                        hsla: false,
                        hsva: false,
                        cmyk: false,
                        input: true,
                        clear: false,
                        save: true
                    }
                }
            });

            // 设置初始背景色
            colorPickerElement.style.backgroundColor = '#1677FF';

            colorPicker.on('save', (color) => {
                if (color) {
                    const hexColor = color.toHEXA().toString();
                    const primaryColorInput = document.getElementById('primary-color');
                    if (primaryColorInput) {
                        primaryColorInput.value = hexColor;
                    }
                    colorPickerElement.style.backgroundColor = hexColor;
                }
                colorPicker.hide();
            });
        } catch (error) {
            console.error('Error initializing color picker:', error);
        }
    }

    /**
     * 初始化标签页切换
     */
    function initTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有标签页的激活状态
                tabButtons.forEach(btn => {
                    btn.classList.remove('border-indigo-500', 'text-indigo-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });

                // 激活当前标签页
                this.classList.remove('border-transparent', 'text-gray-500');
                this.classList.add('border-indigo-500', 'text-indigo-600');

                // 隐藏所有内容
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // 显示对应内容
                const contentId = 'content-' + this.id.split('-')[1];
                document.getElementById(contentId).classList.add('active');
            });
        });
    }

    /**
     * 加载配置
     * @param {string} templateName - 模板名称
     */
    async function loadConfig(templateName) {
        try {
            const response = await fetch(`/api/template_config/${templateName}`);
            if (!response.ok) {
                throw new Error(`Failed to load template config: ${response.statusText}`);
            }
            const config = await response.json();
            currentConfig = config;
            originalConfig = JSON.parse(JSON.stringify(config)); // 深拷贝

            // 填充表单
            populateForm(config);

            showNotification('成功', `已加载 ${templateName} 的配置`, 'success');
        } catch (error) {
            console.error('Error loading template config:', error);
            showNotification('错误', `加载配置失败: ${error.message}`, 'error');
        }
    }

    /**
     * 保存配置
     * @param {string} templateName - 模板名称
     * @param {Object} config - 配置对象
     */
    async function saveConfig(templateName, config) {
        try {
            const response = await fetch(`/api/template_config/${templateName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `Failed to save template config: ${response.statusText}`);
            }

            currentConfig = config;
            originalConfig = JSON.parse(JSON.stringify(config)); // 更新原始配置
            showNotification('成功', `已保存 ${templateName} 的配置`, 'success');
        } catch (error) {
            console.error('Error saving template config:', error);
            showNotification('错误', `保存配置失败: ${error.message}`, 'error');
        }
    }

    /**
     * 填充表单
     * @param {Object} config - 配置对象
     */
    function populateForm(config) {
        // 填充全局设置
        if (config.global_settings) {
            const settings = config.global_settings;

            // 设置主题颜色
            if (settings.primary_color) {
                const primaryColorInput = document.getElementById('primary-color');
                const colorPickerElement = document.getElementById('primary-color-picker');

                if (primaryColorInput) {
                    primaryColorInput.value = settings.primary_color;
                }

                if (colorPickerElement) {
                    colorPickerElement.style.backgroundColor = settings.primary_color;
                }

                if (colorPicker) {
                    try {
                        colorPicker.setColor(settings.primary_color);
                    } catch (error) {
                        console.error('Error setting color picker color:', error);
                    }
                }
            }

            // 设置布局样式
            const layoutStyleSelect = document.getElementById('layout-style');
            if (settings.layout_style && layoutStyleSelect) {
                layoutStyleSelect.value = settings.layout_style;
            }

            // 设置显示/隐藏选项
            const showShopOverview = document.getElementById('show-shop-overview');
            const showBalance = document.getElementById('show-balance');
            const showModules = document.getElementById('show-modules');
            const showStatistics = document.getElementById('show-statistics');

            if (showShopOverview) showShopOverview.checked = settings.show_shop_overview !== false;
            if (showBalance) showBalance.checked = settings.show_balance !== false;
            if (showModules) showModules.checked = settings.show_modules !== false;
            if (showStatistics) showStatistics.checked = settings.show_statistics !== false;
        }

        // 填充顶部导航栏
        if (config.header) {
            const header = config.header;

            // 设置标题和logo
            const headerTitleInput = document.getElementById('header-title');
            const headerLogoInput = document.getElementById('header-logo');

            if (headerTitleInput) headerTitleInput.value = header.title || '';
            if (headerLogoInput) headerLogoInput.value = header.logo || '';

            // 设置搜索框
            if (header.search) {
                const searchPlaceholderInput = document.getElementById('search-placeholder');
                const searchEnabledCheckbox = document.getElementById('search-enabled');

                if (searchPlaceholderInput) searchPlaceholderInput.value = header.search.placeholder || '';
                if (searchEnabledCheckbox) searchEnabledCheckbox.checked = header.search.enabled !== false;
            }

            // 填充导航菜单项
            const navItemsContainer = document.getElementById('nav-items-container');
            if (navItemsContainer) {
                navItemsContainer.innerHTML = ''; // 清空容器

                if (header.navigation && header.navigation.length) {
                    header.navigation.forEach((item, index) => {
                        addNavItem(item, index);
                    });
                }
            }
        }

        // 填充侧边栏菜单
        if (config.sidebar && config.sidebar.categories) {
            const sidebarCategoriesContainer = document.getElementById('sidebar-categories-container');
            if (sidebarCategoriesContainer) {
                sidebarCategoriesContainer.innerHTML = ''; // 清空容器

                config.sidebar.categories.forEach((category, categoryIndex) => {
                    addSidebarCategory(category, categoryIndex);
                });
            }
        }

        // 填充右侧栏
        if (config.rightbar) {
            const rightbar = config.rightbar;

            const rightbarEnabledCheckbox = document.getElementById('rightbar-enabled');
            const rightbarDateInput = document.getElementById('rightbar-date');
            const rightbarTitleInput = document.getElementById('rightbar-title');
            const rightbarViewAllInput = document.getElementById('rightbar-view-all');

            if (rightbarEnabledCheckbox) rightbarEnabledCheckbox.checked = rightbar.enabled !== false;
            if (rightbarDateInput) rightbarDateInput.value = rightbar.date || '';
            if (rightbarTitleInput) rightbarTitleInput.value = rightbar.title || '';

            if (rightbar.view_all && rightbarViewAllInput) {
                rightbarViewAllInput.value = rightbar.view_all.text || '';
            }

            // 填充消息列表
            const messagesContainer = document.getElementById('messages-container');
            if (messagesContainer && rightbar.messages && rightbar.messages.length) {
                messagesContainer.innerHTML = ''; // 清空容器

                rightbar.messages.forEach((message, index) => {
                    addMessage(message, index);
                });
            }
        }

        // 填充主内容区
        if (config.main_content && config.main_content.sections) {
            const mainSectionsContainer = document.getElementById('main-sections-container');
            if (mainSectionsContainer) {
                mainSectionsContainer.innerHTML = ''; // 清空容器

                config.main_content.sections.forEach((section, sectionIndex) => {
                    addMainSection(section, sectionIndex);
                });
            }
        }
    }

    /**
     * 从表单构建配置对象
     * @returns {Object} 配置对象
     */
    function buildConfigFromForm() {
        // 获取模板选择器
        const templateSelect = document.getElementById('template-select');
        if (!templateSelect) {
            console.error('Template select element not found');
            return null;
        }

        // 创建配置对象
        const config = {
            template_id: templateSelect.value,
            template_name: getTemplateName(templateSelect.value),
            version: currentConfig ? currentConfig.version : '1.0.0',
            last_updated: new Date().toISOString(),
            global_settings: {
                primary_color: document.getElementById('primary-color')?.value || '#1677FF',
                layout_style: document.getElementById('layout-style')?.value || 'default',
                show_shop_overview: document.getElementById('show-shop-overview')?.checked !== false,
                show_balance: document.getElementById('show-balance')?.checked !== false,
                show_modules: document.getElementById('show-modules')?.checked !== false,
                show_statistics: document.getElementById('show-statistics')?.checked !== false
            },
            header: {
                title: document.getElementById('header-title')?.value || '商家平台',
                logo: document.getElementById('header-logo')?.value || 'ri-alipay-line',
                search: {
                    enabled: document.getElementById('search-enabled')?.checked !== false,
                    placeholder: document.getElementById('search-placeholder')?.value || '搜索功能/应用/服务'
                },
                navigation: getNavItemsFromForm()
            },
            rightbar: {
                enabled: document.getElementById('rightbar-enabled')?.checked !== false,
                date: document.getElementById('rightbar-date')?.value || '2025/05/08',
                title: document.getElementById('rightbar-title')?.value || '消息中心',
                view_all: {
                    text: document.getElementById('rightbar-view-all')?.value || '全部 6',
                    url: '#'
                },
                messages: [] // 将在后续实现
            }
        };

        // 从表单中获取侧边栏配置
        config.sidebar = {
            categories: getSidebarCategoriesFromForm()
        };

        // 从表单中获取主内容区配置
        config.main_content = {
            sections: getMainSectionsFromForm()
        };

        // 从表单中获取消息配置
        if (config.rightbar) {
            config.rightbar.messages = getMessagesFromForm();
        }

        return config;
    }

    /**
     * 获取导航菜单项
     * @returns {Array} 导航菜单项数组
     */
    function getNavItemsFromForm() {
        const navItems = [];
        const navItemElements = document.querySelectorAll('.nav-item');

        if (!navItemElements || navItemElements.length === 0) {
            // 如果没有导航项，返回默认导航
            return currentConfig && currentConfig.header && currentConfig.header.navigation
                ? currentConfig.header.navigation
                : [
                    {
                        id: "workbench",
                        text: "工作台",
                        url: "/",
                        active: true
                    },
                    {
                        id: "finance",
                        text: "资金管理",
                        url: "/financial_management",
                        active: false
                    }
                ];
        }

        navItemElements.forEach((element, index) => {
            const textElement = element.querySelector('.nav-item-text');
            const urlElement = element.querySelector('.nav-item-url');
            const activeElement = element.querySelector('.nav-item-active');

            navItems.push({
                id: `nav_${index}`,
                text: textElement ? textElement.value : '导航项',
                url: urlElement ? urlElement.value : '#',
                active: activeElement ? activeElement.checked : false
            });
        });

        return navItems;
    }

    /**
     * 获取侧边栏分类
     * @returns {Array} 侧边栏分类数组
     */
    function getSidebarCategoriesFromForm() {
        const categories = [];
        const categoryElements = document.querySelectorAll('.sidebar-category');

        if (!categoryElements || categoryElements.length === 0) {
            // 如果没有分类，返回默认分类
            return currentConfig && currentConfig.sidebar && currentConfig.sidebar.categories
                ? currentConfig.sidebar.categories
                : [];
        }

        categoryElements.forEach((element, index) => {
            const titleElement = element.querySelector('.sidebar-category-title');
            const idElement = element.querySelector('.sidebar-category-id');
            const collapsibleElement = element.querySelector('.sidebar-category-collapsible');
            const expandedElement = element.querySelector('.sidebar-category-expanded');

            // 获取分类中的菜单项
            const items = getSidebarItemsFromCategory(element);

            categories.push({
                id: idElement ? idElement.value : `category_${index}`,
                title: titleElement ? titleElement.value : '分类',
                collapsible: collapsibleElement ? collapsibleElement.checked : true,
                expanded: expandedElement ? expandedElement.checked : true,
                items: items
            });
        });

        return categories;
    }

    /**
     * 获取分类中的菜单项
     * @param {HTMLElement} categoryElement - 分类元素
     * @returns {Array} 菜单项数组
     */
    function getSidebarItemsFromCategory(categoryElement) {
        if (!categoryElement) return [];

        const items = [];
        const itemElements = categoryElement.querySelectorAll('.sidebar-item');

        if (!itemElements || itemElements.length === 0) {
            return [];
        }

        itemElements.forEach((element, index) => {
            const textElement = element.querySelector('.sidebar-item-text');
            const iconElement = element.querySelector('.sidebar-item-icon');
            const urlElement = element.querySelector('.sidebar-item-url');
            const idElement = element.querySelector('.sidebar-item-id');
            const activeElement = element.querySelector('.sidebar-item-active');
            const hasBadgeElement = element.querySelector('.sidebar-item-has-badge');

            const item = {
                id: idElement ? idElement.value : `item_${index}`,
                text: textElement ? textElement.value : '菜单项',
                icon: iconElement ? iconElement.value : 'ri-home-line',
                url: urlElement ? urlElement.value : '#',
                active: activeElement ? activeElement.checked : false
            };

            // 如果有徽章，添加徽章信息
            if (hasBadgeElement && hasBadgeElement.checked) {
                const badgeTextElement = element.querySelector('.sidebar-item-badge-text');
                const badgeColorElement = element.querySelector('.sidebar-item-badge-color');

                item.badge = {
                    text: badgeTextElement ? badgeTextElement.value : '',
                    color: badgeColorElement ? badgeColorElement.value : 'red'
                };
            }

            items.push(item);
        });

        return items;
    }

    /**
     * 获取主内容区块
     * @returns {Array} 主内容区块数组
     */
    function getMainSectionsFromForm() {
        const sections = [];
        const sectionElements = document.querySelectorAll('.main-section');

        if (!sectionElements || sectionElements.length === 0) {
            // 如果没有区块，返回默认区块
            return currentConfig && currentConfig.main_content && currentConfig.main_content.sections
                ? currentConfig.main_content.sections
                : [];
        }

        sectionElements.forEach((element, index) => {
            const typeElement = element.querySelector('.main-section-type');
            const idElement = element.querySelector('.main-section-id');
            const titleElement = element.querySelector('.main-section-title');
            const descriptionElement = element.querySelector('.main-section-description');
            const hasActionLinkElement = element.querySelector('.main-section-has-action-link');
            const hasButtonElement = element.querySelector('.main-section-has-button');

            const section = {
                id: idElement ? idElement.value : `section_${index}`,
                type: typeElement ? typeElement.value : 'info_card',
                title: titleElement ? titleElement.value : '',
                description: descriptionElement ? descriptionElement.value : ''
            };

            // 添加操作链接
            if (hasActionLinkElement && hasActionLinkElement.checked) {
                const actionLinkTextElement = element.querySelector('.main-section-action-link-text');
                const actionLinkUrlElement = element.querySelector('.main-section-action-link-url');

                section.action_link = {
                    text: actionLinkTextElement ? actionLinkTextElement.value : '',
                    url: actionLinkUrlElement ? actionLinkUrlElement.value : '#'
                };
            }

            // 添加按钮
            if (hasButtonElement && hasButtonElement.checked) {
                const buttonTextElement = element.querySelector('.main-section-button-text');
                const buttonUrlElement = element.querySelector('.main-section-button-url');

                section.button = {
                    text: buttonTextElement ? buttonTextElement.value : '',
                    url: buttonUrlElement ? buttonUrlElement.value : '#'
                };
            }

            // 添加列
            section.columns = getMainSectionColumnsFromSection(element);

            sections.push(section);
        });

        return sections;
    }

    /**
     * 获取主内容区块的列
     * @param {HTMLElement} sectionElement - 区块元素
     * @returns {Array} 列数组
     */
    function getMainSectionColumnsFromSection(sectionElement) {
        if (!sectionElement) return [];

        const columns = [];
        const columnsContainer = sectionElement.querySelector('.main-section-columns-container');

        if (!columnsContainer) return [];

        const columnElements = columnsContainer.querySelectorAll('.main-section-column');

        if (!columnElements || columnElements.length === 0) {
            return [];
        }

        columnElements.forEach((element, index) => {
            const titleElement = element.querySelector('.main-section-column-title');
            const idElement = element.querySelector('.main-section-column-id');
            const descriptionElement = element.querySelector('.main-section-column-description');
            const hasGuideLinkElement = element.querySelector('.main-section-column-has-guide-link');
            const hasButtonElement = element.querySelector('.main-section-column-has-button');

            const column = {
                id: idElement ? idElement.value : `column_${index}`,
                title: titleElement ? titleElement.value : '',
                description: descriptionElement ? descriptionElement.value : ''
            };

            // 添加图表设置
            const typeElement = sectionElement.querySelector('.main-section-type');
            if (typeElement) {
                if (typeElement.value === 'chart_row') {
                    const chartIdElement = element.querySelector('.main-section-column-chart-id');
                    const chartHeightElement = element.querySelector('.main-section-column-chart-height');

                    column.chart_id = chartIdElement ? chartIdElement.value : '';
                    column.height = chartHeightElement ? chartHeightElement.value : '';
                } else if (typeElement.value === 'account_row') {
                    // 获取余额项
                    const balanceItemsContainer = element.querySelector('.balance-items-container');
                    if (balanceItemsContainer) {
                        column.balance_items = getBalanceItemsFromContainer(balanceItemsContainer);
                    }
                }
            }

            // 添加指南链接
            if (hasGuideLinkElement && hasGuideLinkElement.checked) {
                const guideLinkTextElement = element.querySelector('.main-section-column-guide-link-text');
                const guideLinkUrlElement = element.querySelector('.main-section-column-guide-link-url');

                column.guide_link = {
                    text: guideLinkTextElement ? guideLinkTextElement.value : '',
                    url: guideLinkUrlElement ? guideLinkUrlElement.value : '#'
                };
            }

            // 添加按钮
            if (hasButtonElement && hasButtonElement.checked) {
                const buttonTextElement = element.querySelector('.main-section-column-button-text');
                const buttonUrlElement = element.querySelector('.main-section-column-button-url');

                column.button = {
                    text: buttonTextElement ? buttonTextElement.value : '',
                    url: buttonUrlElement ? buttonUrlElement.value : '#'
                };
            }

            columns.push(column);
        });

        return columns;
    }

    /**
     * 获取消息
     * @returns {Array} 消息数组
     */
    function getMessagesFromForm() {
        const messages = [];
        const messageElements = document.querySelectorAll('.message-item');

        if (!messageElements || messageElements.length === 0) {
            // 如果没有消息，返回默认消息
            return currentConfig && currentConfig.rightbar && currentConfig.rightbar.messages
                ? currentConfig.rightbar.messages
                : [];
        }

        messageElements.forEach((element, index) => {
            const titleElement = element.querySelector('.message-title');
            const iconElement = element.querySelector('.message-icon');
            const statsElement = element.querySelector('.message-stats');
            const highlightElement = element.querySelector('.message-highlight');

            messages.push({
                id: `msg${index + 1}`,
                title: titleElement ? titleElement.value : '消息',
                icon: iconElement ? iconElement.value : 'ri-notification-3-line',
                stats: statsElement ? statsElement.value : '',
                highlight: highlightElement ? highlightElement.checked : false
            });
        });

        return messages;
    }

    /**
     * 添加导航菜单项
     * @param {Object} item - 导航菜单项
     * @param {number} index - 索引
     */
    function addNavItem(item, index) {
        const navItemsContainer = document.getElementById('nav-items-container');
        if (!navItemsContainer) return;

        const template = document.getElementById('nav-item-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.nav-item-index').textContent = index !== undefined ? index + 1 : navItemsContainer.children.length + 1;

        // 设置值
        clone.querySelector('.nav-item-text').value = item.text || '';
        clone.querySelector('.nav-item-url').value = item.url || '';
        clone.querySelector('.nav-item-active').checked = item.active || false;

        // 添加删除事件
        clone.querySelector('.remove-nav-item').addEventListener('click', function() {
            this.closest('.nav-item').remove();
            // 更新索引
            updateNavItemIndices();
        });

        navItemsContainer.appendChild(clone);
    }

    /**
     * 添加侧边栏分类
     * @param {Object} category - 侧边栏分类
     * @param {number} index - 索引
     */
    function addSidebarCategory(category, index) {
        const sidebarCategoriesContainer = document.getElementById('sidebar-categories-container');
        if (!sidebarCategoriesContainer) return;

        const template = document.getElementById('sidebar-category-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.sidebar-category-index').textContent = index !== undefined ? index + 1 : sidebarCategoriesContainer.children.length + 1;

        // 设置值
        clone.querySelector('.sidebar-category-title').value = category.title || '';
        clone.querySelector('.sidebar-category-id').value = category.id || '';
        clone.querySelector('.sidebar-category-collapsible').checked = category.collapsible !== false;
        clone.querySelector('.sidebar-category-expanded').checked = category.expanded !== false;

        // 添加删除事件
        clone.querySelector('.remove-sidebar-category').addEventListener('click', function() {
            this.closest('.sidebar-category').remove();
            // 更新索引
            updateSidebarCategoryIndices();
        });

        // 添加菜单项按钮事件
        clone.querySelector('.add-sidebar-item').addEventListener('click', function() {
            const sidebarItemsContainer = this.closest('.sidebar-category').querySelector('.sidebar-items-container');
            addSidebarItem(sidebarItemsContainer, {
                id: 'new_item_' + Date.now(),
                text: '新菜单项',
                icon: 'ri-home-line',
                url: '#',
                active: false
            });
        });

        // 添加菜单项
        const sidebarItemsContainer = clone.querySelector('.sidebar-items-container');
        if (category.items && category.items.length) {
            category.items.forEach((item, itemIndex) => {
                addSidebarItem(sidebarItemsContainer, item, itemIndex);
            });
        }

        sidebarCategoriesContainer.appendChild(clone);
    }

    /**
     * 添加侧边栏菜单项
     * @param {HTMLElement} container - 容器元素
     * @param {Object} item - 菜单项
     * @param {number} index - 索引
     */
    function addSidebarItem(container, item, index) {
        if (!container) return;

        const template = document.getElementById('sidebar-item-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.sidebar-item-index').textContent = index !== undefined ? index + 1 : container.children.length + 1;

        // 设置值
        clone.querySelector('.sidebar-item-text').value = item.text || '';
        clone.querySelector('.sidebar-item-icon').value = item.icon || '';
        clone.querySelector('.sidebar-item-url').value = item.url || '';
        clone.querySelector('.sidebar-item-id').value = item.id || '';
        clone.querySelector('.sidebar-item-active').checked = item.active || false;

        // 设置徽章
        const hasBadgeCheckbox = clone.querySelector('.sidebar-item-has-badge');
        const badgeContainer = clone.querySelector('.sidebar-item-badge-container');

        if (item.badge) {
            hasBadgeCheckbox.checked = true;
            badgeContainer.style.display = 'grid';

            clone.querySelector('.sidebar-item-badge-text').value = item.badge.text || '';

            const badgeColorSelect = clone.querySelector('.sidebar-item-badge-color');
            if (badgeColorSelect && item.badge.color) {
                badgeColorSelect.value = item.badge.color;
            }
        }

        // 添加徽章显示/隐藏事件
        hasBadgeCheckbox.addEventListener('change', function() {
            badgeContainer.style.display = this.checked ? 'grid' : 'none';
        });

        // 添加删除事件
        clone.querySelector('.remove-sidebar-item').addEventListener('click', function() {
            this.closest('.sidebar-item').remove();
            // 更新索引
            updateSidebarItemIndices(container);
        });

        container.appendChild(clone);
    }

    /**
     * 添加消息
     * @param {Object} message - 消息
     * @param {number} index - 索引
     */
    function addMessage(message, index) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;

        const template = document.getElementById('message-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.message-index').textContent = index !== undefined ? index + 1 : messagesContainer.children.length + 1;

        // 设置值
        clone.querySelector('.message-title').value = message.title || '';
        clone.querySelector('.message-icon').value = message.icon || '';
        clone.querySelector('.message-stats').value = message.stats || '';
        clone.querySelector('.message-highlight').checked = message.highlight || false;

        // 添加删除事件
        clone.querySelector('.remove-message').addEventListener('click', function() {
            this.closest('.message-item').remove();
            // 更新索引
            updateMessageIndices();
        });

        messagesContainer.appendChild(clone);
    }

    /**
     * 添加主内容区块
     * @param {Object} section - 区块配置
     * @param {number} index - 索引
     */
    function addMainSection(section, index) {
        const mainSectionsContainer = document.getElementById('main-sections-container');
        if (!mainSectionsContainer) return;

        const template = document.getElementById('main-section-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.main-section-index').textContent = index !== undefined ? index + 1 : mainSectionsContainer.children.length + 1;

        // 设置值
        const typeSelect = clone.querySelector('.main-section-type');
        if (typeSelect && section.type) {
            typeSelect.value = section.type;
        }

        clone.querySelector('.main-section-id').value = section.id || '';
        clone.querySelector('.main-section-title').value = section.title || '';
        clone.querySelector('.main-section-description').value = section.description || '';

        // 设置操作链接
        const hasActionLinkCheckbox = clone.querySelector('.main-section-has-action-link');
        const actionLinkContainer = clone.querySelector('.main-section-action-link-container');

        if (section.action_link) {
            hasActionLinkCheckbox.checked = true;
            actionLinkContainer.style.display = 'grid';

            clone.querySelector('.main-section-action-link-text').value = section.action_link.text || '';
            clone.querySelector('.main-section-action-link-url').value = section.action_link.url || '';
        }

        // 添加操作链接显示/隐藏事件
        hasActionLinkCheckbox.addEventListener('change', function() {
            actionLinkContainer.style.display = this.checked ? 'grid' : 'none';
        });

        // 设置按钮
        const hasButtonCheckbox = clone.querySelector('.main-section-has-button');
        const buttonContainer = clone.querySelector('.main-section-button-container');

        if (section.button) {
            hasButtonCheckbox.checked = true;
            buttonContainer.style.display = 'grid';

            clone.querySelector('.main-section-button-text').value = section.button.text || '';
            clone.querySelector('.main-section-button-url').value = section.button.url || '';
        }

        // 添加按钮显示/隐藏事件
        hasButtonCheckbox.addEventListener('change', function() {
            buttonContainer.style.display = this.checked ? 'grid' : 'none';
        });

        // 添加删除事件
        clone.querySelector('.remove-main-section').addEventListener('click', function() {
            this.closest('.main-section').remove();
            // 更新索引
            updateMainSectionIndices();
        });

        // 添加列按钮事件
        clone.querySelector('.add-main-section-column').addEventListener('click', function() {
            const columnsContainer = this.closest('.main-section').querySelector('.main-section-columns-container');
            addMainSectionColumn(columnsContainer, {
                id: 'new_column_' + Date.now(),
                title: '新列',
                description: ''
            });
        });

        // 添加列
        const columnsContainer = clone.querySelector('.main-section-columns-container');
        if (section.columns && section.columns.length) {
            section.columns.forEach((column, columnIndex) => {
                addMainSectionColumn(columnsContainer, column, columnIndex);
            });
        }

        // 添加类型变更事件
        typeSelect.addEventListener('change', function() {
            const sectionElement = this.closest('.main-section');
            const columnsContainer = sectionElement.querySelector('.main-section-columns-container');
            const columns = columnsContainer.querySelectorAll('.main-section-column');

            // 对每个列应用类型特定的设置
            columns.forEach(column => {
                const chartSettings = column.querySelector('.main-section-column-chart-settings');
                const balanceSettings = column.querySelector('.main-section-column-balance-settings');

                if (chartSettings) {
                    chartSettings.style.display = this.value === 'chart_row' ? 'block' : 'none';
                }

                if (balanceSettings) {
                    balanceSettings.style.display = this.value === 'account_row' ? 'block' : 'none';
                }
            });
        });

        mainSectionsContainer.appendChild(clone);
    }

    /**
     * 添加主内容区列
     * @param {HTMLElement} container - 容器元素
     * @param {Object} column - 列配置
     * @param {number} index - 索引
     */
    function addMainSectionColumn(container, column, index) {
        if (!container) return;

        const template = document.getElementById('main-section-column-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.main-section-column-index').textContent = index !== undefined ? index + 1 : container.children.length + 1;

        // 设置值
        clone.querySelector('.main-section-column-title').value = column.title || '';
        clone.querySelector('.main-section-column-id').value = column.id || '';
        clone.querySelector('.main-section-column-description').value = column.description || '';

        // 设置图表设置
        const chartSettings = clone.querySelector('.main-section-column-chart-settings');
        const balanceSettings = clone.querySelector('.main-section-column-balance-settings');
        const sectionElement = container.closest('.main-section');
        const typeSelect = sectionElement ? sectionElement.querySelector('.main-section-type') : null;

        if (chartSettings && balanceSettings && typeSelect) {
            // 根据区块类型显示/隐藏相应设置
            chartSettings.style.display = typeSelect.value === 'chart_row' ? 'block' : 'none';
            balanceSettings.style.display = typeSelect.value === 'account_row' ? 'block' : 'none';

            if (column.chart_id) {
                clone.querySelector('.main-section-column-chart-id').value = column.chart_id;
            }

            if (column.height) {
                clone.querySelector('.main-section-column-chart-height').value = column.height;
            }

            // 添加余额项按钮事件
            const addBalanceItemButton = clone.querySelector('.add-balance-item');
            if (addBalanceItemButton) {
                addBalanceItemButton.addEventListener('click', function() {
                    const balanceItemsContainer = this.closest('.main-section-column-balance-settings').querySelector('.balance-items-container');
                    addBalanceItem(balanceItemsContainer, {
                        label: '新余额项',
                        value: '1000.00',  // 使用更明显的默认值
                        has_tooltip: false
                    });
                });
            }

            // 添加余额项
            if (typeSelect.value === 'account_row' && column.balance_items && column.balance_items.length) {
                const balanceItemsContainer = clone.querySelector('.balance-items-container');
                if (balanceItemsContainer) {
                    column.balance_items.forEach((item, itemIndex) => {
                        addBalanceItem(balanceItemsContainer, item, itemIndex);
                    });
                }
            }
        }

        // 设置指南链接
        const hasGuideLinkCheckbox = clone.querySelector('.main-section-column-has-guide-link');
        const guideLinkContainer = clone.querySelector('.main-section-column-guide-link-container');

        if (column.guide_link) {
            hasGuideLinkCheckbox.checked = true;
            guideLinkContainer.style.display = 'grid';

            clone.querySelector('.main-section-column-guide-link-text').value = column.guide_link.text || '';
            clone.querySelector('.main-section-column-guide-link-url').value = column.guide_link.url || '';
        }

        // 添加指南链接显示/隐藏事件
        hasGuideLinkCheckbox.addEventListener('change', function() {
            guideLinkContainer.style.display = this.checked ? 'grid' : 'none';
        });

        // 设置按钮
        const hasButtonCheckbox = clone.querySelector('.main-section-column-has-button');
        const buttonContainer = clone.querySelector('.main-section-column-button-container');

        if (column.button) {
            hasButtonCheckbox.checked = true;
            buttonContainer.style.display = 'grid';

            clone.querySelector('.main-section-column-button-text').value = column.button.text || '';
            clone.querySelector('.main-section-column-button-url').value = column.button.url || '';
        }

        // 添加按钮显示/隐藏事件
        hasButtonCheckbox.addEventListener('change', function() {
            buttonContainer.style.display = this.checked ? 'grid' : 'none';
        });

        // 添加删除事件
        clone.querySelector('.remove-main-section-column').addEventListener('click', function() {
            this.closest('.main-section-column').remove();
            // 更新索引
            updateMainSectionColumnIndices(container);
        });

        container.appendChild(clone);
    }

    /**
     * 更新导航菜单项索引
     */
    function updateNavItemIndices() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach((item, index) => {
            const indexElement = item.querySelector('.nav-item-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 更新侧边栏分类索引
     */
    function updateSidebarCategoryIndices() {
        const categories = document.querySelectorAll('.sidebar-category');
        categories.forEach((category, index) => {
            const indexElement = category.querySelector('.sidebar-category-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 更新侧边栏菜单项索引
     * @param {HTMLElement} container - 容器元素
     */
    function updateSidebarItemIndices(container) {
        if (!container) return;

        const items = container.querySelectorAll('.sidebar-item');
        items.forEach((item, index) => {
            const indexElement = item.querySelector('.sidebar-item-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 更新消息索引
     */
    function updateMessageIndices() {
        const messages = document.querySelectorAll('.message-item');
        messages.forEach((message, index) => {
            const indexElement = message.querySelector('.message-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 更新主内容区块索引
     */
    function updateMainSectionIndices() {
        const sections = document.querySelectorAll('.main-section');
        sections.forEach((section, index) => {
            const indexElement = section.querySelector('.main-section-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 更新主内容区列索引
     * @param {HTMLElement} container - 容器元素
     */
    function updateMainSectionColumnIndices(container) {
        if (!container) return;

        const columns = container.querySelectorAll('.main-section-column');
        columns.forEach((column, index) => {
            const indexElement = column.querySelector('.main-section-column-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 添加余额项
     * @param {HTMLElement} container - 容器元素
     * @param {Object} item - 余额项
     * @param {number} index - 索引
     */
    function addBalanceItem(container, item, index) {
        if (!container) return;

        const template = document.getElementById('balance-item-template');
        if (!template) return;

        const clone = document.importNode(template.content, true);

        // 设置索引
        clone.querySelector('.balance-item-index').textContent = index !== undefined ? index + 1 : container.children.length + 1;

        // 设置值
        clone.querySelector('.balance-item-label').value = item.label || '';
        clone.querySelector('.balance-item-value').value = item.value || '';
        clone.querySelector('.balance-item-has-tooltip').checked = item.has_tooltip || false;

        // 添加删除事件
        clone.querySelector('.remove-balance-item').addEventListener('click', function() {
            this.closest('.balance-item').remove();
            // 更新索引
            updateBalanceItemIndices(container);
        });

        container.appendChild(clone);
    }

    /**
     * 更新余额项索引
     * @param {HTMLElement} container - 容器元素
     */
    function updateBalanceItemIndices(container) {
        if (!container) return;

        const items = container.querySelectorAll('.balance-item');
        items.forEach((item, index) => {
            const indexElement = item.querySelector('.balance-item-index');
            if (indexElement) {
                indexElement.textContent = index + 1;
            }
        });
    }

    /**
     * 获取容器中的余额项
     * @param {HTMLElement} container - 容器元素
     * @returns {Array} 余额项数组
     */
    function getBalanceItemsFromContainer(container) {
        if (!container) return [];

        const items = [];
        const itemElements = container.querySelectorAll('.balance-item');

        if (!itemElements || itemElements.length === 0) {
            return [];
        }

        itemElements.forEach((element, index) => {
            const labelElement = element.querySelector('.balance-item-label');
            const valueElement = element.querySelector('.balance-item-value');
            const hasTooltipElement = element.querySelector('.balance-item-has-tooltip');

            items.push({
                label: labelElement ? labelElement.value : '',
                value: valueElement ? valueElement.value : '',
                has_tooltip: hasTooltipElement ? hasTooltipElement.checked : false
            });
        });

        return items;
    }

    /**
     * 获取模板名称
     * @param {string} templateId - 模板ID
     * @returns {string} 模板名称
     */
    function getTemplateName(templateId) {
        try {
            const select = document.getElementById('template-select');
            if (!select) return '支付宝商家后台';

            const option = select.options[select.selectedIndex];
            if (!option) return '支付宝商家后台';

            return option.textContent.trim();
        } catch (error) {
            console.error('Error getting template name:', error);
            return '支付宝商家后台';
        }
    }

    /**
     * 显示通知
     * @param {string} title - 标题
     * @param {string} message - 消息
     * @param {string} type - 类型（success, error, warning, info）
     */
    function showNotification(title, message, type) {
        const notification = document.getElementById('notification');
        const notificationTitle = document.getElementById('notification-title');
        const notificationMessage = document.getElementById('notification-message');
        const notificationIcon = document.getElementById('notification-icon');

        notificationTitle.textContent = title;
        notificationMessage.textContent = message;

        // 设置图标和颜色
        if (type === 'success') {
            notificationIcon.innerHTML = '<i class="ri-check-line text-green-500 text-xl"></i>';
        } else if (type === 'error') {
            notificationIcon.innerHTML = '<i class="ri-error-warning-line text-red-500 text-xl"></i>';
        } else if (type === 'warning') {
            notificationIcon.innerHTML = '<i class="ri-alert-line text-yellow-500 text-xl"></i>';
        } else if (type === 'info') {
            notificationIcon.innerHTML = '<i class="ri-information-line text-blue-500 text-xl"></i>';
        }

        // 显示通知
        notification.classList.remove('translate-x-full');

        // 5秒后自动隐藏
        setTimeout(hideNotification, 5000);
    }

    /**
     * 隐藏通知
     */
    function hideNotification() {
        const notification = document.getElementById('notification');
        notification.classList.add('translate-x-full');
    }

    // 数据标签页功能
    document.getElementById('load-table-data').addEventListener('click', function() {
        const tableName = document.getElementById('table-select').value;
        if (!tableName) {
            showNotification('警告', '请先选择要查看的表格', 'warning');
            return;
        }
        loadTableData(tableName);
    });

    /**
     * 加载表格数据
     * @param {string} tableName - 表格名称
     */
    async function loadTableData(tableName) {
        const loadingElement = document.getElementById('data-table-loading');
        const emptyElement = document.getElementById('data-table-empty');
        const contentElement = document.getElementById('data-table-content');

        try {
            // 显示加载状态
            loadingElement.classList.remove('hidden');
            emptyElement.classList.add('hidden');
            contentElement.classList.add('hidden');

            const response = await fetch(`/api/database_data/${tableName}`);
            if (!response.ok) {
                throw new Error(`Failed to load table data: ${response.statusText}`);
            }

            const result = await response.json();

            // 隐藏加载状态
            loadingElement.classList.add('hidden');

            if (result.data && result.data.length > 0) {
                // 存储当前表格数据
                currentTableData = result;
                currentTableName = tableName;

                renderTable(result);
                contentElement.classList.remove('hidden');
            } else {
                emptyElement.textContent = '该表格暂无数据';
                emptyElement.classList.remove('hidden');
            }

            showNotification('成功', `已加载 ${result.table_name} 表格数据`, 'success');
        } catch (error) {
            console.error('Error loading table data:', error);
            loadingElement.classList.add('hidden');
            emptyElement.textContent = `加载失败: ${error.message}`;
            emptyElement.classList.remove('hidden');
            showNotification('错误', `加载表格数据失败: ${error.message}`, 'error');
        }
    }

    /**
     * 渲染表格
     * @param {Object} result - 表格数据结果
     */
    function renderTable(result) {
        const contentElement = document.getElementById('data-table-content');

        let tableHtml = `
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    ${getTableDisplayName(result.table_name)}
                    <span class="text-sm text-gray-500">(共 ${result.pagination.total} 条记录)</span>
                </h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>`;

        // 生成表头
        result.columns.forEach(column => {
            tableHtml += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${getColumnDisplayName(column)}</th>`;
        });
        // 添加操作列
        tableHtml += `<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>`;

        tableHtml += `</tr></thead><tbody class="bg-white divide-y divide-gray-200">`;

        // 生成表格数据
        result.data.forEach((row, index) => {
            tableHtml += `<tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}">`;
            result.columns.forEach(column => {
                const value = row[column];
                const displayValue = value !== null && value !== undefined ? value : '-';
                tableHtml += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${displayValue}</td>`;
            });
            // 添加操作按钮
            tableHtml += `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="editTableRow('${result.table_name}', ${row.id})" class="text-indigo-600 hover:text-indigo-900 mr-3">
                        <i class="ri-edit-line"></i> 编辑
                    </button>
                    <button onclick="deleteTableRow('${result.table_name}', ${row.id})" class="text-red-600 hover:text-red-900">
                        <i class="ri-delete-bin-line"></i> 删除
                    </button>
                </td>`;
            tableHtml += `</tr>`;
        });

        tableHtml += `</tbody></table></div>`;

        // 添加分页信息
        if (result.pagination.pages > 1) {
            tableHtml += `
                <div class="mt-4 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        第 ${result.pagination.page} 页，共 ${result.pagination.pages} 页
                    </div>
                    <div class="text-sm text-gray-700">
                        每页 ${result.pagination.per_page} 条，共 ${result.pagination.total} 条记录
                    </div>
                </div>`;
        }

        tableHtml += `</div>`;
        contentElement.innerHTML = tableHtml;
    }

    /**
     * 获取表格显示名称
     * @param {string} tableName - 表格名称
     * @returns {string} 显示名称
     */
    function getTableDisplayName(tableName) {
        const tableNames = {
            'sensitive_words': '敏感词表',
            'sensitive_word_hits': '敏感词命中记录',
            'user': '用户表',
            'analytics_data': '分析数据表',
            'template_config': '模板配置表',
            'template_content': '模板内容表',
            'user_sample_data': '用户示例数据表',
            'group_sample_data': '群组示例数据表',
            'message_sample_data': '消息示例数据表',
            'sensitive_word_sample_data': '敏感词示例数据表',
            'banned_user_sample_data': '封禁用户示例数据表',
            'menu_configuration': '菜单配置表',
            'system_settings': '系统设置表'
        };
        return tableNames[tableName] || tableName;
    }

    /**
     * 获取列显示名称
     * @param {string} columnName - 列名
     * @returns {string} 显示名称
     */
    function getColumnDisplayName(columnName) {
        const columnNames = {
            'id': 'ID',
            'word': '敏感词',
            'category': '分类',
            'severity': '严重程度',
            'action': '处理动作',
            'description': '描述',
            'is_active': '是否激活',
            'created_at': '创建时间',
            'updated_at': '更新时间',
            'user_id': '用户ID',
            'username': '用户名',
            'sensitive_word_id': '敏感词ID',
            'hit_word': '命中词',
            'original_content': '原始内容',
            'filtered_content': '过滤后内容',
            'hit_context': '命中上下文',
            'action_taken': '采取行动',
            'source_type': '来源类型',
            'source_id': '来源ID',
            'ip_address': 'IP地址',
            'user_agent': '用户代理',
            'is_processed': '是否已处理',
            'processed_at': '处理时间',
            'password': '密码',
            'is_admin': '是否管理员',
            'date': '日期',
            'time_range': '时间范围',
            'transaction_amount': '交易金额',
            'order_count': '订单数量',
            'visitor_count': '访客数量',
            'conversion_rate': '转化率',
            'template_name': '模板名称',
            'content_type': '内容类型',
            'content_key': '内容键',
            'content_value': '内容值',
            'content_description': '内容描述',
            // 用户示例数据表字段
            'phone': '手机号',
            'user_code': '用户编码',
            'avatar_url': '头像URL',
            'create_time': '创建时间',
            'status': '状态',
            // 群组示例数据表字段
            'group_id': '群组ID',
            'group_name': '群组名称',
            'group_type': '群组类型',
            'member_count': '成员数量',
            'owner_id': '群主ID',
            'group_portrait': '群组头像',
            'group_description': '群组描述',
            'is_mute': '是否禁言',
            'is_private': '是否私有',
            // 消息示例数据表字段
            'message_id': '消息ID',
            'message_type': '消息类型',
            'content': '内容',
            'receiver': '接收者',
            'send_time': '发送时间',
            // 封禁用户示例数据表字段
            'banned_user_id': '封禁用户ID',
            'banned_username': '封禁用户名',
            'ban_reason': '封禁原因',
            'ban_start_time': '封禁开始时间',
            'ban_end_time': '封禁结束时间',
            'ban_duration': '封禁时长',
            'banned_by': '封禁操作者',
            'is_permanent': '是否永久封禁',
            // 菜单配置表字段
            'menu_type': '菜单类型',
            'parent_id': '父菜单ID',
            'menu_key': '菜单键',
            'menu_text': '菜单文字',
            'menu_url': '菜单链接',
            'menu_icon': '菜单图标',
            'sort_order': '排序顺序',
            'is_collapsible': '是否可折叠',
            'is_expanded': '是否展开',
            'is_active': '是否激活',
            // 系统设置表字段
            'setting_category': '设置分类',
            'setting_key': '设置键',
            'setting_value': '设置值',
            'setting_description': '设置描述'
        };
        return columnNames[columnName] || columnName;
    }

    // 全局变量存储当前表格数据
    let currentTableData = null;
    let currentTableName = null;

    // 编辑表格行
    window.editTableRow = function(tableName, rowId) {
        if (!currentTableData) {
            showNotification('错误', '请先加载表格数据', 'error');
            return;
        }

        const row = currentTableData.data.find(r => r.id == rowId);
        if (!row) {
            showNotification('错误', '找不到要编辑的数据', 'error');
            return;
        }

        openEditModal(tableName, row);
    };

    // 删除表格行
    window.deleteTableRow = function(tableName, rowId) {
        if (!confirm('确定要删除这条数据吗？此操作不可恢复！')) {
            return;
        }

        deleteTableData(tableName, rowId);
    };

    // 打开编辑模态框
    function openEditModal(tableName, rowData) {
        const modal = document.getElementById('edit-modal');
        const title = document.getElementById('edit-modal-title');
        const fieldsContainer = document.getElementById('edit-form-fields');

        title.textContent = `编辑${getTableDisplayName(tableName)}数据`;

        // 清空表单字段
        fieldsContainer.innerHTML = '';

        // 动态生成表单字段
        currentTableData.columns.forEach(column => {
            if (column === 'id') return; // 跳过ID字段

            const fieldDiv = document.createElement('div');
            fieldDiv.innerHTML = `
                <label for="edit-${column}" class="block text-sm font-medium text-gray-700">
                    ${getColumnDisplayName(column)}
                </label>
                <input type="text"
                       id="edit-${column}"
                       name="${column}"
                       value="${rowData[column] || ''}"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            `;
            fieldsContainer.appendChild(fieldDiv);
        });

        // 存储当前编辑的数据
        modal.dataset.tableName = tableName;
        modal.dataset.rowId = rowData.id;

        // 显示模态框
        modal.classList.remove('hidden');
    }

    // 关闭编辑模态框
    function closeEditModal() {
        const modal = document.getElementById('edit-modal');
        modal.classList.add('hidden');
    }

    // 模态框事件监听
    document.getElementById('close-edit-modal').addEventListener('click', closeEditModal);
    document.getElementById('cancel-edit').addEventListener('click', closeEditModal);

    // 表单提交处理
    document.getElementById('edit-form').addEventListener('submit', async function(e) {
        e.preventDefault();

        const modal = document.getElementById('edit-modal');
        const tableName = modal.dataset.tableName;
        const rowId = modal.dataset.rowId;

        // 收集表单数据
        const formData = new FormData(this);
        const updateData = {};
        for (let [key, value] of formData.entries()) {
            updateData[key] = value;
        }

        try {
            await updateTableData(tableName, rowId, updateData);
            closeEditModal();
            // 重新加载表格数据
            loadTableData(tableName);
            showNotification('成功', '数据更新成功', 'success');
        } catch (error) {
            showNotification('错误', `更新失败: ${error.message}`, 'error');
        }
    });

    // 更新表格数据
    async function updateTableData(tableName, rowId, updateData) {
        const response = await fetch(`/api/database_data/${tableName}/${rowId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updateData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || '更新失败');
        }

        return await response.json();
    }

    // 删除表格数据
    async function deleteTableData(tableName, rowId) {
        try {
            const response = await fetch(`/api/database_data/${tableName}/${rowId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error || '删除失败');
            }

            // 重新加载表格数据
            loadTableData(tableName);
            showNotification('成功', '数据删除成功', 'success');
        } catch (error) {
            showNotification('错误', `删除失败: ${error.message}`, 'error');
        }
    }

    // 自动加载当前选中模板的配置
    const templateId = document.getElementById('template-select').value;
    const templateOption = document.querySelector(`#template-select option[value="${templateId}"]`);
    const templateName = templateOption.textContent.trim().includes('finviz数据提取后台') ? 'yehuo' : 'yehuo';
    loadConfig(templateName);
});
