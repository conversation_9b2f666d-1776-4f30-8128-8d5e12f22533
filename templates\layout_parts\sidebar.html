<aside class="w-56 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
  <div class="sidebar-container py-3">
    <div class="px-4 mb-4">
      <div class="relative w-full">
        <input
          type="text"
          placeholder="搜索菜单"
          class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
        />
        <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
          <i class="ri-search-line"></i>
        </div>
      </div>
    </div>

    <div class="px-4 py-2">
      <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
        <span>{{ contents.sidebar_app_center|default('应用中心') }}</span>
        <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
          <i class="ri-arrow-down-s-line"></i>
        </button>
      </div>
      <ul class="space-y-0.5">
        {% for item in sidebar_items.app_center %}
        <li>
          <a
            href="{{ item.url }}"
            class="flex items-center px-3 py-2.5 text-sm {% if current_page == item.id or (current_page is not defined and item.id == 'home') %}text-primary bg-blue-50{% else %}text-gray-700 hover:bg-gray-50{% endif %} rounded-lg sidebar-menu-item {% if current_page == item.id or (current_page is not defined and item.id == 'home') %}active{% endif %} transition-all"
          >
            <span class="w-6 h-6 flex items-center justify-center mr-3 {% if current_page == item.id or (current_page is not defined and item.id == 'home') %}{% else %}text-gray-500{% endif %} sidebar-icon">
              <i class="{{ item.icon }} text-lg"></i>
            </span>
            <span class="{% if current_page == item.id or (current_page is not defined and item.id == 'home') %}font-medium{% endif %}">{{ item.text }}</span>
            {% if item.badge %}
            <span class="ml-auto bg-{{ item.badge.color }}-500 text-white text-xs px-1.5 py-0.5 rounded-full">{{ item.badge.text }}</span>
            {% endif %}
          </a>
        </li>
        {% endfor %}

      </ul>
    </div>

    <div class="mt-2 px-4 py-2">
      <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
        <span>{{ contents.sidebar_product_service|default('产品服务') }}</span>
        <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
          <i class="ri-arrow-down-s-line"></i>
        </button>
      </div>
      <ul class="space-y-0.5">
        {% for item in sidebar_items.product_service %}
        <li>
          <a
            href="{{ item.url }}"
            class="flex items-center px-3 py-2.5 text-sm {% if current_page == item.id %}text-primary bg-blue-50{% else %}text-gray-700 hover:bg-gray-50{% endif %} rounded-lg sidebar-menu-item {% if current_page == item.id %}active{% endif %} transition-all"
          >
            <span class="w-6 h-6 flex items-center justify-center mr-3 {% if current_page == item.id %}{% else %}text-gray-500{% endif %} sidebar-icon">
              <i class="{{ item.icon }} text-lg"></i>
            </span>
            <span class="{% if current_page == item.id %}font-medium{% endif %}">{{ item.text }}</span>
            {% if item.badge %}
            <span class="ml-auto bg-{{ item.badge.color }}-500 text-white text-xs px-1.5 py-0.5 rounded-full">{{ item.badge.text }}</span>
            {% endif %}
          </a>
        </li>
        {% endfor %}

      </ul>
    </div>

    <div class="mt-2 px-4 py-2">
      <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
        <span>营销推广</span>
        <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
          <i class="ri-arrow-down-s-line"></i>
        </button>
      </div>
      <ul class="space-y-0.5">
        {% for item in sidebar_items.marketing_promotion %}
        <li>
          <a
            href="{{ item.url }}"
            class="flex items-center px-3 py-2.5 text-sm {% if current_page == item.id %}text-primary bg-blue-50{% else %}text-gray-700 hover:bg-gray-50{% endif %} rounded-lg sidebar-menu-item {% if current_page == item.id %}active{% endif %} transition-all"
          >
            <span class="w-6 h-6 flex items-center justify-center mr-3 {% if current_page == item.id %}{% else %}text-gray-500{% endif %} sidebar-icon">
              <i class="{{ item.icon }} text-lg"></i>
            </span>
            <span class="{% if current_page == item.id %}font-medium{% endif %}">{{ item.text }}</span>
            {% if item.badge %}
            <span class="ml-auto text-xs px-1.5 py-0.5 text-{{ item.badge.color }}-600 bg-{{ item.badge.color }}-100 rounded">{{ item.badge.text }}</span>
            {% endif %}
          </a>
        </li>
        {% endfor %}
      </ul>
    </div>

    <div class="mt-2 px-4 py-2">
      <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
        <span>店铺管理</span>
        <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
          <i class="ri-arrow-down-s-line"></i>
        </button>
      </div>
      <ul class="space-y-0.5">
        {% for item in sidebar_items.shop_management %}
        <li>
          <a
            href="{{ item.url }}"
            class="flex items-center px-3 py-2.5 text-sm {% if current_page == item.id %}text-primary bg-blue-50{% else %}text-gray-700 hover:bg-gray-50{% endif %} rounded-lg sidebar-menu-item {% if current_page == item.id %}active{% endif %} transition-all"
          >
            <span class="w-6 h-6 flex items-center justify-center mr-3 {% if current_page == item.id %}{% else %}text-gray-500{% endif %} sidebar-icon">
              <i class="{{ item.icon }} text-lg"></i>
            </span>
            <span class="{% if current_page == item.id %}font-medium{% endif %}">{{ item.text }}</span>
            {% if item.badge %}
            <span class="ml-auto bg-{{ item.badge.color }}-500 text-white text-xs px-1.5 py-0.5 rounded-full">{{ item.badge.text }}</span>
            {% endif %}
          </a>
        </li>
        {% endfor %}
      </ul>
    </div>
  </div>
</aside>