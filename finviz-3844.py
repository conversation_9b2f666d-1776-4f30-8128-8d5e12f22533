from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
import os
import random
import json
from datetime import datetime, timezone, timedelta

# 初始化Flask应用
app = Flask(__name__)
app.secret_key = os.urandom(24)

# 数据库配置 - 使用SQLite代替MySQL
import os
basedir = os.path.abspath(os.path.dirname(__file__))
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///' + os.path.join(basedir, 'data.sqlite')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库
db = SQLAlchemy(app)

# 初始化登录管理器
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'admin_login'

# 定义模型
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)

    def __repr__(self):
        return f'<User {self.username}>'

class TemplateConfig(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    template_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.Boolean, default=False)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<TemplateConfig {self.template_name}>'

class TemplateCredential(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template_config.id'), nullable=False)
    username = db.Column(db.String(50), nullable=False)
    password = db.Column(db.String(100), nullable=False)
    description = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    template = db.relationship('TemplateConfig', backref=db.backref('credentials', lazy=True))

    def __repr__(self):
        return f'<TemplateCredential {self.username} for template {self.template_id}>'

class TemplateCustomization(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template_config.id'), nullable=False)
    primary_color = db.Column(db.String(20), default="#1677FF")
    layout_style = db.Column(db.String(20), default="default")  # default, compact, wide
    show_shop_overview = db.Column(db.Boolean, default=True)
    show_balance = db.Column(db.Boolean, default=True)
    show_modules = db.Column(db.Boolean, default=True)
    show_statistics = db.Column(db.Boolean, default=True)
    custom_css = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    template = db.relationship('TemplateConfig', backref=db.backref('customization', uselist=False))

    def __repr__(self):
        return f'<TemplateCustomization for template {self.template_id}>'

class TemplateContent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template_config.id'), nullable=False)
    content_type = db.Column(db.String(50), nullable=False)  # 'button', 'title', 'description', 'number', etc.
    content_key = db.Column(db.String(100), nullable=False)  # 唯一标识符，如 'shop_title', 'recharge_button', etc.
    content_value = db.Column(db.Text, nullable=False)  # 内容值
    content_description = db.Column(db.String(200))  # 描述，帮助管理员理解这个内容的用途
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    template = db.relationship('TemplateConfig', backref=db.backref('contents', lazy=True))

    __table_args__ = (db.UniqueConstraint('template_id', 'content_key', name='uix_template_content'),)

    def __repr__(self):
        return f'<TemplateContent {self.content_key} for template {self.template_id}>'

# 数据分析相关模型
class AnalyticsData(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    time_range = db.Column(db.String(20), nullable=False)  # 'today', 'yesterday', 'week', 'month'
    transaction_amount = db.Column(db.Float, nullable=False)
    order_count = db.Column(db.Integer, nullable=False)
    visitor_count = db.Column(db.Integer, nullable=False)
    conversion_rate = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def __repr__(self):
        return f'<AnalyticsData {self.date} {self.time_range}>'

class TransactionTrend(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    analytics_id = db.Column(db.Integer, db.ForeignKey('analytics_data.id'), nullable=False)
    hour = db.Column(db.Integer, nullable=False)  # 0-23
    amount = db.Column(db.Float, nullable=False)
    order_count = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    analytics = db.relationship('AnalyticsData', backref=db.backref('transaction_trends', lazy=True))

    def __repr__(self):
        return f'<TransactionTrend {self.analytics_id} hour:{self.hour}>'

class UserSource(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    analytics_id = db.Column(db.Integer, db.ForeignKey('analytics_data.id'), nullable=False)
    source_name = db.Column(db.String(50), nullable=False)  # 'search_engine', 'direct', 'social', 'ads', 'other'
    visitor_count = db.Column(db.Integer, nullable=False)
    conversion_rate = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    analytics = db.relationship('AnalyticsData', backref=db.backref('user_sources', lazy=True))

    def __repr__(self):
        return f'<UserSource {self.analytics_id} {self.source_name}>'

class ProductSales(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    analytics_id = db.Column(db.Integer, db.ForeignKey('analytics_data.id'), nullable=False)
    rank = db.Column(db.Integer, nullable=False)
    product_name = db.Column(db.String(100), nullable=False)
    product_image = db.Column(db.String(200), nullable=False)
    sales_amount = db.Column(db.Float, nullable=False)
    sales_count = db.Column(db.Integer, nullable=False)
    conversion_rate = db.Column(db.Float, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    analytics = db.relationship('AnalyticsData', backref=db.backref('product_sales', lazy=True))

    def __repr__(self):
        return f'<ProductSales {self.analytics_id} rank:{self.rank}>'

# 敏感词模型
class SensitiveWord(db.Model):
    __tablename__ = 'sensitive_words'

    id = db.Column(db.Integer, primary_key=True)
    word = db.Column(db.String(255), nullable=False, unique=True)
    category = db.Column(db.String(100), default='默认')
    severity = db.Column(db.String(20), default='中等')  # 低、中等、高
    action = db.Column(db.String(50), default='过滤')  # 过滤、警告、封禁
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<SensitiveWord {self.word}>'

# 敏感词命中记录模型
class SensitiveWordHit(db.Model):
    __tablename__ = 'sensitive_word_hits'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(100), nullable=False)
    sensitive_word_id = db.Column(db.Integer, db.ForeignKey('sensitive_words.id'), nullable=False)
    hit_word = db.Column(db.String(255), nullable=False)  # 实际命中的词
    original_content = db.Column(db.Text, nullable=False)  # 原始内容
    filtered_content = db.Column(db.Text)  # 过滤后的内容
    hit_context = db.Column(db.Text)  # 命中上下文
    action_taken = db.Column(db.String(50), nullable=False)  # 采取的行动：过滤、警告、封禁
    source_type = db.Column(db.String(50), default='message')  # 来源类型：message、comment、post等
    source_id = db.Column(db.String(100))  # 来源ID
    ip_address = db.Column(db.String(45))  # IP地址
    user_agent = db.Column(db.Text)  # 用户代理
    is_processed = db.Column(db.Boolean, default=False)  # 是否已处理
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))
    processed_at = db.Column(db.DateTime)

    # 关联敏感词
    sensitive_word = db.relationship('SensitiveWord', backref=db.backref('hits', lazy=True))

    def __repr__(self):
        return f'<SensitiveWordHit {self.hit_word} by {self.username}>'

# 用户示例数据模型
class UserSampleData(db.Model):
    __tablename__ = 'user_sample_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    user_code = db.Column(db.String(50), nullable=False)
    avatar_url = db.Column(db.String(200))
    create_time = db.Column(db.String(50), nullable=False)
    status = db.Column(db.String(20), default='正常')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<UserSampleData {self.username}>'

# 群组示例数据模型
class GroupSampleData(db.Model):
    __tablename__ = 'group_sample_data'

    id = db.Column(db.Integer, primary_key=True)
    group_id = db.Column(db.String(100), nullable=False)
    group_name = db.Column(db.String(200), nullable=False)
    avatar = db.Column(db.String(200), default='群')
    owner = db.Column(db.String(100), nullable=False)
    member_count = db.Column(db.Integer, default=0)
    group_type = db.Column(db.String(50), default='普通群组')
    create_time = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<GroupSampleData {self.group_name}>'

# 消息示例数据模型
class MessageSampleData(db.Model):
    __tablename__ = 'message_sample_data'

    id = db.Column(db.Integer, primary_key=True)
    message_id = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.String(100), nullable=False)
    message_type = db.Column(db.String(20), nullable=False)  # text, image, file, voice, video
    content = db.Column(db.Text, nullable=False)
    receiver = db.Column(db.String(100), nullable=False)
    send_time = db.Column(db.String(50), nullable=False)
    status = db.Column(db.String(20), default='已发送')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<MessageSampleData {self.message_id}>'

# 敏感词示例数据模型
class SensitiveWordSampleData(db.Model):
    __tablename__ = 'sensitive_word_sample_data'

    id = db.Column(db.Integer, primary_key=True)
    word = db.Column(db.String(255), nullable=False)
    category = db.Column(db.String(100), default='默认')
    severity = db.Column(db.String(20), default='中等')  # 低、中等、高
    action = db.Column(db.String(50), default='过滤')  # 过滤、警告、封禁
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<SensitiveWordSampleData {self.word}>'

# 封禁用户示例数据模型
class BannedUserSampleData(db.Model):
    __tablename__ = 'banned_user_sample_data'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(100), nullable=False)
    username = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    ban_reason = db.Column(db.String(200), nullable=False)  # 封禁原因
    ban_time = db.Column(db.String(50), nullable=False)  # 封禁时间
    ban_expire = db.Column(db.String(50), nullable=False)  # 封禁到期时间，"永久封禁"表示永久
    ban_type = db.Column(db.String(20), default='临时')  # 临时、永久
    ban_operator = db.Column(db.String(100), default='系统管理员')  # 封禁操作员
    ban_level = db.Column(db.String(20), default='普通')  # 普通、严重、极严重
    is_active = db.Column(db.Boolean, default=True)  # 是否仍在封禁中
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))

    def __repr__(self):
        return f'<BannedUserSampleData {self.username}>'

# 菜单配置模型
class MenuConfiguration(db.Model):
    __tablename__ = 'menu_configuration'

    id = db.Column(db.Integer, primary_key=True)
    menu_type = db.Column(db.String(50), nullable=False)  # header, sidebar
    parent_id = db.Column(db.Integer, db.ForeignKey('menu_configuration.id'))
    menu_key = db.Column(db.String(100), nullable=False)
    menu_text = db.Column(db.String(200), nullable=False)
    menu_url = db.Column(db.String(200))
    menu_icon = db.Column(db.String(100))
    sort_order = db.Column(db.Integer, default=0)
    is_collapsible = db.Column(db.Boolean, default=False)
    is_expanded = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))

    # 自关联关系
    children = db.relationship('MenuConfiguration', backref=db.backref('parent', remote_side=[id]))

    def __repr__(self):
        return f'<MenuConfiguration {self.menu_text}>'

# 系统设置和标签模型
class SystemSettings(db.Model):
    __tablename__ = 'system_settings'

    id = db.Column(db.Integer, primary_key=True)
    setting_category = db.Column(db.String(100), nullable=False)  # labels, pagination, buttons等
    setting_key = db.Column(db.String(100), nullable=False)
    setting_value = db.Column(db.Text, nullable=False)
    setting_description = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))

    __table_args__ = (db.UniqueConstraint('setting_category', 'setting_key', name='uix_category_key'),)

    def __repr__(self):
        return f'<SystemSettings {self.setting_category}.{self.setting_key}>'

# 创建智能静态文件URL生成函数
@app.template_global()
def smart_static(filename):
    """智能静态文件URL生成 - 自动适配本地和服务器环境"""
    from flask import request

    base_url = url_for('static', filename=filename)

    # 如果是域名访问，添加/app前缀
    host = request.headers.get('Host', '')
    if 'find001.info' in host:
        return '/app' + base_url

    # 本地访问保持原样
    return base_url

@login_manager.user_loader
def load_user(user_id):
    # 使用 db.session.get 代替 query.get (适用于 SQLAlchemy 2.0)
    return db.session.get(User, int(user_id))

# 创建数据库表
with app.app_context():
    db.create_all()

    # 清理数据库，只保留JJ竞技世界模板
    def cleanup_database():
        try:
            # 获取JJ竞技世界模板
            yehuo_template = TemplateConfig.query.filter_by(template_name='yehuo.html').first()

            # 如果JJ竞技世界模板不存在，创建一个
            if not yehuo_template:
                yehuo_template = TemplateConfig(template_name='yehuo.html', is_active=True)
                db.session.add(yehuo_template)
                db.session.flush()  # 获取ID

            # 删除其他模板的内容配置
            TemplateContent.query.filter(TemplateContent.template_id != yehuo_template.id).delete()

            # 删除其他模板的自定义配置
            TemplateCustomization.query.filter(TemplateCustomization.template_id != yehuo_template.id).delete()

            # 删除其他模板的凭证
            TemplateCredential.query.filter(TemplateCredential.template_id != yehuo_template.id).delete()

            # 删除其他模板
            TemplateConfig.query.filter(TemplateConfig.id != yehuo_template.id).delete()

            # 确保JJ竞技世界模板是激活的
            yehuo_template.is_active = True

            db.session.commit()
            print("数据库清理完成，只保留JJ竞技世界模板")
        except Exception as e:
            db.session.rollback()
            print(f"清理数据库时出错: {str(e)}")

    # 执行数据库清理
    cleanup_database()

    # 检查是否有模板配置，如果没有则创建默认配置
    if not TemplateConfig.query.first():
        # 检查JJ竞技世界模板文件是否存在
        template_file = 'yehuo.html'
        if os.path.exists(os.path.join(app.template_folder, template_file)):
            # 添加JJ竞技世界模板并设置为激活状态
            db.session.add(TemplateConfig(template_name=template_file, is_active=True))
        else:
            print(f"警告: 模板文件 {template_file} 不存在")

    # 检查是否有管理员用户，如果没有则创建默认管理员
    if not User.query.filter_by(username='admin').first():
        admin = User(username='admin', password='admin123')
        db.session.add(admin)

    # 为每个模板添加默认登录凭证和自定义配置
    templates = TemplateConfig.query.all()
    for template in templates:
        # 检查该模板是否已有凭证
        if not TemplateCredential.query.filter_by(template_id=template.id).first():
            # 设置JJ竞技世界模板的默认凭证
            credential = TemplateCredential(
                template_id=template.id,
                username='yehuo_user',
                password='123456',
                description='JJ竞技世界模板默认登录凭证'
            )
            db.session.add(credential)

        # 检查该模板是否已有自定义配置
        if not TemplateCustomization.query.filter_by(template_id=template.id).first():
            # 设置JJ竞技世界模板的默认自定义配置
            customization = TemplateCustomization(
                template_id=template.id,
                primary_color="#1e293b",
                layout_style="default"
            )
            db.session.add(customization)

            # 检查该模板是否已有内容配置
            if not TemplateContent.query.filter_by(template_id=template.id).first():
                default_contents = [
                    # 顶部导航
                    TemplateContent(template_id=template.id, content_type='title', content_key='header_title',
                                   content_value='JJ竞技世界管理系统', content_description='顶部导航栏标题'),
                    TemplateContent(template_id=template.id, content_type='text', content_key='language_text',
                                   content_value='语言', content_description='语言选择文本'),
                    TemplateContent(template_id=template.id, content_type='text', content_key='username',
                                   content_value='admin', content_description='用户名显示'),

                    # 面包屑导航
                    TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_parent',
                                   content_value='用户管理', content_description='面包屑导航父级'),
                    TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_current',
                                   content_value='用户列表', content_description='面包屑导航当前页'),

                    # 搜索框
                    TemplateContent(template_id=template.id, content_type='placeholder', content_key='search_placeholder',
                                   content_value='搜索用户', content_description='搜索框占位文字'),

                    # 按钮文本
                    TemplateContent(template_id=template.id, content_type='button', content_key='add_button',
                                   content_value='添加用户', content_description='添加按钮文字'),
                    TemplateContent(template_id=template.id, content_type='button', content_key='more_button',
                                   content_value='更多', content_description='更多按钮文字'),

                    # 表格标题
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_1',
                                   content_value='序号', content_description='表格标题1'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_2',
                                   content_value='用户ID', content_description='表格标题2'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_3',
                                   content_value='用户名', content_description='表格标题3'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_4',
                                   content_value='手机号', content_description='表格标题4'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_5',
                                   content_value='用户ID', content_description='表格标题5'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_6',
                                   content_value='头像', content_description='表格标题6'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_7',
                                   content_value='创建时间', content_description='表格标题7'),
                    TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_8',
                                   content_value='操作', content_description='表格标题8'),

                    # 分页信息
                    TemplateContent(template_id=template.id, content_type='text', content_key='total_records',
                                   content_value='共 25371 条', content_description='总记录数'),
                ]
                db.session.add_all(default_contents)

    db.session.commit()

    # 填充示例数据
    def populate_sample_data():
        """检查示例数据（数据应通过migrate_all_data.py脚本导入）"""
        try:
            print("=== 检查示例数据 ===")

            # 检查用户示例数据
            user_count = UserSampleData.query.count()
            if user_count == 0:
                print("警告: 数据库中没有用户示例数据")
            else:
                print(f"用户示例数据: {user_count} 条")

            # 检查群组示例数据（数据应通过migrate_group_data.py脚本导入）
            group_count = GroupSampleData.query.count()
            if group_count == 0:
                print("警告: 数据库中没有群组示例数据")
                print("请运行 'python migrate_group_data.py' 来导入群组数据")
            else:
                print(f"数据库中已有 {group_count} 条群组数据")

            # 检查敏感词示例数据
            sensitive_word_count = SensitiveWordSampleData.query.count()
            if sensitive_word_count == 0:
                print("警告: 数据库中没有敏感词示例数据")
            else:
                print(f"敏感词示例数据: {sensitive_word_count} 条")

            # 检查消息示例数据
            message_count = MessageSampleData.query.count()
            if message_count == 0:
                print("警告: 数据库中没有消息示例数据")
            else:
                print(f"消息示例数据: {message_count} 条")

            # 检查封禁用户示例数据
            banned_user_count = BannedUserSampleData.query.count()
            if banned_user_count == 0:
                print("警告: 数据库中没有封禁用户示例数据")
            else:
                print(f"封禁用户示例数据: {banned_user_count} 条")

            # 检查菜单配置数据
            menu_config_count = MenuConfiguration.query.count()
            if menu_config_count == 0:
                print("警告: 数据库中没有菜单配置数据")
            else:
                print(f"菜单配置数据: {menu_config_count} 条")

            # 检查系统设置数据
            system_settings_count = SystemSettings.query.count()
            if system_settings_count == 0:
                print("警告: 数据库中没有系统设置数据")
            else:
                print(f"系统设置数据: {system_settings_count} 条")

            # 汇总检查结果
            missing_data = []
            if user_count == 0:
                missing_data.append("用户示例数据")
            if group_count == 0:
                missing_data.append("群组示例数据")
            if sensitive_word_count == 0:
                missing_data.append("敏感词示例数据")
            if message_count == 0:
                missing_data.append("消息示例数据")
            if banned_user_count == 0:
                missing_data.append("封禁用户示例数据")
            if menu_config_count == 0:
                missing_data.append("菜单配置数据")
            if system_settings_count == 0:
                missing_data.append("系统设置数据")

            if missing_data:
                print(f"\n警告: 以下数据类型在数据库中不存在:")
                for data_type in missing_data:
                    print(f"  - {data_type}")
                print(f"\n请运行以下命令导入示例数据:")
                print(f"  python migrate_all_data.py")
            else:
                print(f"\n✓ 所有示例数据都已存在于数据库中")

            print("示例数据检查完成")
        except Exception as e:
            db.session.rollback()
            print(f"填充示例数据时出错: {str(e)}")

    # 执行示例数据填充
    populate_sample_data()

def load_menu_configuration():
    """加载菜单配置数据"""
    try:
        # 获取侧边栏菜单配置
        sidebar_menus = MenuConfiguration.query.filter_by(
            menu_type='sidebar',
            is_active=True,
            parent_id=None
        ).order_by(MenuConfiguration.sort_order).all()

        menu_items = []
        for menu in sidebar_menus:
            menu_item = {
                'id': menu.menu_key,
                'text': menu.menu_text,
                'url': menu.menu_url,
                'icon': menu.menu_icon,
                'is_collapsible': menu.is_collapsible,
                'is_expanded': menu.is_expanded,
                'children': []
            }

            # 获取子菜单
            if menu.is_collapsible:
                sub_menus = MenuConfiguration.query.filter_by(
                    parent_id=menu.id,
                    is_active=True
                ).order_by(MenuConfiguration.sort_order).all()

                for sub_menu in sub_menus:
                    menu_item['children'].append({
                        'id': sub_menu.menu_key,
                        'text': sub_menu.menu_text,
                        'url': sub_menu.menu_url
                    })

            menu_items.append(menu_item)

        return menu_items
    except Exception as e:
        print(f"Error loading menu configuration: {str(e)}")
        return []

# API路由 - 获取模板配置
@app.route('/api/template_config/<template_name>')
def get_template_config(template_name):
    try:
        # 只允许JJ竞技世界模板
        if template_name != 'yehuo':
            template_name = 'yehuo'  # 强制使用JJ竞技世界模板

        # 从数据库加载菜单配置，构建模板配置
        menu_items = load_menu_configuration()

        # 获取活动模板
        active_template = TemplateConfig.query.filter_by(is_active=True).first()
        if not active_template:
            return jsonify({"error": "No active template found"}), 404

        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()

        # 构建配置对象
        config = {
            "template_id": str(active_template.id),
            "template_name": "JJ竞技世界管理系统",
            "version": "2.0.0",
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "global_settings": {
                "primary_color": customization.primary_color if customization else "#1677FF",
                "layout_style": customization.layout_style if customization else "default",
                "show_shop_overview": customization.show_shop_overview if customization else True,
                "show_balance": customization.show_balance if customization else True,
                "show_modules": customization.show_modules if customization else True,
                "show_statistics": customization.show_statistics if customization else True
            },
            "header": {
                "title": "JJ竞技世界管理系统",
                "logo": "ri-apps-2-line",
                "search": {
                    "enabled": True,
                    "placeholder": "搜索用户"
                },
                "navigation": []  # 从菜单配置生成
            },
            "sidebar": {
                "categories": []  # 从菜单配置生成
            },
            "rightbar": {
                "enabled": True,
                "date": datetime.now().strftime("%Y/%m/%d"),
                "title": "消息中心",
                "view_all": {
                    "text": "全部",
                    "url": "#"
                },
                "messages": []
            },
            "main_content": {
                "sections": []
            }
        }

        # 从数据库菜单配置生成侧边栏配置
        for menu_item in menu_items:
            category = {
                "id": menu_item['id'],
                "title": menu_item['text'],
                "icon": menu_item.get('icon', 'ri-home-line'),
                "url": menu_item.get('url', '#'),
                "collapsible": len(menu_item.get('children', [])) > 0,
                "expanded": True,
                "items": []
            }

            # 添加子菜单项
            for child in menu_item.get('children', []):
                category['items'].append({
                    "id": child['id'],
                    "text": child['text'],
                    "icon": child.get('icon', 'ri-home-line'),
                    "url": child.get('url', '#'),
                    "active": False
                })

            config['sidebar']['categories'].append(category)

        return jsonify(config)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# API路由 - 保存模板配置
@app.route('/api/template_config/<template_name>', methods=['POST'])
@login_required
def save_template_config(template_name):
    try:
        # 只允许野火模板
        if template_name != 'yehuo':
            return jsonify({"error": "只支持野火IM模板"}), 400

        # 获取请求中的JSON数据
        config_data = request.json
        if not config_data:
            return jsonify({"error": "No configuration data provided"}), 400

        # 验证JSON数据
        required_fields = ['template_id', 'template_name', 'version', 'global_settings', 'header', 'sidebar', 'main_content']
        for field in required_fields:
            if field not in config_data:
                return jsonify({"error": f"Missing required field: {field}"}), 400

        # 保存JSON数据到文件
        config_path = os.path.join(app.static_folder, 'config', f'{template_name}_template.json')
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        return jsonify({"success": True, "message": f"Template configuration for {template_name} saved successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# 路由
@app.route('/')
def index():
    try:
        # 检查是否是预览模式
        preview_template_id = request.args.get('preview')

        # 检查用户是否已登录（使用session）
        if not session.get('template_logged_in') and not preview_template_id:
            # 从数据库获取当前激活的模板
            active_template = TemplateConfig.query.filter_by(is_active=True).first()

            if active_template:
                # 根据模板名称重定向到相应的登录页面
                template_name = active_template.template_name.replace('.html', '')
                return redirect(url_for('template_login', template_name=template_name))
            else:
                # 如果没有激活的模板，默认使用野火IM登录
                return redirect(url_for('template_login', template_name='yehuo'))

        # 如果是预览模式，直接显示指定的模板
        if preview_template_id and current_user.is_authenticated:
            preview_template = db.session.get(TemplateConfig, preview_template_id)
            if preview_template:
                template_path = os.path.join(app.template_folder, preview_template.template_name)
                if os.path.exists(template_path):
                    # 准备模板上下文
                    template_context = {
                        'is_preview': True,
                        'preview_template_name': preview_template.template_name
                    }

                    # 获取模板自定义配置
                    customization = TemplateCustomization.query.filter_by(template_id=preview_template.id).first()
                    if customization:
                        template_context['primary_color'] = customization.primary_color
                        template_context['layout_style'] = customization.layout_style
                        template_context['show_shop_overview'] = customization.show_shop_overview
                        template_context['show_balance'] = customization.show_balance
                        template_context['show_modules'] = customization.show_modules
                        template_context['show_statistics'] = customization.show_statistics
                        template_context['custom_css'] = customization.custom_css

                    # 获取模板内容配置
                    template_contents = TemplateContent.query.filter_by(template_id=preview_template.id).all()
                    if template_contents:
                        # 将内容配置转换为字典，方便在模板中使用
                        contents = {}
                        for content in template_contents:
                            contents[content.content_key] = content.content_value
                        template_context['contents'] = contents

                    # 检查是否存在JSON配置文件
                    template_name = preview_template.template_name.replace('.html', '')
                    config_path = os.path.join(app.static_folder, 'config', f'{template_name}_template.json')
                    if os.path.exists(config_path):
                        # 如果存在JSON配置文件，添加标记到模板上下文
                        template_context['has_json_config'] = True

                    return render_template(preview_template.template_name, **template_context)

        # 用户已登录，显示相应模板
        active_template = TemplateConfig.query.filter_by(is_active=True).first()

        if active_template:
            # 检查模板文件是否存在
            template_path = os.path.join(app.template_folder, active_template.template_name)
            if os.path.exists(template_path):
                # 获取模板自定义配置
                customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()

                # 准备模板上下文
                template_context = {}

                if customization:
                    # 添加自定义配置到模板上下文
                    template_context['primary_color'] = customization.primary_color
                    template_context['layout_style'] = customization.layout_style
                    template_context['show_shop_overview'] = customization.show_shop_overview
                    template_context['show_balance'] = customization.show_balance
                    template_context['show_modules'] = customization.show_modules
                    template_context['show_statistics'] = customization.show_statistics
                    template_context['custom_css'] = customization.custom_css

                # 获取模板内容配置
                template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
                if template_contents:
                    # 将内容配置转换为字典，方便在模板中使用
                    contents = {}
                    for content in template_contents:
                        contents[content.content_key] = content.content_value
                    template_context['contents'] = contents

                # 获取示例数据
                try:
                    # 获取用户示例数据
                    user_samples = UserSampleData.query.filter_by(is_active=True).limit(10).all()
                    template_context['user_samples'] = user_samples

                    # 获取系统设置
                    system_settings = SystemSettings.query.filter_by(is_active=True).all()
                    settings_dict = {}
                    for setting in system_settings:
                        if setting.setting_category not in settings_dict:
                            settings_dict[setting.setting_category] = {}
                        settings_dict[setting.setting_category][setting.setting_key] = setting.setting_value
                    template_context['system_settings'] = settings_dict

                    # 获取菜单配置
                    sidebar_menus = MenuConfiguration.query.filter_by(
                        menu_type='sidebar',
                        is_active=True,
                        parent_id=None
                    ).order_by(MenuConfiguration.sort_order).all()

                    menu_items = []
                    for menu in sidebar_menus:
                        menu_item = {
                            'id': menu.menu_key,
                            'text': menu.menu_text,
                            'url': menu.menu_url,
                            'icon': menu.menu_icon,
                            'is_collapsible': menu.is_collapsible,
                            'is_expanded': menu.is_expanded,
                            'children': []
                        }

                        # 获取子菜单
                        if menu.is_collapsible:
                            sub_menus = MenuConfiguration.query.filter_by(
                                parent_id=menu.id,
                                is_active=True
                            ).order_by(MenuConfiguration.sort_order).all()

                            for sub_menu in sub_menus:
                                menu_item['children'].append({
                                    'id': sub_menu.menu_key,
                                    'text': sub_menu.menu_text,
                                    'url': sub_menu.menu_url
                                })

                        menu_items.append(menu_item)

                    template_context['menu_items'] = menu_items

                except Exception as e:
                    print(f"Error loading sample data: {str(e)}")

                # 设置当前页面
                template_context['current_page'] = 'home'

                # 渲染首页仪表板
                return render_template('dashboard.html', **template_context)
            else:
                print(f"警告: 模板文件 {active_template.template_name} 不存在，使用默认模板")
                # 如果模板文件不存在，更新数据库并使用JJ竞技世界模板
                active_template.is_active = False

                # 查找或创建JJ竞技世界模板
                default_template = TemplateConfig.query.filter_by(template_name='yehuo.html').first()
                if not default_template:
                    # 创建JJ竞技世界模板
                    default_template = TemplateConfig(template_name='yehuo.html', is_active=True)
                    db.session.add(default_template)
                    db.session.flush()  # 获取ID

                    # 添加默认凭证和配置
                    credential = TemplateCredential(
                        template_id=default_template.id,
                        username='yehuo_user',
                        password='123456',
                        description='JJ竞技世界模板默认登录凭证'
                    )
                    db.session.add(credential)

                    customization = TemplateCustomization(
                        template_id=default_template.id,
                        primary_color="#1e293b",
                        layout_style="default"
                    )
                    db.session.add(customization)
                else:
                    default_template.is_active = True

                db.session.commit()
                return render_template('yehuo.html')
        else:
            # 如果没有激活的模板，默认使用yehuo.html
            return render_template('yehuo.html')
    except Exception as e:
        print(f"错误: {str(e)}")
        try:
            # 出现任何错误，尝试使用JJ竞技世界模板
            # 检查JJ竞技世界模板是否存在
            yehuo_template = TemplateConfig.query.filter_by(template_name='yehuo.html').first()
            if not yehuo_template:
                # 如果不存在，创建一个新的JJ竞技世界模板
                yehuo_template = TemplateConfig(template_name='yehuo.html', is_active=True)
                db.session.add(yehuo_template)
                db.session.commit()
            return render_template('yehuo.html')
        except Exception as inner_e:
            print(f"严重错误: {str(inner_e)}")
            # 如果还是出错，返回一个简单的错误页面
            return "系统错误，请联系管理员", 500

@app.route('/login/<template_name>', methods=['GET', 'POST'])
def template_login(template_name):
    # 只允许JJ竞技世界模板
    if template_name != 'yehuo':
        template_name = 'yehuo'  # 强制使用JJ竞技世界模板

    # 获取模板配置
    template = TemplateConfig.query.filter(TemplateConfig.template_name.like(f'{template_name}.html')).first()

    if not template:
        # 如果找不到模板，创建一个新的JJ竞技世界模板及其关联数据
        template = TemplateConfig(template_name='yehuo.html', is_active=True)
        db.session.add(template)
        db.session.flush()  # 获取ID

        # 添加默认凭证
        credential = TemplateCredential(
            template_id=template.id,
            username='yehuo_user',
            password='123456',
            description='JJ竞技世界模板默认登录凭证'
        )
        db.session.add(credential)

        # 添加默认自定义配置
        customization = TemplateCustomization(
            template_id=template.id,
            primary_color="#1e293b",
            layout_style="default"
        )
        db.session.add(customization)

        # 添加默认内容配置
        default_contents = [
            # 顶部导航
            TemplateContent(template_id=template.id, content_type='title', content_key='header_title',
                           content_value='JJ竞技世界管理系统', content_description='顶部导航栏标题'),
            TemplateContent(template_id=template.id, content_type='text', content_key='language_text',
                           content_value='语言', content_description='语言选择文本'),
            TemplateContent(template_id=template.id, content_type='text', content_key='username',
                           content_value='admin', content_description='用户名显示'),

            # 面包屑导航
            TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_parent',
                           content_value='用户管理', content_description='面包屑导航父级'),
            TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_current',
                           content_value='用户列表', content_description='面包屑导航当前页'),

            # 搜索框
            TemplateContent(template_id=template.id, content_type='placeholder', content_key='search_placeholder',
                           content_value='搜索用户', content_description='搜索框占位文字'),

            # 按钮文本
            TemplateContent(template_id=template.id, content_type='button', content_key='add_button',
                           content_value='添加用户', content_description='添加按钮文字'),
            TemplateContent(template_id=template.id, content_type='button', content_key='more_button',
                           content_value='更多', content_description='更多按钮文字'),

            # 表格标题
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_1',
                           content_value='序号', content_description='表格标题1'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_2',
                           content_value='用户ID', content_description='表格标题2'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_3',
                           content_value='用户名', content_description='表格标题3'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_4',
                           content_value='手机号', content_description='表格标题4'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_5',
                           content_value='用户ID', content_description='表格标题5'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_6',
                           content_value='头像', content_description='表格标题6'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_7',
                           content_value='创建时间', content_description='表格标题7'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_8',
                           content_value='操作', content_description='表格标题8'),

            # 分页信息
            TemplateContent(template_id=template.id, content_type='text', content_key='total_records',
                           content_value='共 25371 条', content_description='总记录数'),
        ]
        db.session.add_all(default_contents)

        db.session.commit()
        flash('已创建JJ竞技世界模板')
        return redirect(url_for('template_login', template_name='yehuo'))

    # 获取模板凭证
    credential = TemplateCredential.query.filter_by(template_id=template.id).first()

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if credential and username == credential.username and password == credential.password:
            # 登录成功，设置session
            session['template_logged_in'] = True
            session['template_name'] = template_name
            return redirect(url_for('index'))
        else:
            flash('用户名或密码错误')

    # 根据模板名称返回相应的登录页面
    return render_template(f'login/{template_name}_login.html')

# 用户列表页面
@app.route('/user_list')
def user_list():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    try:
        # 获取活动模板
        active_template = TemplateConfig.query.filter_by(is_active=True).first()
        template_context = {}

        if active_template:
            # 获取模板内容配置
            template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
            if template_contents:
                contents = {}
                for content in template_contents:
                    contents[content.content_key] = content.content_value
                template_context['contents'] = contents

            # 获取示例数据
            try:
                # 获取用户示例数据
                user_samples = UserSampleData.query.filter_by(is_active=True).limit(10).all()
                template_context['user_samples'] = user_samples

                # 获取系统设置
                system_settings = SystemSettings.query.filter_by(is_active=True).all()
                settings_dict = {}
                for setting in system_settings:
                    if setting.setting_category not in settings_dict:
                        settings_dict[setting.setting_category] = {}
                    settings_dict[setting.setting_category][setting.setting_key] = setting.setting_value
                template_context['system_settings'] = settings_dict

                # 加载菜单配置
                template_context['menu_items'] = load_menu_configuration()
                template_context['current_page'] = 'user_list'

            except Exception as e:
                print(f"Error loading sample data: {str(e)}")

            return render_template('yehuo.html', **template_context)
        else:
            return "No active template found", 404

    except Exception as e:
        print(f"Error in user_list route: {str(e)}")
        return "Internal Server Error", 500

# 模板登出
@app.route('/logout')
def template_logout():
    # 清除session
    session.pop('template_logged_in', None)
    session.pop('template_name', None)
    return redirect(url_for('index'))

# 店铺概况页面
@app.route('/shop_overview')
def shop_overview():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    template_context = {
        'menu_items': load_menu_configuration(),
        'current_page': 'shop_overview'
    }
    return render_template('shop_overview.html', **template_context)

# 余额页面
@app.route('/balance')
def balance():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    template_context = {
        'menu_items': load_menu_configuration(),
        'current_page': 'balance'
    }
    return render_template('balance.html', **template_context)

# 模块页面
@app.route('/modules')
def modules():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    template_context = {
        'menu_items': load_menu_configuration(),
        'current_page': 'modules'
    }
    return render_template('modules.html', **template_context)

# 统计页面
@app.route('/statistics')
def statistics():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    template_context = {
        'menu_items': load_menu_configuration(),
        'current_page': 'statistics'
    }
    return render_template('statistics.html', **template_context)

# 订单管理页面
@app.route('/order_management')
def order_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为订单管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'order_management'

    return render_template('order_management.html', **template_context)

# 商品管理页面
@app.route('/product_management')
def product_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为商品管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'product_management'

    return render_template('product_management.html', **template_context)

# 数据分析页面
@app.route('/data_analysis')
def data_analysis():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    # 获取默认数据（今日数据）
    try:
        analytics_data = AnalyticsData.query.filter_by(time_range='today').first()
        if analytics_data:
            # 将数据添加到模板上下文
            template_context['analytics_data'] = {
                'transaction_amount': analytics_data.transaction_amount,
                'order_count': analytics_data.order_count,
                'visitor_count': analytics_data.visitor_count,
                'conversion_rate': analytics_data.conversion_rate
            }

            # 获取交易趋势数据
            transaction_trends = TransactionTrend.query.filter_by(analytics_id=analytics_data.id).order_by(TransactionTrend.hour).all()
            template_context['trend_hours'] = [trend.hour for trend in transaction_trends]
            template_context['trend_amounts'] = [trend.amount for trend in transaction_trends]
            template_context['trend_orders'] = [trend.order_count for trend in transaction_trends]

            # 获取用户来源数据
            user_sources = UserSource.query.filter_by(analytics_id=analytics_data.id).all()
            template_context['user_sources'] = [
                {
                    'name': source.source_name,
                    'visitor_count': source.visitor_count,
                    'conversion_rate': source.conversion_rate
                } for source in user_sources
            ]

            # 获取商品销售数据
            product_sales = ProductSales.query.filter_by(analytics_id=analytics_data.id).order_by(ProductSales.rank).all()
            template_context['product_sales'] = [
                {
                    'rank': product.rank,
                    'name': product.product_name,
                    'image': product.product_image,
                    'amount': product.sales_amount,
                    'count': product.sales_count,
                    'conversion_rate': product.conversion_rate
                } for product in product_sales
            ]
    except Exception as e:
        print(f"Error loading analytics data: {str(e)}")

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为数据中心，用于在侧边栏中高亮显示
    template_context['current_page'] = 'data_analysis'

    return render_template('data_analysis.html', **template_context)

# 营销活动管理页面
@app.route('/marketing_management')
def marketing_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为营销中心，用于在侧边栏中高亮显示
    template_context['current_page'] = 'marketing_management'

    return render_template('marketing_management.html', **template_context)

# 客服管理页面
@app.route('/customer_service')
def customer_service():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为客服管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'customer_service'

    return render_template('customer_service.html', **template_context)

# 资金管理页面
@app.route('/financial_management')
def financial_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为资金管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'financial_management'

    return render_template('financial_management.html', **template_context)

# 对账中心页面
@app.route('/reconciliation_center')
def reconciliation_center():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['show_shop_overview'] = customization.show_shop_overview
            template_context['show_balance'] = customization.show_balance
            template_context['show_modules'] = customization.show_modules
            template_context['show_statistics'] = customization.show_statistics
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

        # 加载菜单配置
        template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为对账中心，用于在侧边栏中高亮显示
    template_context['current_page'] = 'reconciliation_center'

    return render_template('reconciliation_center.html', **template_context)

# 管理员登录
@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.password == password:
            login_user(user)
            return redirect(url_for('admin_dashboard'))
        else:
            flash('用户名或密码错误')

    return render_template('admin/login.html')

# 管理员退出
@app.route('/admin/logout')
@login_required
def admin_logout():
    logout_user()
    return redirect(url_for('admin_login'))

# JSON配置管理页面
@app.route('/admin/json_config')
@login_required
def admin_json_config():
    templates = TemplateConfig.query.all()
    return render_template('admin/json_config.html', templates=templates)

# 用户友好的配置界面
@app.route('/admin/friendly_config')
@login_required
def admin_friendly_config():
    templates = TemplateConfig.query.all()

    # 获取URL参数中的template_id
    template_id = request.args.get('template_id')
    selected_template = None

    if template_id:
        try:
            # 尝试将template_id转换为整数
            template_id = int(template_id)
            # 查找对应的模板
            selected_template = TemplateConfig.query.get(template_id)
        except (ValueError, TypeError):
            # 如果转换失败或找不到模板，忽略错误
            pass

    # 如果没有指定模板或找不到指定的模板，使用第一个模板
    if not selected_template and templates:
        selected_template = templates[0]

    return render_template('admin/friendly_config.html', templates=templates, selected_template=selected_template)

# 封禁用户列表页面
@app.route('/banned_users')
def banned_users():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为封禁用户列表，用于在侧边栏中高亮显示
    template_context['current_page'] = 'banned_users'

    return render_template('banned_users.html', **template_context)

# 敏感词列表页面
@app.route('/sensitive_words')
def sensitive_words():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为敏感词列表，用于在侧边栏中高亮显示
    template_context['current_page'] = 'sensitive_words'

    return render_template('sensitive_words.html', **template_context)

# 敏感词命中页面
@app.route('/sensitive_word_hits')
def sensitive_word_hits():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为敏感词命中，用于在侧边栏中高亮显示
    template_context['current_page'] = 'sensitive_word_hits'

    return render_template('sensitive_word_hits.html', **template_context)

# 敏感词API - 获取所有敏感词
@app.route('/api/sensitive_words', methods=['GET'])
def get_sensitive_words():
    try:
        words = SensitiveWord.query.order_by(SensitiveWord.created_at.desc()).all()
        words_data = []
        for word in words:
            words_data.append({
                'id': word.id,
                'word': word.word,
                'category': word.category,
                'severity': word.severity,
                'action': word.action,
                'description': word.description,
                'is_active': word.is_active,
                'created_at': word.created_at.strftime('%Y-%m-%d %H:%M')
            })
        return jsonify(words_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 敏感词API - 添加敏感词
@app.route('/api/sensitive_words', methods=['POST'])
def add_sensitive_word():
    try:
        data = request.get_json()

        # 检查敏感词是否已存在
        existing_word = SensitiveWord.query.filter_by(word=data['word']).first()
        if existing_word:
            return jsonify({'error': '该敏感词已存在'}), 400

        # 创建新的敏感词
        new_word = SensitiveWord(
            word=data['word'],
            category=data.get('category', '默认'),
            severity=data.get('severity', '中等'),
            action=data.get('action', '过滤'),
            description=data.get('description', ''),
            is_active=True
        )

        db.session.add(new_word)
        db.session.commit()

        return jsonify({'message': '敏感词添加成功', 'id': new_word.id}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 敏感词API - 更新敏感词
@app.route('/api/sensitive_words/<int:word_id>', methods=['PUT'])
def update_sensitive_word(word_id):
    try:
        word = SensitiveWord.query.get_or_404(word_id)
        data = request.get_json()

        # 检查是否有其他敏感词使用相同的词汇
        existing_word = SensitiveWord.query.filter(
            SensitiveWord.word == data['word'],
            SensitiveWord.id != word_id
        ).first()
        if existing_word:
            return jsonify({'error': '该敏感词已存在'}), 400

        # 更新敏感词
        word.word = data['word']
        word.category = data.get('category', word.category)
        word.severity = data.get('severity', word.severity)
        word.action = data.get('action', word.action)
        word.description = data.get('description', word.description)
        word.updated_at = datetime.now(timezone.utc)

        db.session.commit()

        return jsonify({'message': '敏感词更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 敏感词API - 删除敏感词
@app.route('/api/sensitive_words/<int:word_id>', methods=['DELETE'])
def delete_sensitive_word(word_id):
    try:
        word = SensitiveWord.query.get_or_404(word_id)
        db.session.delete(word)
        db.session.commit()

        return jsonify({'message': '敏感词删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 敏感词API - 批量删除敏感词
@app.route('/api/sensitive_words/batch_delete', methods=['DELETE'])
def batch_delete_sensitive_words():
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return jsonify({'error': '未提供要删除的敏感词ID'}), 400

        # 删除指定的敏感词
        deleted_count = SensitiveWord.query.filter(SensitiveWord.id.in_(ids)).delete(synchronize_session=False)
        db.session.commit()

        return jsonify({'message': f'成功删除 {deleted_count} 个敏感词'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 敏感词命中API - 获取所有命中记录
@app.route('/api/sensitive_word_hits', methods=['GET'])
def get_sensitive_word_hits():
    try:
        hits = SensitiveWordHit.query.order_by(SensitiveWordHit.created_at.desc()).all()
        hits_data = []
        for hit in hits:
            hits_data.append({
                'id': hit.id,
                'user_id': hit.user_id,
                'username': hit.username,
                'hit_word': hit.hit_word,
                'original_content': hit.original_content,
                'filtered_content': hit.filtered_content,
                'hit_context': hit.hit_context,
                'action_taken': hit.action_taken,
                'source_type': hit.source_type,
                'source_id': hit.source_id,
                'ip_address': hit.ip_address,
                'user_agent': hit.user_agent,
                'is_processed': hit.is_processed,
                'created_at': hit.created_at.strftime('%Y-%m-%d %H:%M'),
                'processed_at': hit.processed_at.strftime('%Y-%m-%d %H:%M') if hit.processed_at else None
            })
        return jsonify(hits_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 敏感词命中API - 标记单个记录为已处理
@app.route('/api/sensitive_word_hits/<int:hit_id>/process', methods=['PUT'])
def process_sensitive_word_hit(hit_id):
    try:
        hit = SensitiveWordHit.query.get_or_404(hit_id)
        hit.is_processed = True
        hit.processed_at = datetime.now(timezone.utc)

        db.session.commit()

        return jsonify({'message': '标记为已处理成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 敏感词命中API - 批量处理记录
@app.route('/api/sensitive_word_hits/batch_process', methods=['PUT'])
def batch_process_sensitive_word_hits():
    try:
        data = request.get_json()
        ids = data.get('ids', [])

        if not ids:
            return jsonify({'error': '未提供要处理的记录ID'}), 400

        # 更新指定的记录
        updated_count = SensitiveWordHit.query.filter(SensitiveWordHit.id.in_(ids)).update(
            {
                'is_processed': True,
                'processed_at': datetime.now(timezone.utc)
            },
            synchronize_session=False
        )
        db.session.commit()

        return jsonify({'message': f'成功处理 {updated_count} 条记录'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 群组管理页面
@app.route('/group_management')
def group_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为群组管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'group_management'

    return render_template('group_management.html', **template_context)

# 消息管理页面
@app.route('/message_management')
def message_management():
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为消息管理，用于在侧边栏中高亮显示
    template_context['current_page'] = 'message_management'

    return render_template('message_management.html', **template_context)

# 用户消息页面
@app.route('/user_messages/<user_id>')
def user_messages(user_id):
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return redirect(url_for('index'))

    # 获取活动模板的内容配置
    active_template = TemplateConfig.query.filter_by(is_active=True).first()
    template_context = {}

    if active_template:
        # 获取模板自定义配置
        customization = TemplateCustomization.query.filter_by(template_id=active_template.id).first()
        if customization:
            template_context['primary_color'] = customization.primary_color
            template_context['layout_style'] = customization.layout_style
            template_context['custom_css'] = customization.custom_css

        # 获取模板内容配置
        template_contents = TemplateContent.query.filter_by(template_id=active_template.id).all()
        if template_contents:
            # 将内容配置转换为字典，方便在模板中使用
            contents = {}
            for content in template_contents:
                contents[content.content_key] = content.content_value
            template_context['contents'] = contents

    # 加载菜单配置
    template_context['menu_items'] = load_menu_configuration()

    # 设置当前页面为用户消息，用于在侧边栏中高亮显示
    template_context['current_page'] = 'user_messages'
    template_context['current_user_id'] = user_id

    return render_template('user_messages.html', **template_context)

# 管理员仪表盘
@app.route('/admin/dashboard')
@login_required
def admin_dashboard():
    # 获取统计数据
    stats = {
        'total_templates': TemplateConfig.query.count(),
        'active_template': TemplateConfig.query.filter_by(is_active=True).first(),
        'total_credentials': TemplateCredential.query.count(),
        'total_customizations': TemplateCustomization.query.count(),
        'total_visits': random.randint(1000, 5000),  # 模拟数据
        'total_users': random.randint(100, 500),     # 模拟数据
        'conversion_rate': round(random.uniform(10, 30), 2),  # 模拟数据
        'avg_time': round(random.uniform(5, 15), 2),  # 模拟数据
    }

    # 获取最近的活动日志（这里使用模拟数据）
    recent_activities = [
        {
            'action': '模板切换',
            'description': '将活跃模板切换为支付宝商家后台',
            'user': 'admin',
            'timestamp': datetime.now(timezone.utc) - timedelta(hours=2)
        },
        {
            'action': '凭证更新',
            'description': '更新了TikTok模板的登录凭证',
            'user': 'admin',
            'timestamp': datetime.now(timezone.utc) - timedelta(hours=5)
        },
        {
            'action': '模板自定义',
            'description': '修改了支付宝模板的主题色和布局',
            'user': 'admin',
            'timestamp': datetime.now(timezone.utc) - timedelta(days=1)
        },
        {
            'action': '用户登录',
            'description': '管理员登录系统',
            'user': 'admin',
            'timestamp': datetime.now(timezone.utc) - timedelta(days=1, hours=6)
        },
    ]

    return render_template('admin/dashboard.html', stats=stats, recent_activities=recent_activities)

# 模板凭证管理页面
@app.route('/admin/credentials', methods=['GET', 'POST'])
@login_required
def admin_credentials():
    try:
        # 获取所有模板及其凭证
        templates = TemplateConfig.query.all()
        credentials = {}

        for template in templates:
            credential = TemplateCredential.query.filter_by(template_id=template.id).first()
            if credential:
                credentials[template.id] = credential

        if request.method == 'POST':
            try:
                template_id = request.form.get('template_id')
                username = request.form.get('username')
                password = request.form.get('password')
                description = request.form.get('description')

                if not template_id or not username or not password:
                    flash('请填写所有必填字段')
                    return redirect(url_for('admin_credentials'))

                # 检查模板是否存在
                template = db.session.get(TemplateConfig, template_id)
                if not template:
                    flash('模板不存在')
                    return redirect(url_for('admin_credentials'))

                # 检查该模板是否已有凭证
                credential = TemplateCredential.query.filter_by(template_id=template.id).first()

                if credential:
                    # 更新现有凭证
                    credential.username = username
                    credential.password = password
                    credential.description = description
                    credential.updated_at = datetime.now(timezone.utc)
                else:
                    # 创建新凭证
                    credential = TemplateCredential(
                        template_id=template.id,
                        username=username,
                        password=password,
                        description=description
                    )
                    db.session.add(credential)

                db.session.commit()
                flash('凭证设置已更新')
                return redirect(url_for('admin_credentials'))
            except Exception as e:
                db.session.rollback()
                flash(f'错误: {str(e)}')
                return redirect(url_for('admin_credentials'))

        return render_template('admin/credential_config.html', templates=templates, credentials=credentials)
    except Exception as e:
        flash(f'系统错误: {str(e)}')
        return redirect(url_for('admin_dashboard'))

# 模板配置页面
@app.route('/admin/templates', methods=['GET', 'POST'])
@login_required
def admin_templates():
    try:
        templates = TemplateConfig.query.all()

        # 检查模板文件是否存在，如果不存在则从数据库中删除
        templates_to_remove = []
        for template in templates:
            template_path = os.path.join(app.template_folder, template.template_name)
            if not os.path.exists(template_path):
                if template.is_active:
                    # 如果当前激活的模板文件不存在，将其设为非激活
                    template.is_active = False
                    # 将alipay.html设为激活状态
                    default_template = TemplateConfig.query.filter_by(template_name='alipay.html').first()
                    if default_template:
                        default_template.is_active = True
                templates_to_remove.append(template)

        # 从数据库中删除不存在的模板
        for template in templates_to_remove:
            db.session.delete(template)

        # 如果没有模板，添加默认模板
        if not templates or all(template in templates_to_remove for template in templates):
            default_template = TemplateConfig(template_name='alipay.html', is_active=True)
            db.session.add(default_template)

        db.session.commit()

        # 重新查询模板列表
        templates = TemplateConfig.query.all()

        if request.method == 'POST':
            try:
                template_id = request.form.get('template_id')

                # 将所有模板设置为非激活
                TemplateConfig.query.update({TemplateConfig.is_active: False})

                # 将选中的模板设置为激活
                template = db.session.get(TemplateConfig, template_id)
                if template:
                    # 检查模板文件是否存在
                    template_path = os.path.join(app.template_folder, template.template_name)
                    if os.path.exists(template_path):
                        template.is_active = True
                        template.updated_at = datetime.now(timezone.utc)
                        db.session.commit()
                        flash('模板设置已更新')
                    else:
                        flash(f'错误: 模板文件 {template.template_name} 不存在')
                else:
                    flash('错误: 无效的模板ID')

                return redirect(url_for('admin_templates'))
            except Exception as e:
                db.session.rollback()
                flash(f'错误: {str(e)}')
                return redirect(url_for('admin_templates'))

        return render_template('admin/template_config.html', templates=templates)
    except Exception as e:
        flash(f'系统错误: {str(e)}')
        return redirect(url_for('admin_dashboard'))

# 模板内容管理页面
@app.route('/admin/content', methods=['GET', 'POST'])
@login_required
def admin_content():
    try:
        templates = TemplateConfig.query.all()
        contents = {}

        # 检查是否需要重新创建内容
        recreate_content = request.args.get('recreate') == '1'

        if recreate_content:
            # 删除所有现有内容
            TemplateContent.query.delete()
            db.session.commit()
            flash('所有模板内容已重置，正在重新创建默认内容...')

            # 为野火模板重新创建默认内容
            for template in templates:
                # 只处理野火模板
                if 'yehuo' in template.template_name:
                    default_contents = [
                        # 顶部导航
                        TemplateContent(template_id=template.id, content_type='title', content_key='header_title',
                                       content_value='JJ竞技世界管理系统', content_description='顶部导航栏标题'),
                        TemplateContent(template_id=template.id, content_type='text', content_key='language_text',
                                       content_value='语言', content_description='语言选择文本'),
                        TemplateContent(template_id=template.id, content_type='text', content_key='username',
                                       content_value='admin', content_description='用户名显示'),

                        # 面包屑导航
                        TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_parent',
                                       content_value='用户管理', content_description='面包屑导航父级'),
                        TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_current',
                                       content_value='用户列表', content_description='面包屑导航当前页'),

                        # 搜索框
                        TemplateContent(template_id=template.id, content_type='placeholder', content_key='search_placeholder',
                                       content_value='搜索用户', content_description='搜索框占位文字'),

                        # 按钮文本
                        TemplateContent(template_id=template.id, content_type='button', content_key='add_button',
                                       content_value='添加用户', content_description='添加按钮文字'),
                        TemplateContent(template_id=template.id, content_type='button', content_key='more_button',
                                       content_value='更多', content_description='更多按钮文字'),

                        # 表格标题
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_1',
                                       content_value='序号', content_description='表格标题1'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_2',
                                       content_value='用户ID', content_description='表格标题2'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_3',
                                       content_value='用户名', content_description='表格标题3'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_4',
                                       content_value='手机号', content_description='表格标题4'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_5',
                                       content_value='用户ID', content_description='表格标题5'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_6',
                                       content_value='头像', content_description='表格标题6'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_7',
                                       content_value='创建时间', content_description='表格标题7'),
                        TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_8',
                                       content_value='操作', content_description='表格标题8'),

                        # 分页信息
                        TemplateContent(template_id=template.id, content_type='text', content_key='total_records',
                                       content_value='共 25371 条', content_description='总记录数'),
                    ]
                    db.session.add_all(default_contents)

            db.session.commit()
            return redirect(url_for('admin_content'))

        # 获取每个模板的内容配置
        for template in templates:
            template_contents = TemplateContent.query.filter_by(template_id=template.id).all()
            contents[template.id] = template_contents

        if request.method == 'POST':
            action = request.form.get('action')

            if action == 'update_content':
                try:
                    template_id = request.form.get('template_id')
                    content_section = request.form.get('content_section')

                    # 检查模板是否存在
                    template = db.session.get(TemplateConfig, template_id)
                    if not template:
                        flash('错误: 无效的模板ID')
                        return redirect(url_for('admin_content'))

                    # 更新内容
                    for key, value in request.form.items():
                        if key.startswith('content_'):
                            content_id = key.replace('content_', '')
                            content = db.session.get(TemplateContent, content_id)
                            if content and content.template_id == int(template_id):
                                content.content_value = value
                                content.updated_at = datetime.now(timezone.utc)

                    db.session.commit()
                    flash(f'模板内容已更新 ({content_section})')
                    return redirect(url_for('admin_content'))
                except Exception as e:
                    db.session.rollback()
                    flash(f'错误: {str(e)}')
                    return redirect(url_for('admin_content'))

        return render_template('admin/content_config.html', templates=templates, contents=contents)
    except Exception as e:
        flash(f'系统错误: {str(e)}')
        return redirect(url_for('admin_dashboard'))

# 增强版模板配置页面
@app.route('/admin/enhanced_templates', methods=['GET', 'POST'])
@login_required
def admin_enhanced_templates():
    try:
        templates = TemplateConfig.query.all()
        customizations = {}

        # 检查模板文件是否存在，如果不存在则从数据库中删除
        templates_to_remove = []
        for template in templates:
            template_path = os.path.join(app.template_folder, template.template_name)
            if not os.path.exists(template_path):
                if template.is_active:
                    # 如果当前激活的模板文件不存在，将其设为非激活
                    template.is_active = False
                    # 查找野火模板并设为激活状态
                    yehuo_template = TemplateConfig.query.filter(TemplateConfig.template_name.contains('yehuo')).first()
                    if yehuo_template:
                        yehuo_template.is_active = True
                templates_to_remove.append(template)

        # 从数据库中删除不存在的模板
        for template in templates_to_remove:
            db.session.delete(template)

        # 如果没有模板，添加默认野火模板
        if not templates or all(template in templates_to_remove for template in templates):
            # 查找野火模板文件
            yehuo_template_files = ['yehuo.html', 'index.html']
            for template_file in yehuo_template_files:
                template_path = os.path.join(app.template_folder, template_file)
                if os.path.exists(template_path):
                    default_template = TemplateConfig(template_name=template_file, is_active=True)
                    db.session.add(default_template)
                    break

        db.session.commit()

        # 重新查询模板列表
        templates = TemplateConfig.query.all()

        # 获取每个模板的自定义配置
        for template in templates:
            customization = TemplateCustomization.query.filter_by(template_id=template.id).first()
            if customization:
                customizations[template.id] = customization
            else:
                # 如果没有自定义配置，创建一个默认配置（野火模板）
                primary_color = "#1677FF"  # 野火模板默认颜色

                customization = TemplateCustomization(
                    template_id=template.id,
                    primary_color=primary_color,
                    layout_style="default"
                )
                db.session.add(customization)
                db.session.commit()
                customizations[template.id] = customization

        if request.method == 'POST':
            action = request.form.get('action', 'activate')

            if action == 'activate':
                try:
                    template_id = request.form.get('template_id')

                    # 将所有模板设置为非激活
                    TemplateConfig.query.update({TemplateConfig.is_active: False})

                    # 将选中的模板设置为激活
                    template = db.session.get(TemplateConfig, template_id)
                    if template:
                        # 检查模板文件是否存在
                        template_path = os.path.join(app.template_folder, template.template_name)
                        if os.path.exists(template_path):
                            template.is_active = True
                            template.updated_at = datetime.now(timezone.utc)
                            db.session.commit()
                            flash('模板设置已更新')
                        else:
                            flash(f'错误: 模板文件 {template.template_name} 不存在')
                    else:
                        flash('错误: 无效的模板ID')

                    return redirect(url_for('admin_enhanced_templates'))
                except Exception as e:
                    db.session.rollback()
                    flash(f'错误: {str(e)}')
                    return redirect(url_for('admin_enhanced_templates'))

            elif action == 'customize':
                try:
                    template_id = request.form.get('template_id')
                    primary_color = request.form.get('primary_color')
                    layout_style = request.form.get('layout_style')
                    show_shop_overview = 'show_shop_overview' in request.form
                    show_balance = 'show_balance' in request.form
                    show_modules = 'show_modules' in request.form
                    show_statistics = 'show_statistics' in request.form
                    custom_css = request.form.get('custom_css', '')

                    # 检查模板是否存在
                    template = db.session.get(TemplateConfig, template_id)
                    if not template:
                        flash('错误: 无效的模板ID')
                        return redirect(url_for('admin_enhanced_templates'))

                    # 获取或创建自定义配置
                    customization = TemplateCustomization.query.filter_by(template_id=template.id).first()
                    if not customization:
                        customization = TemplateCustomization(template_id=template.id)
                        db.session.add(customization)

                    # 更新自定义配置
                    customization.primary_color = primary_color
                    customization.layout_style = layout_style
                    customization.show_shop_overview = show_shop_overview
                    customization.show_balance = show_balance
                    customization.show_modules = show_modules
                    customization.show_statistics = show_statistics
                    customization.custom_css = custom_css
                    customization.updated_at = datetime.now(timezone.utc)

                    db.session.commit()
                    flash('模板自定义配置已更新')
                    return redirect(url_for('admin_enhanced_templates'))
                except Exception as e:
                    db.session.rollback()
                    flash(f'错误: {str(e)}')
                    return redirect(url_for('admin_enhanced_templates'))

        return render_template('admin/enhanced_template_config.html', templates=templates, customizations=customizations)
    except Exception as e:
        flash(f'系统错误: {str(e)}')
        return redirect(url_for('admin_dashboard'))

# 数据分析API
@app.route('/api/analytics/<time_range>')
def get_analytics(time_range):
    # 检查用户是否已登录
    if not session.get('template_logged_in'):
        return jsonify({'error': 'Unauthorized'}), 401

    # 验证时间范围参数
    valid_ranges = ['today', 'yesterday', 'week', 'month']
    if time_range not in valid_ranges:
        return jsonify({'error': 'Invalid time range'}), 400

    try:
        # 查询指定时间范围的分析数据
        analytics_data = AnalyticsData.query.filter_by(time_range=time_range).first()

        if not analytics_data:
            return jsonify({'error': 'Data not found'}), 404

        # 查询交易趋势数据
        transaction_trends = TransactionTrend.query.filter_by(analytics_id=analytics_data.id).order_by(TransactionTrend.hour).all()

        # 查询用户来源数据
        user_sources = UserSource.query.filter_by(analytics_id=analytics_data.id).all()

        # 查询商品销售数据
        product_sales = ProductSales.query.filter_by(analytics_id=analytics_data.id).order_by(ProductSales.rank).all()

        # 构建响应数据
        response = {
            'overview': {
                'transaction_amount': analytics_data.transaction_amount,
                'order_count': analytics_data.order_count,
                'visitor_count': analytics_data.visitor_count,
                'conversion_rate': analytics_data.conversion_rate
            },
            'transaction_trend': {
                'hours': [trend.hour for trend in transaction_trends],
                'amounts': [trend.amount for trend in transaction_trends],
                'orders': [trend.order_count for trend in transaction_trends]
            },
            'user_sources': [
                {
                    'name': source.source_name,
                    'visitor_count': source.visitor_count,
                    'conversion_rate': source.conversion_rate
                } for source in user_sources
            ],
            'product_sales': [
                {
                    'rank': product.rank,
                    'name': product.product_name,
                    'image': product.product_image,
                    'amount': product.sales_amount,
                    'count': product.sales_count,
                    'conversion_rate': product.conversion_rate
                } for product in product_sales
            ]
        }

        return jsonify(response)

    except Exception as e:
        print(f"Error fetching analytics data: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

# 示例数据管理API
@app.route('/api/sample_data/users', methods=['GET'])
def get_user_samples():
    """获取用户示例数据（支持分页）"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '', type=str)

        # 构建查询
        query = UserSampleData.query.filter_by(is_active=True)

        # 添加搜索条件
        if search:
            query = query.filter(
                db.or_(
                    UserSampleData.username.contains(search),
                    UserSampleData.user_id.contains(search),
                    UserSampleData.phone.contains(search),
                    UserSampleData.user_code.contains(search)
                )
            )

        # 执行分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        users_data = []
        for user in pagination.items:
            users_data.append({
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'phone': user.phone,
                'user_code': user.user_code,
                'avatar_url': user.avatar_url,
                'create_time': user.create_time,
                'status': user.status
            })

        return jsonify({
            'users': users_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/sample_data/users', methods=['POST'])
def add_user_sample():
    """添加用户示例数据"""
    try:
        data = request.get_json()

        new_user = UserSampleData(
            user_id=data['user_id'],
            username=data['username'],
            phone=data['phone'],
            user_code=data['user_code'],
            avatar_url=data.get('avatar_url', ''),
            create_time=data['create_time'],
            status=data.get('status', '正常')
        )

        db.session.add(new_user)
        db.session.commit()

        return jsonify({'message': '用户示例数据添加成功', 'id': new_user.id}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/sample_data/groups', methods=['GET'])
def get_group_samples():
    """获取群组示例数据，支持分页和搜索"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '', type=str)

        # 构建查询
        query = GroupSampleData.query.filter_by(is_active=True)

        # 如果有搜索条件，添加搜索过滤
        if search:
            search_filter = db.or_(
                GroupSampleData.group_name.contains(search),
                GroupSampleData.group_id.contains(search),
                GroupSampleData.owner.contains(search),
                GroupSampleData.group_type.contains(search)
            )
            query = query.filter(search_filter)

        # 执行分页查询
        pagination = query.order_by(GroupSampleData.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 构建返回数据
        groups_data = []
        for group in pagination.items:
            groups_data.append({
                'id': group.id,
                'group_id': group.group_id,
                'group_name': group.group_name,
                'avatar': group.avatar,
                'owner': group.owner,
                'member_count': group.member_count,
                'group_type': group.group_type,
                'create_time': group.create_time,
                'description': group.description
            })

        return jsonify({
            'groups': groups_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/sample_data/sensitive_words', methods=['GET'])
def get_sensitive_word_samples():
    """获取敏感词示例数据"""
    try:
        words = SensitiveWordSampleData.query.filter_by(is_active=True).all()
        words_data = []
        for word in words:
            words_data.append({
                'id': word.id,
                'word': word.word,
                'category': word.category,
                'severity': word.severity,
                'action': word.action,
                'description': word.description,
                'is_active': word.is_active,
                'created_at': word.created_at.strftime('%Y-%m-%d %H:%M') if word.created_at else ''
            })
        return jsonify(words_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/system_settings', methods=['GET'])
def get_system_settings():
    """获取系统设置"""
    try:
        settings = SystemSettings.query.filter_by(is_active=True).all()
        settings_dict = {}
        for setting in settings:
            if setting.setting_category not in settings_dict:
                settings_dict[setting.setting_category] = {}
            settings_dict[setting.setting_category][setting.setting_key] = setting.setting_value
        return jsonify(settings_dict)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/system_settings', methods=['PUT'])
def update_system_settings():
    """更新系统设置"""
    try:
        data = request.get_json()

        for category, settings in data.items():
            for key, value in settings.items():
                setting = SystemSettings.query.filter_by(
                    setting_category=category,
                    setting_key=key
                ).first()

                if setting:
                    setting.setting_value = value
                    setting.updated_at = datetime.now(timezone.utc)
                else:
                    # 创建新设置
                    new_setting = SystemSettings(
                        setting_category=category,
                        setting_key=key,
                        setting_value=value,
                        setting_description=f'{category}.{key} 设置'
                    )
                    db.session.add(new_setting)

        db.session.commit()
        return jsonify({'message': '系统设置更新成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# 首页仪表板API
@app.route('/api/dashboard/statistics', methods=['GET'])
def get_dashboard_statistics():
    """获取首页统计数据"""
    try:
        # 获取用户统计
        total_users = UserSampleData.query.filter_by(is_active=True).count()

        # 获取群组统计
        total_groups = GroupSampleData.query.filter_by(is_active=True).count()

        # 获取敏感词统计
        total_sensitive_words = SensitiveWordSampleData.query.filter_by(is_active=True).count()

        # 获取消息统计
        total_messages = MessageSampleData.query.filter_by(is_active=True).count()

        # 模拟增长数据（实际项目中应该从真实数据计算）
        statistics = {
            'totalUsers': total_users,
            'userGrowth': 12.5,  # 模拟增长率
            'activeUsers': int(total_users * 0.3),  # 模拟活跃用户为总用户的30%
            'activeGrowth': 8.3,
            'totalGroups': total_groups,
            'groupGrowth': 5.2,
            'todayMessages': total_messages * 10,  # 模拟今日消息数
            'messageGrowth': -3.1
        }

        return jsonify(statistics)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/activities', methods=['GET'])
def get_dashboard_activities():
    """获取首页最新动态"""
    try:
        activities = []

        # 获取最新用户（模拟）
        recent_users = UserSampleData.query.filter_by(is_active=True).order_by(UserSampleData.created_at.desc()).limit(2).all()
        for user in recent_users:
            activities.append({
                'type': 'user_register',
                'message': f'新用户 "{user.username}" 注册',
                'time': '刚刚',
                'icon': 'ri-user-add-line',
                'color': 'text-green-600'
            })

        # 获取最新敏感词命中（模拟）
        recent_words = SensitiveWordSampleData.query.filter_by(is_active=True).limit(2).all()
        for word in recent_words:
            activities.append({
                'type': 'sensitive_word',
                'message': f'检测到敏感词 "{word.word}" 命中',
                'time': '5分钟前',
                'icon': 'ri-spam-2-line',
                'color': 'text-red-600'
            })

        # 获取最新群组（模拟）
        recent_groups = GroupSampleData.query.filter_by(is_active=True).order_by(GroupSampleData.created_at.desc()).limit(1).all()
        for group in recent_groups:
            activities.append({
                'type': 'group_create',
                'message': f'创建新群组 "{group.group_name}"',
                'time': '10分钟前',
                'icon': 'ri-group-line',
                'color': 'text-blue-600'
            })

        # 限制返回最新5条
        return jsonify(activities[:5])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 封禁用户API
@app.route('/api/sample_data/banned_users', methods=['GET'])
def get_banned_users():
    """获取封禁用户列表，支持分页和搜索"""
    try:
        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '', type=str)

        # 构建查询
        query = BannedUserSampleData.query.filter_by(is_active=True)

        # 如果有搜索条件，添加搜索过滤
        if search:
            search_filter = db.or_(
                BannedUserSampleData.username.contains(search),
                BannedUserSampleData.user_id.contains(search),
                BannedUserSampleData.phone.contains(search),
                BannedUserSampleData.ban_reason.contains(search)
            )
            query = query.filter(search_filter)

        # 执行分页查询
        pagination = query.order_by(BannedUserSampleData.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 构建返回数据
        banned_users_data = []
        for user in pagination.items:
            banned_users_data.append({
                'id': user.id,
                'user_id': user.user_id,
                'username': user.username,
                'phone': user.phone,
                'ban_reason': user.ban_reason,
                'ban_time': user.ban_time,
                'ban_expire': user.ban_expire,
                'ban_type': user.ban_type,
                'ban_level': user.ban_level,
                'ban_operator': user.ban_operator
            })

        return jsonify({
            'banned_users': banned_users_data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/database_data/<table_name>', methods=['GET'])
def get_database_data(table_name):
    """获取数据库表格数据"""
    try:
        # 定义允许查询的表和对应的模型
        table_models = {
            'sensitive_words': SensitiveWord,
            'sensitive_word_hits': SensitiveWordHit,
            'user': User,
            'analytics_data': AnalyticsData,
            'template_config': TemplateConfig,
            'template_content': TemplateContent,
            'user_sample_data': UserSampleData,
            'group_sample_data': GroupSampleData,
            'message_sample_data': MessageSampleData,
            'sensitive_word_sample_data': SensitiveWordSampleData,
            'banned_user_sample_data': BannedUserSampleData,
            'menu_configuration': MenuConfiguration,
            'system_settings': SystemSettings
        }

        if table_name not in table_models:
            return jsonify({"error": "表格不存在"}), 404

        model = table_models[table_name]

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 查询数据
        query = model.query
        total = query.count()

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # 获取列名
        columns = [column.name for column in model.__table__.columns]

        # 转换数据为字典列表
        data = []
        for item in pagination.items:
            row = {}
            for column in columns:
                value = getattr(item, column)
                # 处理日期时间类型
                if isinstance(value, datetime):
                    row[column] = value.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    row[column] = value
            data.append(row)

        return jsonify({
            "table_name": table_name,
            "columns": columns,
            "data": data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": pagination.pages,
                "has_prev": pagination.has_prev,
                "has_next": pagination.has_next
            }
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/database_data/<table_name>/<int:row_id>', methods=['PUT'])
def update_database_data(table_name, row_id):
    """更新数据库表格数据"""
    try:
        # 定义允许查询的表和对应的模型
        table_models = {
            'sensitive_words': SensitiveWord,
            'sensitive_word_hits': SensitiveWordHit,
            'user': User,
            'analytics_data': AnalyticsData,
            'template_config': TemplateConfig,
            'template_content': TemplateContent,
            'user_sample_data': UserSampleData,
            'group_sample_data': GroupSampleData,
            'message_sample_data': MessageSampleData,
            'sensitive_word_sample_data': SensitiveWordSampleData,
            'banned_user_sample_data': BannedUserSampleData,
            'menu_configuration': MenuConfiguration,
            'system_settings': SystemSettings
        }

        if table_name not in table_models:
            return jsonify({"error": "表格不存在"}), 404

        model = table_models[table_name]

        # 查找要更新的记录
        record = model.query.get(row_id)
        if not record:
            return jsonify({"error": "记录不存在"}), 404

        # 获取更新数据
        update_data = request.get_json()
        if not update_data:
            return jsonify({"error": "没有提供更新数据"}), 400

        # 更新记录
        for key, value in update_data.items():
            if hasattr(record, key) and key != 'id':  # 不允许更新ID
                # 处理布尔值
                if isinstance(getattr(record, key), bool):
                    value = value.lower() in ('true', '1', 'yes', 'on') if isinstance(value, str) else bool(value)
                # 处理日期时间
                elif key in ['created_at', 'updated_at'] and isinstance(value, str):
                    try:
                        value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        continue  # 跳过无效的日期格式

                setattr(record, key, value)

        # 如果有updated_at字段，更新它
        if hasattr(record, 'updated_at'):
            record.updated_at = datetime.now(timezone.utc)

        db.session.commit()

        return jsonify({"message": "数据更新成功"})

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

@app.route('/api/database_data/<table_name>/<int:row_id>', methods=['DELETE'])
def delete_database_data(table_name, row_id):
    """删除数据库表格数据"""
    try:
        # 定义允许查询的表和对应的模型
        table_models = {
            'sensitive_words': SensitiveWord,
            'sensitive_word_hits': SensitiveWordHit,
            'user': User,
            'analytics_data': AnalyticsData,
            'template_config': TemplateConfig,
            'template_content': TemplateContent,
            'user_sample_data': UserSampleData,
            'group_sample_data': GroupSampleData,
            'message_sample_data': MessageSampleData,
            'sensitive_word_sample_data': SensitiveWordSampleData,
            'banned_user_sample_data': BannedUserSampleData,
            'menu_configuration': MenuConfiguration,
            'system_settings': SystemSettings
        }

        if table_name not in table_models:
            return jsonify({"error": "表格不存在"}), 404

        model = table_models[table_name]

        # 查找要删除的记录
        record = model.query.get(row_id)
        if not record:
            return jsonify({"error": "记录不存在"}), 404

        # 删除记录
        db.session.delete(record)
        db.session.commit()

        return jsonify({"message": "数据删除成功"})

    except Exception as e:
        db.session.rollback()
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0',port=3844,threaded=True,debug=False)