<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>对账中心 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
            boxShadow: {
              card: "0 2px 8px rgba(0, 0, 0, 0.08)",
              dropdown: "0 4px 12px rgba(0, 0, 0, 0.1)",
              nav: "0 1px 4px rgba(0, 0, 0, 0.05)"
            },
            animation: {
              'fade-in': 'fadeIn 0.2s ease-in-out',
              'slide-down': 'slideDown 0.3s ease-out',
            },
            keyframes: {
              fadeIn: {
                '0%': { opacity: '0' },
                '100%': { opacity: '1' },
              },
              slideDown: {
                '0%': { transform: 'translateY(-10px)', opacity: '0' },
                '100%': { transform: 'translateY(0)', opacity: '1' },
              },
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      .transition-all {
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
      }

      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }

      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }

      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }

      .hover-card {
        transition: all 0.2s ease;
      }

      .hover-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a
              href="/"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >工作台</a
            >
            <a
              href="/financial_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >资金管理</a
            >
            <a
              href="/reconciliation_center"
              class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all"
              >对账中心</a
            >
            <a
              href="/product_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >产品中心</a
            >
            <a
              href="/data_analysis"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >数据中心</a
            >
            <a
              href="/marketing_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >营销中心</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input
              type="text"
              placeholder="搜索功能/应用/服务"
              class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
            >
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button
            class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-notification-3-line text-xl"></i>
            <span
              class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"
            ></span>
          </button>
          <button
            class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div
              class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all"
            >
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">对账中心</h1>
            <p class="mt-1 text-sm text-gray-500">查询和管理交易对账单、结算对账单和发票</p>
          </div>

          <!-- 对账概览卡片 -->
          <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-lg font-medium text-gray-800">对账概览</h2>
              <div class="flex items-center">
                <span class="text-sm text-gray-500 mr-2">更新时间: 2025-05-09 10:30</span>
                <button class="p-1 text-gray-400 hover:text-primary">
                  <i class="ri-refresh-line"></i>
                </button>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-1">今日交易总额 (元)</div>
                <div class="text-2xl font-semibold text-gray-800">45,678.90</div>
                <div class="mt-2 text-xs text-blue-600">
                  <span class="inline-flex items-center">
                    <i class="ri-arrow-up-s-line mr-1"></i> 较昨日 +12.5%
                  </span>
                </div>
              </div>

              <div class="bg-green-50 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-1">今日交易笔数</div>
                <div class="text-2xl font-semibold text-gray-800">234</div>
                <div class="mt-2 text-xs text-green-600">
                  <span class="inline-flex items-center">
                    <i class="ri-arrow-up-s-line mr-1"></i> 较昨日 +8.3%
                  </span>
                </div>
              </div>

              <div class="bg-orange-50 rounded-lg p-4">
                <div class="text-sm text-gray-600 mb-1">待核对差异 (笔)</div>
                <div class="text-2xl font-semibold text-gray-800">3</div>
                <div class="mt-2 text-xs text-orange-600">
                  <span class="inline-flex items-center">
                    <i class="ri-alert-line mr-1"></i> 请尽快处理
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 对账单查询 -->
          <div class="bg-white rounded-lg shadow-sm mb-6">
            <div class="p-6 border-b border-gray-100">
              <h2 class="text-lg font-medium text-gray-800">对账单查询</h2>
            </div>

            <div class="p-6">
              <form class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">对账单类型</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                    <option value="all">全部类型</option>
                    <option value="transaction">交易对账单</option>
                    <option value="settlement">结算对账单</option>
                    <option value="refund">退款对账单</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">对账状态</label>
                  <select class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                    <option value="all">全部状态</option>
                    <option value="matched">已核对</option>
                    <option value="unmatched">待核对</option>
                    <option value="difference">有差异</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                  <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                  <input type="date" class="w-full px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary">
                </div>

                <div class="md:col-span-4 flex justify-end">
                  <button type="button" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-button text-sm hover:bg-gray-200 transition-all mr-2">
                    重置
                  </button>
                  <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button text-sm hover:bg-primary/90 transition-all">
                    查询
                  </button>
                </div>
              </form>

              <div class="overflow-x-auto">
                <table class="w-full">
                  <thead>
                    <tr class="bg-gray-50">
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对账单号</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对账类型</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对账日期</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易笔数</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易金额</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对账状态</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">DZ20250509001</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">交易对账单</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-09</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">125</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">23,456.78</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          已核对
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-primary">
                        <a href="#" class="mr-3">查看</a>
                        <a href="#">下载</a>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">DZ20250508001</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">交易对账单</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-08</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">118</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">21,345.67</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          已核对
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-primary">
                        <a href="#" class="mr-3">查看</a>
                        <a href="#">下载</a>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">DZ20250507001</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">结算对账单</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-07</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">98</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">18,765.43</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                          有差异
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-primary">
                        <a href="#" class="mr-3">查看</a>
                        <a href="#" class="mr-3">下载</a>
                        <a href="#" class="text-red-500">处理差异</a>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">DZ20250506001</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">退款对账单</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-06</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2,345.67</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          待核对
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-primary">
                        <a href="#" class="mr-3">查看</a>
                        <a href="#" class="mr-3">下载</a>
                        <a href="#" class="text-blue-500">核对</a>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">DZ20250505001</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">交易对账单</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-05</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">105</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">19,876.54</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          已核对
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-primary">
                        <a href="#" class="mr-3">查看</a>
                        <a href="#">下载</a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-500">
                  共 <span class="font-medium">25</span> 条记录，当前第 <span class="font-medium">1</span> 页，共 <span class="font-medium">5</span> 页
                </div>
                <div class="flex space-x-1">
                  <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all opacity-50 cursor-not-allowed">
                    上一页
                  </button>
                  <button class="px-3 py-1 text-sm text-white bg-primary rounded hover:bg-primary/90 transition-all">
                    1
                  </button>
                  <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                    2
                  </button>
                  <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                    3
                  </button>
                  <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                    下一页
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        console.log('对账中心页面已加载');
      });
    </script>
  </body>
</html>
