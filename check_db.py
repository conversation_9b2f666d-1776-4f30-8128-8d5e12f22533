#!/usr/bin/env python3
import sqlite3

def check_database():
    conn = sqlite3.connect('data.sqlite')
    cursor = conn.cursor()

    print('=== TemplateContent 表中的 header_title ===')
    cursor.execute('SELECT id, template_id, content_key, content_value FROM template_content WHERE content_key = "header_title"')
    results = cursor.fetchall()
    for row in results:
        print(f'ID: {row[0]}, Template ID: {row[1]}, Key: {row[2]}, Value: {row[3]}')

    print('\n=== SystemSettings 表中的 system_title ===')
    cursor.execute('SELECT id, setting_category, setting_key, setting_value FROM system_settings WHERE setting_category = "labels" AND setting_key = "system_title"')
    results = cursor.fetchall()
    for row in results:
        print(f'ID: {row[0]}, Category: {row[1]}, Key: {row[2]}, Value: {row[3]}')

    print('\n=== 当前激活的模板 ===')
    cursor.execute('SELECT id, template_name, is_active FROM template_config WHERE is_active = 1')
    results = cursor.fetchall()
    for row in results:
        print(f'ID: {row[0]}, Name: {row[1]}, Active: {row[2]}')

    conn.close()

if __name__ == "__main__":
    check_database()
