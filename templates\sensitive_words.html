<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台 - 敏感词管理</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .severity-low { color: #10b981; }
      .severity-medium { color: #f59e0b; }
      .severity-high { color: #ef4444; }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ contents.header_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ contents.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ contents.username|default('admin') }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}
      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>敏感词管理</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>敏感词列表</span>
          </div>
        </div>
        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                id="searchInput"
                placeholder="搜索敏感词"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <button
              id="addWordBtn"
              class="bg-blue-500 text-white px-4 py-2 rounded-button flex items-center ml-4 hover:bg-blue-600 transition-colors"
            >
              <i class="ri-add-line mr-2"></i>
              <span>添加敏感词</span>
            </button>
            <button
              id="batchDeleteBtn"
              class="bg-red-500 text-white px-4 py-2 rounded-button flex items-center ml-2 hover:bg-red-600 transition-colors"
            >
              <i class="ri-delete-bin-line mr-2"></i>
              <span>批量删除</span>
            </button>
            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer"
              >
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium w-10">
                    <input type="checkbox" id="selectAll" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                  </th>
                  <th class="font-medium">序号</th>
                  <th class="font-medium">敏感词</th>
                  <th class="font-medium">分类</th>
                  <th class="font-medium">严重程度</th>
                  <th class="font-medium">处理方式</th>
                  <th class="font-medium">状态</th>
                  <th class="font-medium">创建时间</th>
                  <th class="font-medium">操作</th>
                </tr>
              </thead>
              <tbody id="wordsTableBody">
                <!-- 表格数据行将通过JavaScript动态生成 -->
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600" id="totalRecords">共 0 条</div>
            <div class="flex items-center" id="pagination">
              <!-- 分页将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 添加/编辑敏感词模态框 -->
    <div id="wordModal" class="modal">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900" id="modalTitle">添加敏感词</h3>
          <button id="closeModal" class="text-gray-400 hover:text-gray-600">
            <i class="ri-close-line text-xl"></i>
          </button>
        </div>
        <form id="wordForm" class="p-6">
          <input type="hidden" id="wordId" name="id">
          <div class="mb-4">
            <label for="wordInput" class="block text-sm font-medium text-gray-700 mb-2">敏感词</label>
            <input
              type="text"
              id="wordInput"
              name="word"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="请输入敏感词"
            />
          </div>
          <div class="mb-4">
            <label for="categoryInput" class="block text-sm font-medium text-gray-700 mb-2">分类</label>
            <select
              id="categoryInput"
              name="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="默认">默认</option>
              <option value="政治敏感">政治敏感</option>
              <option value="色情内容">色情内容</option>
              <option value="暴力内容">暴力内容</option>
              <option value="广告垃圾">广告垃圾</option>
              <option value="其他">其他</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="severityInput" class="block text-sm font-medium text-gray-700 mb-2">严重程度</label>
            <select
              id="severityInput"
              name="severity"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="低">低</option>
              <option value="中等" selected>中等</option>
              <option value="高">高</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="actionInput" class="block text-sm font-medium text-gray-700 mb-2">处理方式</label>
            <select
              id="actionInput"
              name="action"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="过滤" selected>过滤</option>
              <option value="警告">警告</option>
              <option value="封禁">封禁</option>
            </select>
          </div>
          <div class="mb-4">
            <label for="descriptionInput" class="block text-sm font-medium text-gray-700 mb-2">描述</label>
            <textarea
              id="descriptionInput"
              name="description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="请输入描述（可选）"
            ></textarea>
          </div>
          <div class="flex items-center justify-end space-x-3">
            <button
              type="button"
              id="cancelBtn"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              取消
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script>
      // 设置当前页面标识
      window.currentPage = 'sensitive_words';

      // 全局变量
      let currentPage = 1;
      let totalPages = 1;
      let wordsData = [];
      let filteredData = [];

      // DOM元素
      const modal = document.getElementById('wordModal');
      const addWordBtn = document.getElementById('addWordBtn');
      const closeModal = document.getElementById('closeModal');
      const cancelBtn = document.getElementById('cancelBtn');
      const wordForm = document.getElementById('wordForm');
      const searchInput = document.getElementById('searchInput');
      const selectAllCheckbox = document.getElementById('selectAll');
      const batchDeleteBtn = document.getElementById('batchDeleteBtn');

      // 初始化页面
      document.addEventListener('DOMContentLoaded', function() {
        loadSensitiveWords();
        initEventListeners();
      });

      // 初始化事件监听器
      function initEventListeners() {
        // 模态框事件
        addWordBtn.addEventListener('click', () => openModal());
        closeModal.addEventListener('click', () => closeModalHandler());
        cancelBtn.addEventListener('click', () => closeModalHandler());

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
          if (e.target === modal) closeModalHandler();
        });

        // 表单提交
        wordForm.addEventListener('submit', handleFormSubmit);

        // 搜索功能
        searchInput.addEventListener('input', handleSearch);

        // 全选功能
        selectAllCheckbox.addEventListener('change', handleSelectAll);

        // 批量删除
        batchDeleteBtn.addEventListener('click', handleBatchDelete);

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && modal.classList.contains('show')) {
            closeModalHandler();
          }
        });
      }

      // 加载敏感词数据
      async function loadSensitiveWords() {
        try {
          // 首先尝试从示例数据API获取
          const response = await fetch('/api/sample_data/sensitive_words');
          if (response.ok) {
            wordsData = await response.json();
            filteredData = [...wordsData];
            renderTable();
            updatePagination();
            return;
          }
        } catch (error) {
          console.log('Sample data API not available, trying main API');
        }

        try {
          // 如果示例数据API不可用，尝试主API
          const response = await fetch('/api/sensitive_words');
          if (response.ok) {
            wordsData = await response.json();
            filteredData = [...wordsData];
            renderTable();
            updatePagination();
          } else {
            console.error('Failed to load sensitive words');
            // 如果API失败，使用模拟数据
            loadMockData();
          }
        } catch (error) {
          console.error('Error loading sensitive words:', error);
          // 如果API失败，使用模拟数据
          loadMockData();
        }
      }

      // 加载模拟数据
      function loadMockData() {
        wordsData = [
          { id: 1, word: '垃圾', category: '广告垃圾', severity: '中等', action: '过滤', is_active: true, created_at: '2023-07-25 11:04', description: '垃圾信息过滤' },
          { id: 2, word: '色情', category: '色情内容', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-25 10:02', description: '色情内容严格禁止' },
          { id: 3, word: '暴力', category: '暴力内容', severity: '高', action: '警告', is_active: true, created_at: '2023-07-25 08:13', description: '暴力内容警告' },
          { id: 4, word: '广告', category: '广告垃圾', severity: '低', action: '过滤', is_active: true, created_at: '2023-07-24 22:08', description: '广告信息过滤' },
          { id: 5, word: '诈骗', category: '其他', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-24 20:33', description: '诈骗信息严厉打击' },
          { id: 6, word: '赌博', category: '其他', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-24 19:55', description: '赌博相关内容' },
          { id: 7, word: '毒品', category: '其他', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-24 17:50', description: '毒品相关内容' },
          { id: 8, word: '政治', category: '政治敏感', severity: '高', action: '过滤', is_active: true, created_at: '2023-07-24 17:48', description: '政治敏感内容' },
          { id: 9, word: '反动', category: '政治敏感', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-24 16:31', description: '反动言论' },
          { id: 10, word: '恐怖', category: '暴力内容', severity: '高', action: '封禁', is_active: true, created_at: '2023-07-24 16:16', description: '恐怖主义相关' }
        ];
        filteredData = [...wordsData];
        renderTable();
        updatePagination();
      }

      // 渲染表格
      function renderTable() {
        const tbody = document.getElementById('wordsTableBody');
        const startIndex = (currentPage - 1) * 10;
        const endIndex = startIndex + 10;
        const pageData = filteredData.slice(startIndex, endIndex);

        tbody.innerHTML = pageData.map((word, index) => `
          <tr class="border-b border-gray-200">
            <td>
              <input type="checkbox" class="row-checkbox h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" data-id="${word.id}">
            </td>
            <td>${startIndex + index + 1}</td>
            <td class="font-medium">${word.word}</td>
            <td>${word.category}</td>
            <td>
              <span class="severity-${word.severity === '低' ? 'low' : word.severity === '中等' ? 'medium' : 'high'}">
                ${word.severity}
              </span>
            </td>
            <td>${word.action}</td>
            <td>
              <span class="px-2 py-1 text-xs rounded-full ${word.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                ${word.is_active ? '启用' : '禁用'}
              </span>
            </td>
            <td>${word.created_at}</td>
            <td>
              <button onclick="editWord(${word.id})" class="text-blue-500 hover:text-blue-700 mr-2">
                编辑
              </button>
              <button onclick="deleteWord(${word.id})" class="text-red-500 hover:text-red-700">
                删除
              </button>
            </td>
          </tr>
        `).join('');

        // 更新总记录数
        document.getElementById('totalRecords').textContent = `共 ${filteredData.length} 条`;

        // 重新绑定复选框事件
        bindCheckboxEvents();
      }

      // 绑定复选框事件
      function bindCheckboxEvents() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.addEventListener('change', updateSelectAllState);
        });
      }

      // 更新分页
      function updatePagination() {
        totalPages = Math.ceil(filteredData.length / 10);
        const pagination = document.getElementById('pagination');

        if (totalPages <= 1) {
          pagination.innerHTML = '';
          return;
        }

        let paginationHTML = `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage > 1 ? 'changePage(' + (currentPage - 1) + ')' : ''}">
            <i class="ri-arrow-left-s-line"></i>
          </div>
        `;

        for (let i = 1; i <= totalPages; i++) {
          if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHTML += `
              <div class="pagination-item mx-1 ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">
                ${i}
              </div>
            `;
          } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
        }

        paginationHTML += `
          <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer ${currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}"
               onclick="${currentPage < totalPages ? 'changePage(' + (currentPage + 1) + ')' : ''}">
            <i class="ri-arrow-right-s-line"></i>
          </div>
        `;

        pagination.innerHTML = paginationHTML;
      }

      // 切换页面
      function changePage(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
          currentPage = page;
          renderTable();
          updatePagination();
        }
      }

      // 搜索处理
      function handleSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        if (searchTerm === '') {
          filteredData = [...wordsData];
        } else {
          filteredData = wordsData.filter(word =>
            word.word.toLowerCase().includes(searchTerm) ||
            word.category.toLowerCase().includes(searchTerm) ||
            word.description.toLowerCase().includes(searchTerm)
          );
        }
        currentPage = 1;
        renderTable();
        updatePagination();
      }

      // 打开模态框
      function openModal(wordData = null) {
        const modalTitle = document.getElementById('modalTitle');
        const wordId = document.getElementById('wordId');
        const wordInput = document.getElementById('wordInput');
        const categoryInput = document.getElementById('categoryInput');
        const severityInput = document.getElementById('severityInput');
        const actionInput = document.getElementById('actionInput');
        const descriptionInput = document.getElementById('descriptionInput');

        if (wordData) {
          modalTitle.textContent = '编辑敏感词';
          wordId.value = wordData.id;
          wordInput.value = wordData.word;
          categoryInput.value = wordData.category;
          severityInput.value = wordData.severity;
          actionInput.value = wordData.action;
          descriptionInput.value = wordData.description || '';
        } else {
          modalTitle.textContent = '添加敏感词';
          wordForm.reset();
          wordId.value = '';
        }

        modal.classList.add('show');
        wordInput.focus();
      }

      // 关闭模态框
      function closeModalHandler() {
        modal.classList.remove('show');
        wordForm.reset();
      }

      // 表单提交处理
      async function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(wordForm);
        const wordData = {
          word: formData.get('word'),
          category: formData.get('category'),
          severity: formData.get('severity'),
          action: formData.get('action'),
          description: formData.get('description')
        };

        const wordId = formData.get('id');
        const isEdit = wordId !== '';

        try {
          const url = isEdit ? `/api/sensitive_words/${wordId}` : '/api/sensitive_words';
          const method = isEdit ? 'PUT' : 'POST';

          const response = await fetch(url, {
            method: method,
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(wordData)
          });

          if (response.ok) {
            closeModalHandler();
            loadSensitiveWords();
            showMessage(isEdit ? '敏感词更新成功' : '敏感词添加成功', 'success');
          } else {
            const error = await response.json();
            showMessage(error.message || '操作失败', 'error');
          }
        } catch (error) {
          console.error('Error saving word:', error);
          // 模拟成功操作
          if (isEdit) {
            const index = wordsData.findIndex(w => w.id == wordId);
            if (index !== -1) {
              wordsData[index] = { ...wordsData[index], ...wordData };
            }
          } else {
            const newWord = {
              id: Date.now(),
              ...wordData,
              is_active: true,
              created_at: new Date().toLocaleString('zh-CN')
            };
            wordsData.unshift(newWord);
          }
          filteredData = [...wordsData];
          renderTable();
          updatePagination();
          closeModalHandler();
          showMessage(isEdit ? '敏感词更新成功' : '敏感词添加成功', 'success');
        }
      }

      // 编辑敏感词
      function editWord(id) {
        const word = wordsData.find(w => w.id === id);
        if (word) {
          openModal(word);
        }
      }

      // 删除敏感词
      async function deleteWord(id) {
        if (!confirm('确定要删除这个敏感词吗？')) {
          return;
        }

        try {
          const response = await fetch(`/api/sensitive_words/${id}`, {
            method: 'DELETE'
          });

          if (response.ok) {
            loadSensitiveWords();
            showMessage('敏感词删除成功', 'success');
          } else {
            showMessage('删除失败', 'error');
          }
        } catch (error) {
          console.error('Error deleting word:', error);
          // 模拟删除成功
          wordsData = wordsData.filter(w => w.id !== id);
          filteredData = filteredData.filter(w => w.id !== id);
          renderTable();
          updatePagination();
          showMessage('敏感词删除成功', 'success');
        }
      }

      // 全选处理
      function handleSelectAll() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked;
        });
      }

      // 更新全选状态
      function updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

        selectAllCheckbox.checked = checkboxes.length === checkedBoxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
      }

      // 批量删除
      async function handleBatchDelete() {
        const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
        if (checkedBoxes.length === 0) {
          showMessage('请选择要删除的敏感词', 'warning');
          return;
        }

        if (!confirm(`确定要删除选中的 ${checkedBoxes.length} 个敏感词吗？`)) {
          return;
        }

        const ids = Array.from(checkedBoxes).map(cb => parseInt(cb.dataset.id));

        try {
          const response = await fetch('/api/sensitive_words/batch_delete', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ids })
          });

          if (response.ok) {
            loadSensitiveWords();
            showMessage('批量删除成功', 'success');
          } else {
            showMessage('批量删除失败', 'error');
          }
        } catch (error) {
          console.error('Error batch deleting words:', error);
          // 模拟批量删除成功
          wordsData = wordsData.filter(w => !ids.includes(w.id));
          filteredData = filteredData.filter(w => !ids.includes(w.id));
          renderTable();
          updatePagination();
          showMessage('批量删除成功', 'success');
        }
      }

      // 显示消息
      function showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
          type === 'success' ? 'bg-green-500' :
          type === 'error' ? 'bg-red-500' :
          type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
        }`;
        messageEl.textContent = message;

        document.body.appendChild(messageEl);

        // 3秒后自动移除
        setTimeout(() => {
          if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
          }
        }, 3000);
      }

      // 菜单折叠/展开功能
      const menuHeaders = document.querySelectorAll(".px-4.py-2.flex.items-center.text-gray-700.hover\\:bg-gray-100");
      menuHeaders.forEach((header) => {
        if (header.querySelector(".ri-arrow-down-s-line")) {
          header.addEventListener("click", function() {
            // 获取下一个兄弟元素，直到找到不是菜单头部的元素
            let nextSibling = this.nextElementSibling;
            while (nextSibling && !nextSibling.classList.contains("sidebar-menu-item") && !nextSibling.classList.contains("mt-2")) {
              nextSibling = nextSibling.nextElementSibling;
            }

            // 如果找到了菜单项，切换其显示状态
            if (nextSibling && nextSibling.classList.contains("sidebar-menu-item")) {
              const isHidden = nextSibling.style.display === "none";

              // 找到所有相邻的菜单项
              let sibling = nextSibling;
              while (sibling && sibling.classList.contains("sidebar-menu-item")) {
                sibling.style.display = isHidden ? "flex" : "none";
                sibling = sibling.nextElementSibling;
              }

              // 切换箭头方向
              const arrow = this.querySelector(".ri-arrow-down-s-line");
              if (arrow) {
                if (isHidden) {
                  arrow.classList.remove("ri-arrow-right-s-line");
                  arrow.classList.add("ri-arrow-down-s-line");
                } else {
                  arrow.classList.remove("ri-arrow-down-s-line");
                  arrow.classList.add("ri-arrow-right-s-line");
                }
              }
            }
          });
        }
      });
    </script>
  </body>
</html>