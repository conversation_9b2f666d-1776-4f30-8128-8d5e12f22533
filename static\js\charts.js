document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    document.querySelectorAll('.text-lg.font-medium.text-gray-800').forEach(el => {
      if (el.textContent === '******') {
        const randomValue = (Math.random() * 10000).toFixed(2);
        el.textContent = randomValue;
      }
    });
  }, 1000);

  // 收入趋势图表数据
  const revenueData = {
    week: {
      xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      series: [820, 932, 901, 934, 1290, 1330, 1320]
    },
    month: {
      xAxis: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
      series: [1200, 1800, 2200, 1800, 2500, 3000, 2800]
    },
    year: {
      xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      series: [3500, 3200, 4100, 4800, 5200, 6100, 5800, 6300, 7100, 6800, 7500, 8200]
    }
  };

  // 订单分布图表数据
  const orderData = {
    today: {
      xAxis: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
      series: [10, 52, 200, 334, 390, 330, 220]
    },
    yesterday: {
      xAxis: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
      series: [8, 45, 180, 310, 360, 290, 180]
    },
    week: {
      xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      series: [520, 632, 701, 834, 890, 930, 820]
    }
  };

  // 当前选中的时间范围
  let currentRevenueRange = 'week';
  let currentOrderRange = 'today';

  // 初始化收入趋势图表
  const revenueChart = echarts.init(document.getElementById('revenueChart'));

  function updateRevenueChart(range) {
    currentRevenueRange = range;
    const data = revenueData[range];

    const revenueOption = {
      animation: true,
      grid: {
        top: 20,
        right: 20,
        bottom: 30,
        left: 50,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.xAxis,
        axisLine: {
          lineStyle: { color: '#E5E7EB' }
        },
        axisLabel: { color: '#1F2937' }
      },
      yAxis: {
        type: 'value',
        axisLine: { show: false },
        axisLabel: { color: '#1F2937' },
        splitLine: { lineStyle: { color: '#F3F4F6' } }
      },
      series: [{
        data: data.series,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        showSymbol: false,
        emphasis: {
          scale: true,
          symbolSize: 10,
          showSymbol: true
        },
        lineStyle: { color: 'rgba(87, 181, 231, 1)' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(87, 181, 231, 0.1)' },
              { offset: 1, color: 'rgba(87, 181, 231, 0.01)' }
            ]
          }
        }
      }],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#E5E7EB',
        borderWidth: 1,
        textStyle: { color: '#1F2937' },
        formatter: '{b}: {c} 元'
      }
    };

    revenueChart.setOption(revenueOption, true);
  }

  // 初始化订单分布图表
  const orderChart = echarts.init(document.getElementById('orderChart'));

  function updateOrderChart(range) {
    currentOrderRange = range;
    const data = orderData[range];

    const orderOption = {
      animation: true,
      grid: {
        top: 20,
        right: 20,
        bottom: 30,
        left: 50,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.xAxis,
        axisLine: {
          lineStyle: { color: '#E5E7EB' }
        },
        axisLabel: { color: '#1F2937' }
      },
      yAxis: {
        type: 'value',
        axisLine: { show: false },
        axisLabel: { color: '#1F2937' },
        splitLine: { lineStyle: { color: '#F3F4F6' } }
      },
      series: [{
        data: data.series,
        type: 'bar',
        barWidth: '40%',
        itemStyle: {
          color: 'rgba(141, 211, 199, 1)',
          borderRadius: [6, 6, 0, 0]
        },
        emphasis: {
          itemStyle: { color: 'rgba(141, 211, 199, 0.8)' }
        }
      }],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#E5E7EB',
        borderWidth: 1,
        textStyle: { color: '#1F2937' },
        formatter: '{b}: {c} 笔'
      }
    };

    orderChart.setOption(orderOption, true);
  }

  // 初始化图表
  updateRevenueChart(currentRevenueRange);
  updateOrderChart(currentOrderRange);

  // 添加图表点击事件
  revenueChart.on('click', function(params) {
    // 显示详细信息
    const value = params.value;
    const date = params.name;

    // 创建一个弹出提示
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 animate-fade-in';
    toast.innerHTML = `
      <div class="flex items-center">
        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-3">
          <i class="ri-line-chart-line"></i>
        </div>
        <div>
          <h3 class="font-medium text-gray-800">收入详情 - ${date}</h3>
          <p class="text-sm text-gray-600">收入金额: ¥${value.toLocaleString()}</p>
          <p class="text-xs text-gray-500 mt-1">点击查看更多分析</p>
        </div>
      </div>
    `;

    document.body.appendChild(toast);

    // 3秒后移除提示
    setTimeout(() => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  });

  orderChart.on('click', function(params) {
    // 显示详细信息
    const value = params.value;
    const time = params.name;

    // 创建一个弹出提示
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 animate-fade-in';
    toast.innerHTML = `
      <div class="flex items-center">
        <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500 mr-3">
          <i class="ri-bar-chart-line"></i>
        </div>
        <div>
          <h3 class="font-medium text-gray-800">订单详情 - ${time}</h3>
          <p class="text-sm text-gray-600">订单数量: ${value} 笔</p>
          <p class="text-xs text-gray-500 mt-1">点击查看订单明细</p>
        </div>
      </div>
    `;

    document.body.appendChild(toast);

    // 3秒后移除提示
    setTimeout(() => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  });

  // 添加CSS动画
  const style = document.createElement('style');
  style.textContent = `
    .animate-fade-in {
      animation: fadeIn 0.3s ease-in-out;
    }
    .animate-fade-out {
      animation: fadeOut 0.3s ease-in-out;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes fadeOut {
      from { opacity: 1; transform: translateY(0); }
      to { opacity: 0; transform: translateY(-10px); }
    }
  `;
  document.head.appendChild(style);

  // 绑定收入趋势图表的时间范围切换按钮
  const revenueButtons = document.querySelectorAll('.revenue-chart-buttons button');
  if (revenueButtons.length > 0) {
    revenueButtons.forEach(button => {
      button.addEventListener('click', function() {
        const range = this.getAttribute('data-range');
        if (range) {
          // 更新按钮样式
          revenueButtons.forEach(btn => {
            btn.classList.remove('bg-primary', 'text-white');
            btn.classList.add('text-gray-600', 'bg-gray-100');
          });
          this.classList.remove('text-gray-600', 'bg-gray-100');
          this.classList.add('bg-primary', 'text-white');

          // 更新图表
          updateRevenueChart(range);
        }
      });
    });
  }

  // 绑定订单分布图表的时间范围切换按钮
  const orderButtons = document.querySelectorAll('.order-chart-buttons button');
  if (orderButtons.length > 0) {
    orderButtons.forEach(button => {
      button.addEventListener('click', function() {
        const range = this.getAttribute('data-range');
        if (range) {
          // 更新按钮样式
          orderButtons.forEach(btn => {
            btn.classList.remove('bg-primary', 'text-white');
            btn.classList.add('text-gray-600', 'bg-gray-100');
          });
          this.classList.remove('text-gray-600', 'bg-gray-100');
          this.classList.add('bg-primary', 'text-white');

          // 更新图表
          updateOrderChart(range);
        }
      });
    });
  }

  window.addEventListener('resize', function() {
    revenueChart.resize();
    orderChart.resize();
  });
});