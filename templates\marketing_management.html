<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>营销活动 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a href="/" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">工作台</a>
            <a href="/financial_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">资金管理</a>
            <a href="/reconciliation_center" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">对账中心</a>
            <a href="/product_management" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">产品中心</a>
            <a href="/data_analysis" class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all">数据中心</a>
            <a href="/marketing_management" class="flex items-center px-5 text-primary border-b-2 border-primary font-medium transition-all">营销中心</a>
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input type="text" placeholder="搜索功能/应用/服务" class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-notification-3-line text-xl"></i>
            <span class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full">
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all">
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      {% include 'layout_parts/sidebar.html' %}

      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">营销活动</h1>
            <p class="mt-1 text-sm text-gray-500">创建和管理各类营销活动，提升销售转化</p>
          </div>

          <!-- 活动统计卡片 -->
          <div class="grid grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">进行中</span>
                <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center text-green-500">
                  <i class="ri-play-circle-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">1,560</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">未开始</span>
                <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center text-blue-500">
                  <i class="ri-time-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">980</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">已结束</span>
                <div class="w-8 h-8 rounded-full bg-gray-50 flex items-center justify-center text-gray-500">
                  <i class="ri-stop-circle-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">1,460</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">活动转化率</span>
                <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center text-purple-500">
                  <i class="ri-percent-line"></i>
                </div>
              </div>
              <div class="text-2xl font-medium text-gray-800">28.5%</div>
            </div>
          </div>

          <!-- 活动操作按钮 -->
          <div class="mb-6 flex items-center justify-between">
            <div class="flex space-x-3">
              <button class="px-4 py-2 bg-primary text-white rounded-lg text-sm hover:bg-primary/90 transition-all flex items-center">
                <i class="ri-add-line mr-1"></i>
                创建活动
              </button>
              <button class="px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-file-copy-line mr-1"></i>
                活动模板
              </button>
            </div>
            <div class="flex items-center space-x-2">
              <div class="relative">
                <input type="text" placeholder="活动名称/ID" class="pl-9 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all" />
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400">
                  <i class="ri-search-line"></i>
                </div>
              </div>
              <button class="px-3 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-all flex items-center">
                <i class="ri-filter-3-line mr-1"></i>
                筛选
              </button>
            </div>
          </div>

          <!-- 活动类型标签 -->
          <div class="mb-6 bg-white rounded-lg shadow-sm p-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-700">活动类型:</span>
              <button class="px-3 py-1 bg-primary text-white rounded-full text-sm">全部</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">满减活动</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">折扣活动</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">秒杀活动</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">拼团活动</button>
              <button class="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-sm hover:bg-gray-50 transition-all">会员专享</button>
            </div>
          </div>

          <!-- 活动列表 -->
          <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
            <table class="w-full">
              <thead class="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动类型</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动时间</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与人数</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化订单</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <!-- 活动项 1 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-blue-100 flex items-center justify-center text-primary mr-3">
                        <i class="ri-price-tag-3-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">618年中大促</div>
                        <div class="text-xs text-gray-500">ID: ACT2025060001</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-blue-50 text-blue-600 rounded-full">满减活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-06-01 ~ 2025-06-18</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">12,860</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">3,420</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 2 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-red-100 flex items-center justify-center text-red-500 mr-3">
                        <i class="ri-flashlight-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">限时秒杀</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050002</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-red-50 text-red-600 rounded-full">秒杀活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-05-15 ~ 2025-05-20</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">8,650</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2,130</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 3 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-purple-100 flex items-center justify-center text-purple-500 mr-3">
                        <i class="ri-vip-crown-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">会员专享折扣</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050003</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-purple-50 text-purple-600 rounded-full">会员专享</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-05-10 ~ 2025-06-10</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">4,320</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">1,560</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 4 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-orange-100 flex items-center justify-center text-orange-500 mr-3">
                        <i class="ri-group-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">拼团优惠</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050004</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-orange-50 text-orange-600 rounded-full">拼团活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-06-15 ~ 2025-06-30</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">0</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">0</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-blue-50 text-blue-600 rounded-full">未开始</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 5 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-green-100 flex items-center justify-center text-green-500 mr-3">
                        <i class="ri-coupon-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">新人专享券</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050005</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">优惠券活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-05-01 ~ 2025-07-01</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">21,560</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">5,870</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 6 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-yellow-100 flex items-center justify-center text-yellow-500 mr-3">
                        <i class="ri-gift-2-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">买赠活动</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050006</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-yellow-50 text-yellow-600 rounded-full">买赠活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-05-20 ~ 2025-06-20</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">7,430</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2,150</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 7 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-pink-100 flex items-center justify-center text-pink-500 mr-3">
                        <i class="ri-heart-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">情人节特惠</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050007</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-pink-50 text-pink-600 rounded-full">节日活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-02-10 ~ 2025-02-14</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">18,920</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">5,760</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-gray-50 text-gray-600 rounded-full">已结束</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 8 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-indigo-100 flex items-center justify-center text-indigo-500 mr-3">
                        <i class="ri-calendar-event-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">周年庆典</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050008</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-indigo-50 text-indigo-600 rounded-full">庆典活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-08-01 ~ 2025-08-07</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">0</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">0</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-blue-50 text-blue-600 rounded-full">未开始</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 9 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-teal-100 flex items-center justify-center text-teal-500 mr-3">
                        <i class="ri-user-star-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">会员日特惠</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050009</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-teal-50 text-teal-600 rounded-full">会员活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-05-25 ~ 2025-05-25</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">12,450</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">3,890</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-gray-50 text-gray-600 rounded-full">已结束</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <!-- 活动项 10 -->
                <tr>
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded bg-cyan-100 flex items-center justify-center text-cyan-500 mr-3">
                        <i class="ri-shopping-cart-line"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-800">首单立减</div>
                        <div class="text-xs text-gray-500">ID: ACT2025050010</div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-cyan-50 text-cyan-600 rounded-full">首单活动</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">2025-01-01 ~ 2025-12-31</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">35,670</div>
                  </td>
                  <td class="py-4 px-4">
                    <div class="text-sm text-gray-800">12,450</div>
                  </td>
                  <td class="py-4 px-4">
                    <span class="px-2 py-1 text-xs font-medium bg-green-50 text-green-600 rounded-full">进行中</span>
                  </td>
                  <td class="py-4 px-4">
                    <div class="flex space-x-2">
                      <button class="px-2 py-1 text-xs text-primary border border-primary/30 rounded hover:bg-blue-50 transition-all">查看</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">编辑</button>
                      <button class="px-2 py-1 text-xs text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">
                        <i class="ri-more-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <!-- 分页 -->
            <div class="px-4 py-3 flex items-center justify-between border-t border-gray-200">
              <div class="text-sm text-gray-500">
                显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">4,000</span> 条
              </div>
              <div class="flex items-center space-x-2">
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">上一页</button>
                <button class="px-3 py-1 text-sm text-white bg-primary rounded hover:bg-primary/90 transition-all">1</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">2</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">3</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">4</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">5</button>
                <span class="px-3 py-1 text-sm text-gray-600">...</span>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">100</button>
                <span class="px-3 py-1 text-sm text-gray-600">...</span>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">400</button>
                <button class="px-3 py-1 text-sm text-gray-600 border border-gray-200 rounded hover:bg-gray-50 transition-all">下一页</button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        console.log('营销活动页面已加载');

        // 为筛选按钮添加点击事件
        document.querySelector('button:has(.ri-filter-3-line)').addEventListener('click', function() {
          alert('筛选功能将在未来版本中推出');
        });

        // 为创建活动按钮添加点击事件
        document.querySelector('button:has(.ri-add-line)').addEventListener('click', function() {
          alert('创建活动功能将在未来版本中推出');
        });

        // 为活动模板按钮添加点击事件
        document.querySelector('button:has(.ri-file-copy-line)').addEventListener('click', function() {
          alert('活动模板功能将在未来版本中推出');
        });

        // 为活动类型标签添加点击事件
        const activityTypeButtons = document.querySelectorAll('.mb-6.bg-white.rounded-lg.shadow-sm.p-4 button');
        activityTypeButtons.forEach(button => {
          button.addEventListener('click', function() {
            // 移除所有按钮的选中状态
            activityTypeButtons.forEach(btn => {
              btn.classList.remove('bg-primary', 'text-white');
              if (!btn.classList.contains('border-gray-200')) {
                btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-700');
              }
            });

            // 设置当前按钮为选中状态
            this.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-700');
            this.classList.add('bg-primary', 'text-white');

            // 显示加载中提示
            const loadingToast = document.createElement('div');
            loadingToast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50';
            loadingToast.textContent = '正在加载数据...';
            document.body.appendChild(loadingToast);

            // 2秒后移除提示
            setTimeout(() => {
              loadingToast.remove();
            }, 2000);
          });
        });

        // 为分页按钮添加点击事件
        const paginationButtons = document.querySelectorAll('.px-4.py-3.flex.items-center.justify-between.border-t.border-gray-200 button');
        paginationButtons.forEach(button => {
          button.addEventListener('click', function() {
            // 如果不是当前页，则添加点击事件
            if (!this.classList.contains('bg-primary')) {
              // 显示加载中提示
              const loadingToast = document.createElement('div');
              loadingToast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50';
              loadingToast.textContent = '正在加载数据...';
              document.body.appendChild(loadingToast);

              // 2秒后移除提示
              setTimeout(() => {
                loadingToast.remove();
              }, 2000);
            }
          });
        });

        // 为表格中的操作按钮添加点击事件
        const actionButtons = document.querySelectorAll('tbody .flex.space-x-2 button');
        actionButtons.forEach(button => {
          button.addEventListener('click', function() {
            if (this.textContent.trim() === '查看') {
              alert('查看活动详情功能将在未来版本中推出');
            } else if (this.textContent.trim() === '编辑') {
              alert('编辑活动功能将在未来版本中推出');
            } else {
              alert('更多操作功能将在未来版本中推出');
            }
          });
        });

        // 为搜索框添加输入事件
        const searchInput = document.querySelector('input[placeholder="活动名称/ID"]');
        searchInput.addEventListener('keyup', function(e) {
          if (e.key === 'Enter') {
            // 显示加载中提示
            const loadingToast = document.createElement('div');
            loadingToast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50';
            loadingToast.textContent = '正在搜索...';
            document.body.appendChild(loadingToast);

            // 2秒后移除提示
            setTimeout(() => {
              loadingToast.remove();

              if (this.value.trim() !== '') {
                const resultToast = document.createElement('div');
                resultToast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg z-50';
                resultToast.textContent = `找到 ${Math.floor(Math.random() * 100) + 10} 条相关活动`;
                document.body.appendChild(resultToast);

                setTimeout(() => {
                  resultToast.remove();
                }, 2000);
              }
            }, 2000);
          }
        });
      });
    </script>
  </body>
</html>
