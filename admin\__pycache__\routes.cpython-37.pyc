B

    dLh1  �               @   sB  d dl mZmZmZmZmZmZ d dl mZ d dlm	Z	m
Z
mZmZ d dl
mZmZ d dlmZ d dlZd dlmZ ejdd	d
gd�dd
� �Ze�d�edd� ��Ze�d�edd� ��Ze�d�edd� ��Zejdd	d
gd�edd� ��Zejdd	d
gd�edd� ��Ze�d�edd� ��Zejd d
gd�ed!d"� ��ZdS )#�    )�render_template�redirect�url_for�request�flash�jsonify)�current_app)�
login_user�logout_user�login_required�current_user)�check_password_hash�generate_password_hash)�datetimeN)�admin_bpz/login�GET�POST)�methodsc              C   s�   t jrttd��S tjdkr~tj�d�} tj�d�}tj	}|j
j| d��� }|rvt
|j|�rv|jrvt|� ttd��S td� td�S )Nzadmin.dashboardr   �username�password)r   u3   用户名或密码错误，或没有管理员权限zadmin/login.html)r   �is_authenticatedr   r   r   �method�form�getr   �User�query�	filter_by�firstr
   r   �is_adminr	   r   r   )r   r   r   �user� r    �MC:\Users\<USER>\.PyCharm2018.3\config\scratches\houtai\admin\routes.py�login
   s    
r"   z/logoutc               C   s   t �  ttd��S )Nzadmin.login)r
   r   r   r    r    r    r!   �logout!   s    r#   �/c              C   sJ   t jstd� ttd��S tj} | j�� }| jj	dd��� }t
d||d�S )Nu   没有管理员权限zadmin.logoutT)�	is_activezadmin/dashboard.html)�templates_count�active_templates)r   r   r   r   r   r   �Templater   �countr   r   )r(   r&   r'   r    r    r!   �	dashboard(   s    
r*   z
/templatesc              C   s6   t jstd� ttd��S tj} | j�� }t	d|d�S )Nu   没有管理员权限zadmin.logoutzadmin/templates.html)�	templates)
r   r   r   r   r   r   r(   r   �allr   )r(   r+   r    r    r!   r+   :   s    
r+   z/templates/addc              C   sF  t jstd� ttd��S tj} tj}tj	dk�r>tj
�d�}tj
�d�}tj
�d�dkr^dnd	}tj
�d
�dkrvdnd	}tj
�dd�tj
�d
d�tj
�d�dkr�dnd	tj
�d�dkr�dnd	tj
�d�dkr�dnd	d�}|r�| jj
dd��dd	i� | ||||t�|�t�� d�}|j�|� |j��  td� ttd��S td�S )Nu   没有管理员权限zadmin.logoutr   �name�path�
is_default�onTFr%   �title� �
primary_colorz#1677FF�show_charts�show_sidebar�
show_rightbar)r1   r3   r4   r5   r6   )r/   )r-   r.   r/   r%   �config�
created_atu   模板添加成功zadmin.templateszadmin/add_template.html)r   r   r   r   r   r   r(   �dbr   r   r   r   r   r   �update�json�dumpsr   �now�session�add�commitr   )r(   r9   r-   r.   r/   r%   r7   �templater    r    r!   �add_templateH   s:    
rB   z/templates/<int:id>/editc             C   sb  t jstd� ttd��S tj}tj}|j�	| �}t
jdk�rLt
j�
d�|_t
j�
d�|_t
j�
d�dkrndnd	}t
j�
d
�dkr�dnd	|_t
j�
dd�t
j�
d
d�t
j�
d�dkr�dnd	t
j�
d�dkr�dnd	t
j�
d�dkr�dnd	d�}|�r|j�s|jjdd��dd	i� d|_t�|�|_t�� |_|j��  td� ttd��S |�� }td||d�S )Nu   没有管理员权限zadmin.logoutr   r-   r.   r/   r0   TFr%   r1   r2   r3   z#1677FFr4   r5   r6   )r1   r3   r4   r5   r6   )r/   u   模板更新成功zadmin.templateszadmin/edit_template.html)rA   r7   )r   r   r   r   r   r   r(   r9   r   �
get_or_404r   r   r   r   r-   r.   r%   r/   r   r:   r;   r<   r7   r   r=   �
updated_atr>   r@   �
get_configr   )�idr(   r9   rA   Znew_is_defaultr7   r    r    r!   �
edit_templatex   s8    

rG   z/templates/<int:id>/previewc             C   sD   t jstd� ttd��S tj}|j�| �}|�	� }t
|j|dd�S )Nu   没有管理员权限zadmin.logoutT)r7   Zpreview)r   r   r   r   r   r   r(   r   rC   rE   r   r.   )rF   r(   rA   r7   r    r    r!   �preview_template�   s    rH   z/templates/<int:id>/deletec             C   sv   t jstd� ttd��S tj}tj}|j�	| �}|j
rLtd� ttd��S |j�|� |j�
�  td� ttd��S )Nu   没有管理员权限zadmin.logoutu   不能删除默认模板zadmin.templatesu   模板已删除)r   r   r   r   r   r   r(   r9   r   rC   r/   r>   �deleter@   )rF   r(   r9   rA   r    r    r!   �delete_template�   s    
rJ   )�flaskr   r   r   r   r   r   r   �flask_loginr	   r
   r   r   �werkzeug.securityr
   r   r   r;   �adminr   �router"   r#   r*   r+   rB   rG   rH   rJ   r    r    r    r!   �<module>   s*    
//