<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>finviz数据提取后台 - 封禁用户列表</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "var(--primary-color, {{ primary_color|default('#1e293b') }})",
              secondary: "#3b82f6"
            },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :root {
        --primary-color: {{ primary_color|default('#1e293b') }};
      }
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif;
      }
      .sidebar-menu-item.active {
        background-color: #e5e7eb;
      }
      .sidebar-menu-item:hover {
        background-color: #f3f4f6;
      }
      .submenu-item {
        display: none;
      }
      .submenu-item.show {
        display: flex;
      }
      table th, table td {
        white-space: nowrap;
        padding: 0.75rem 1rem;
        text-align: left;
      }
      table th {
        font-weight: 500;
        color: #4b5563;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      .pagination-item {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .pagination-item.active {
        background-color: var(--primary-color, #1e293b);
        color: white;
      }

      /* 布局样式 */
      {% if layout_style == 'compact' %}
      .p-6 {
        padding: 1rem;
      }
      {% elif layout_style == 'wide' %}
      .container {
        max-width: 100%;
      }
      {% endif %}

      /* 自定义CSS */
      {{ custom_css|default('') }}
    </style>
  </head>
  <body class="bg-gray-100 min-h-screen flex flex-col">
    {% if is_preview %}
    <!-- 预览模式提示 -->
    <div class="fixed top-0 left-0 right-0 bg-indigo-600 text-white text-center py-2 z-[100]">
      <div class="flex items-center justify-center">
        <i class="ri-eye-line mr-2"></i>
        <span>预览模式：{{ preview_template_name }}</span>
        <a href="{{ url_for('admin_enhanced_templates') }}" class="ml-4 px-3 py-1 bg-white text-indigo-600 rounded-md text-sm hover:bg-indigo-50 transition-all">
          返回管理
        </a>
      </div>
    </div>
    <div class="h-10"></div> <!-- 预览模式下的额外空间 -->
    {% endif %}

    <!-- 顶部导航栏 -->
    <header
      class="bg-primary text-white w-full h-14 flex items-center justify-between px-4 shadow-md z-10 {% if is_preview %}mt-10{% endif %}"
    >
      <div class="flex items-center">
        <div class="w-8 h-8 flex items-center justify-center mr-2">
          <i class="ri-apps-2-line ri-lg"></i>
        </div>
        <h1 class="text-lg font-medium">{{ contents.header_title|default('finviz数据提取后台') }}</h1>
      </div>
      <div class="flex items-center">
        <div class="px-3 py-1 mr-2 text-sm cursor-pointer">
          <span>{{ contents.language_text|default('语言') }}</span>
        </div>
        <div class="flex items-center px-3 py-1 mr-2 cursor-pointer">
          <span class="mr-1">{{ contents.username|default('admin') }}</span>
          <div
            class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
          >
            <i class="ri-user-line"></i>
          </div>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-home-line ri-lg"></i>
        </div>
        <div class="w-8 h-8 flex items-center justify-center cursor-pointer">
          <i class="ri-logout-box-line ri-lg"></i>
        </div>
      </div>
    </header>
    <div class="flex flex-1">
      <!-- 左侧菜单栏 -->
      {% include 'includes/dynamic_sidebar_menu.html' %}
      <!-- 主内容区域 -->
      <main class="flex-1 p-6">
        <!-- 面包屑导航 -->
        <div class="flex items-center text-sm text-gray-600 mb-4">
          <div class="flex items-center cursor-pointer">
            <span>用户管理</span>
          </div>
          <div class="mx-2">/</div>
          <div class="flex items-center cursor-pointer">
            <span>封禁用户列表</span>
          </div>
        </div>
        <!-- 内容卡片 -->
        <div class="bg-white rounded shadow-sm p-6">
          <!-- 操作栏 -->
          <div class="flex items-center mb-6">
            <div class="flex-1 max-w-md relative">
              <input
                type="text"
                placeholder="搜索封禁用户"
                class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-button focus:outline-none focus:border-primary text-sm"
              />
              <div
                class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 flex items-center justify-center text-gray-400"
              >
                <i class="ri-search-line"></i>
              </div>
            </div>
            <button
              class="bg-red-500 text-white px-4 py-2 rounded-button flex items-center ml-4"
            >
              <span>批量解封</span>
            </button>
            <div class="ml-auto">
              <div
                class="w-10 h-10 flex items-center justify-center cursor-pointer"
              >
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </div>
          </div>
          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="w-full border-collapse">
              <thead>
                <tr class="border-b border-gray-200">
                  <th class="font-medium w-10">
                    <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                  </th>
                  <th class="font-medium">序号</th>
                  <th class="font-medium">用户ID</th>
                  <th class="font-medium">用户名</th>
                  <th class="font-medium">手机号</th>
                  <th class="font-medium">封禁原因</th>
                  <th class="font-medium">封禁时间</th>
                  <th class="font-medium">封禁到期</th>
                  <th class="font-medium">操作</th>
                </tr>
              </thead>
              <tbody id="bannedUsersTableBody">
                <!-- 表格数据行将通过JavaScript动态加载 -->
                <tr>
                  <td colspan="9" class="text-center py-8">
                    <i class="ri-loader-4-line text-2xl animate-spin text-gray-400"></i>
                    <p class="text-gray-500 mt-2">加载中...</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <!-- 分页 -->
          <div class="flex items-center justify-between mt-6">
            <div class="text-sm text-gray-600" id="totalRecords">共 0 条</div>
            <div class="flex items-center" id="paginationContainer">
              <!-- 分页按钮将通过JavaScript动态生成 -->
            </div>
          </div>
        </div>
      </main>
    </div>
    <!-- 引入统一的侧边栏菜单组件 -->
    <script src="{{ url_for('static', filename='js/sidebar_menu.js') }}"></script>
    <script>
      // 设置当前页面标识
      window.currentPage = 'banned_users';

      // 全局变量
      let currentPage = 1;
      let currentSearch = '';
      const perPage = 10;

      document.addEventListener("DOMContentLoaded", function () {
        // 初始化页面
        loadBannedUsers(1, '');

        // 搜索功能
        const searchInput = document.querySelector('input[placeholder="搜索封禁用户"]');
        if (searchInput) {
          searchInput.addEventListener('input', function() {
            currentSearch = this.value;
            currentPage = 1;
            loadBannedUsers(currentPage, currentSearch);
          });
        }

        // 菜单将由sidebar_menu.js自动初始化

        // 全选/取消全选功能
        const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
        const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');

        if (selectAllCheckbox) {
          selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
              checkbox.checked = this.checked;
            });
          });
        }

        // 当单个复选框状态改变时，检查是否需要更新全选复选框
        rowCheckboxes.forEach(checkbox => {
          checkbox.addEventListener('change', function() {
            const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);

            if (selectAllCheckbox) {
              selectAllCheckbox.checked = allChecked;
              selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
          });
        });
      });

      // 加载封禁用户数据
      async function loadBannedUsers(page = 1, search = '') {
        try {
          const url = `/api/sample_data/banned_users?page=${page}&per_page=${perPage}&search=${encodeURIComponent(search)}`;
          const response = await fetch(url);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          displayBannedUsers(data.banned_users);
          updatePagination(data.pagination);
          updateTotalRecords(data.pagination.total);

          currentPage = page;
        } catch (error) {
          console.error('Error loading banned users:', error);
          showError('加载封禁用户数据失败');
        }
      }

      // 显示封禁用户数据
      function displayBannedUsers(bannedUsers) {
        const tbody = document.getElementById('bannedUsersTableBody');

        if (bannedUsers.length === 0) {
          tbody.innerHTML = `
            <tr>
              <td colspan="9" class="text-center py-8">
                <i class="ri-inbox-line text-2xl text-gray-400"></i>
                <p class="text-gray-500 mt-2">暂无封禁用户数据</p>
              </td>
            </tr>
          `;
          return;
        }

        tbody.innerHTML = bannedUsers.map((user, index) => `
          <tr class="border-b border-gray-200">
            <td>
              <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
            </td>
            <td>${(currentPage - 1) * perPage + index + 1}</td>
            <td>${user.user_id}</td>
            <td>${user.username}</td>
            <td>${user.phone}</td>
            <td>${user.ban_reason}</td>
            <td>${user.ban_time}</td>
            <td>${user.ban_expire}</td>
            <td>
              <button class="text-blue-500 whitespace-nowrap mr-2" onclick="unbanUser('${user.id}')">
                解除封禁
              </button>
              <button class="text-red-500 whitespace-nowrap" onclick="permanentBan('${user.id}')">
                永久封禁
              </button>
            </td>
          </tr>
        `).join('');

        // 重新绑定全选功能
        bindSelectAllFunctionality();
      }

      // 更新分页显示
      function updatePagination(pagination) {
        const container = document.getElementById('paginationContainer');

        if (pagination.pages <= 1) {
          container.innerHTML = '';
          return;
        }

        let paginationHTML = '';

        // 上一页按钮
        if (pagination.has_prev) {
          paginationHTML += `
            <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer" onclick="loadBannedUsers(${pagination.prev_num}, currentSearch)">
              <i class="ri-arrow-left-s-line"></i>
            </div>
          `;
        }

        // 页码按钮
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        if (startPage > 1) {
          paginationHTML += `<div class="pagination-item mx-1" onclick="loadBannedUsers(1, currentSearch)">1</div>`;
          if (startPage > 2) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
        }

        for (let i = startPage; i <= endPage; i++) {
          const activeClass = i === pagination.page ? 'active' : '';
          paginationHTML += `<div class="pagination-item mx-1 ${activeClass}" onclick="loadBannedUsers(${i}, currentSearch)">${i}</div>`;
        }

        if (endPage < pagination.pages) {
          if (endPage < pagination.pages - 1) {
            paginationHTML += `<div class="pagination-item mx-1">...</div>`;
          }
          paginationHTML += `<div class="pagination-item mx-1" onclick="loadBannedUsers(${pagination.pages}, currentSearch)">${pagination.pages}</div>`;
        }

        // 下一页按钮
        if (pagination.has_next) {
          paginationHTML += `
            <div class="pagination-item flex items-center justify-center w-8 h-8 cursor-pointer" onclick="loadBannedUsers(${pagination.next_num}, currentSearch)">
              <i class="ri-arrow-right-s-line"></i>
            </div>
          `;
        }

        container.innerHTML = paginationHTML;
      }

      // 更新总记录数显示
      function updateTotalRecords(total) {
        document.getElementById('totalRecords').textContent = `共 ${total} 条`;
      }

      // 显示错误信息
      function showError(message) {
        const tbody = document.getElementById('bannedUsersTableBody');
        tbody.innerHTML = `
          <tr>
            <td colspan="9" class="text-center py-8">
              <i class="ri-error-warning-line text-2xl text-red-400"></i>
              <p class="text-red-500 mt-2">${message}</p>
            </td>
          </tr>
        `;
      }

      // 重新绑定全选功能
      function bindSelectAllFunctionality() {
        const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
        const rowCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');

        if (selectAllCheckbox) {
          selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
              checkbox.checked = this.checked;
            });
          });
        }

        rowCheckboxes.forEach(checkbox => {
          checkbox.addEventListener('change', function() {
            const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);

            if (selectAllCheckbox) {
              selectAllCheckbox.checked = allChecked;
              selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
          });
        });
      }

      // 解除封禁
      function unbanUser(userId) {
        if (confirm('确定要解除该用户的封禁吗？')) {
          // 这里可以添加解除封禁的API调用
          alert('解除封禁功能待实现');
        }
      }

      // 永久封禁
      function permanentBan(userId) {
        if (confirm('确定要永久封禁该用户吗？此操作不可撤销！')) {
          // 这里可以添加永久封禁的API调用
          alert('永久封禁功能待实现');
        }
      }
    </script>
  </body>
</html>
