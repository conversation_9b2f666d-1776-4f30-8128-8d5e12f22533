import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from houtai_3822 import app, db, TemplateConfig, TemplateContent
from datetime import datetime, timezone

def add_yehuo_template_content():
    with app.app_context():
        # 查找JJ竞技世界模板
        template = TemplateConfig.query.filter(TemplateConfig.template_name.like('yehuo.html')).first()

        if not template:
            print("JJ竞技世界模板不存在，请先添加模板")
            return

        # 检查该模板是否已有内容配置
        if TemplateContent.query.filter_by(template_id=template.id).first():
            print("JJ竞技世界模板已有内容配置，跳过添加")
            return

        # 为JJ竞技世界模板添加默认内容配置
        default_contents = [
            # 顶部导航
            TemplateContent(template_id=template.id, content_type='title', content_key='header_title',
                           content_value='finviz数据提取后台', content_description='顶部导航栏标题'),
            TemplateContent(template_id=template.id, content_type='text', content_key='language_text',
                           content_value='语言', content_description='语言选择文本'),
            TemplateContent(template_id=template.id, content_type='text', content_key='username',
                           content_value='admin', content_description='用户名显示'),

            # 面包屑导航
            TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_parent',
                           content_value='用户管理', content_description='面包屑导航父级'),
            TemplateContent(template_id=template.id, content_type='text', content_key='breadcrumb_current',
                           content_value='用户列表', content_description='面包屑导航当前页'),

            # 搜索框
            TemplateContent(template_id=template.id, content_type='placeholder', content_key='search_placeholder',
                           content_value='搜索用户', content_description='搜索框占位文字'),

            # 按钮文本
            TemplateContent(template_id=template.id, content_type='button', content_key='add_button',
                           content_value='添加用户', content_description='添加按钮文字'),
            TemplateContent(template_id=template.id, content_type='button', content_key='more_button',
                           content_value='更多', content_description='更多按钮文字'),

            # 表格标题
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_1',
                           content_value='序号', content_description='表格标题1'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_2',
                           content_value='用户ID', content_description='表格标题2'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_3',
                           content_value='用户名', content_description='表格标题3'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_4',
                           content_value='手机号', content_description='表格标题4'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_5',
                           content_value='用户ID', content_description='表格标题5'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_6',
                           content_value='头像', content_description='表格标题6'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_7',
                           content_value='创建时间', content_description='表格标题7'),
            TemplateContent(template_id=template.id, content_type='table_header', content_key='table_header_8',
                           content_value='操作', content_description='表格标题8'),

            # 分页信息
            TemplateContent(template_id=template.id, content_type='text', content_key='total_records',
                           content_value='共 25371 条', content_description='总记录数'),
        ]

        db.session.add_all(default_contents)
        db.session.commit()
        print("JJ竞技世界模板内容配置已添加")

if __name__ == "__main__":
    add_yehuo_template_content()
