<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>统计 - 支付宝管理后台</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1677FF",
              secondary: "#f5f7fa",
              blue: {
                50: "#e6f4ff",
                100: "#bae0ff",
                200: "#91caff",
                300: "#69b1ff",
                400: "#4096ff",
                500: "#1677ff",
                600: "#0958d9",
                700: "#003eb3",
                800: "#002c8c",
                900: "#001d66"
              },
              gray: {
                50: "#f5f7fa",
                100: "#e8edf3",
                200: "#d9e0e9",
                300: "#c5cfd8",
                400: "#a3afbf",
                500: "#8696a7",
                600: "#637282",
                700: "#4c5561",
                800: "#343c46",
                900: "#1d232a"
              }
            },
            borderRadius: {
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      }
      .sidebar-menu-item:hover .sidebar-icon {
        color: #1677ff;
      }
      .sidebar-menu-item.active {
        background-color: #e6f4ff;
        color: #1677ff;
      }
      .sidebar-menu-item.active .sidebar-icon {
        color: #1677ff;
      }
      .chart-container {
        width: 100%;
        height: 300px;
      }
    </style>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-nav sticky top-0 z-50">
      <div class="flex items-center justify-between px-6 h-16 max-w-[1920px] mx-auto">
        <div class="flex items-center">
          <div class="flex items-center text-primary mr-10">
            <i class="ri-alipay-line text-2xl mr-2"></i>
            <span class="font-semibold text-lg">支付宝管理后台</span>
          </div>
          <nav class="flex h-16 space-x-1">
            <a
              href="/"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >工作台</a
            >
            <a
              href="/financial_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >资金管理</a
            >
            <a
              href="/reconciliation_center"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >对账中心</a
            >
            <a
              href="/product_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >产品中心</a
            >
            <a
              href="/data_analysis"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >数据中心</a
            >
            <a
              href="/marketing_management"
              class="flex items-center px-5 text-gray-600 hover:text-primary border-b-2 border-transparent hover:border-primary/30 transition-all"
              >营销中心</a
            >
          </nav>
        </div>
        <div class="flex items-center">
          <div class="relative mr-4">
            <input
              type="text"
              placeholder="搜索功能/应用/服务"
              class="pl-10 pr-3 py-2 rounded-lg border border-gray-200 text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 w-56 transition-all"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400"
            >
              <i class="ri-search-line"></i>
            </div>
          </div>
          <button
            class="mr-3 relative w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-notification-3-line text-xl"></i>
            <span
              class="absolute top-2 right-2 w-2 h-2 bg-red-500 rounded-full"
            ></span>
          </button>
          <button
            class="mr-3 w-10 h-10 flex items-center justify-center text-gray-500 hover:text-primary transition-all hover:bg-blue-50 rounded-full"
          >
            <i class="ri-question-line text-xl"></i>
          </button>
          <div class="flex items-center ml-2">
            <div
              class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-primary cursor-pointer hover:bg-blue-200 transition-all"
            >
              <i class="ri-user-line text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="flex min-h-[calc(100vh-64px)]">
      <!-- 左侧菜单 -->
      <aside class="w-56 bg-white border-r border-gray-100 flex-shrink-0 overflow-y-auto">
        <div class="py-3">
          <div class="px-4 mb-4">
            <div class="relative w-full">
              <input
                type="text"
                placeholder="搜索菜单"
                class="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 border border-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all"
              />
              <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                <i class="ri-search-line"></i>
              </div>
            </div>
          </div>
      
          <div class="px-4 py-2">
            <div class="flex items-center justify-between text-sm font-medium text-gray-700 mb-2">
              <span>应用中心</span>
              <button class="w-5 h-5 flex items-center justify-center text-gray-500 hover:text-primary transition-all">
                <i class="ri-arrow-down-s-line"></i>
              </button>
            </div>
            <ul class="space-y-0.5">
              <li>
                <a
                  href="/shop_overview"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-store-line text-lg"></i>
                  </span>
                  <span>店铺概况</span>
                </a>
              </li>
              <li>
                <a
                  href="/balance"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-wallet-3-line text-lg"></i>
                  </span>
                  <span>余额</span>
                </a>
              </li>
              <li>
                <a
                  href="/modules"
                  class="flex items-center px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-lg sidebar-menu-item transition-all"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 text-gray-500 sidebar-icon">
                    <i class="ri-apps-line text-lg"></i>
                  </span>
                  <span>模块</span>
                </a>
              </li>
              <li>
                <a
                  href="/statistics"
                  class="flex items-center px-3 py-2.5 text-sm text-primary bg-blue-50 rounded-lg sidebar-menu-item active"
                >
                  <span class="w-6 h-6 flex items-center justify-center mr-3 sidebar-icon">
                    <i class="ri-bar-chart-line text-lg"></i>
                  </span>
                  <span class="font-medium">统计</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      
      <!-- 主内容区 -->
      <main class="flex-1 p-6 bg-gray-50 overflow-auto">
        <div class="max-w-7xl mx-auto">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-2xl font-medium text-gray-800">数据统计</h1>
            <p class="mt-1 text-sm text-gray-500">查看您的店铺数据分析和统计报表</p>
          </div>
          
          <!-- 时间筛选 -->
          <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between">
              <div class="flex items-center space-x-2 mb-2 md:mb-0">
                <button class="px-3 py-1.5 bg-primary text-white rounded-md text-sm">今日</button>
                <button class="px-3 py-1.5 text-gray-600 hover:bg-gray-50 rounded-md text-sm">昨日</button>
                <button class="px-3 py-1.5 text-gray-600 hover:bg-gray-50 rounded-md text-sm">7天</button>
                <button class="px-3 py-1.5 text-gray-600 hover:bg-gray-50 rounded-md text-sm">30天</button>
              </div>
              <div class="flex items-center space-x-2">
                <div class="relative">
                  <input type="date" class="pl-3 pr-10 py-1.5 border border-gray-200 rounded-md text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20" />
                </div>
                <span class="text-gray-500">至</span>
                <div class="relative">
                  <input type="date" class="pl-3 pr-10 py-1.5 border border-gray-200 rounded-md text-sm focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20" />
                </div>
                <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">确定</button>
              </div>
            </div>
          </div>
          
          <!-- 数据概览 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">交易金额 (元)</span>
                <span class="text-xs text-green-600">+15.8%</span>
              </div>
              <div class="text-2xl font-semibold text-gray-800">12,456.78</div>
              <div class="mt-4 h-10">
                <div class="w-full h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div class="h-full bg-primary rounded-full" style="width: 75%"></div>
                </div>
                <div class="flex justify-between mt-1">
                  <span class="text-xs text-gray-500">目标: 20,000</span>
                  <span class="text-xs text-gray-500">完成率: 75%</span>
                </div>
              </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">订单数 (笔)</span>
                <span class="text-xs text-green-600">+8.3%</span>
              </div>
              <div class="text-2xl font-semibold text-gray-800">356</div>
              <div class="mt-4 h-10">
                <div class="w-full h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div class="h-full bg-blue-400 rounded-full" style="width: 60%"></div>
                </div>
                <div class="flex justify-between mt-1">
                  <span class="text-xs text-gray-500">目标: 500</span>
                  <span class="text-xs text-gray-500">完成率: 60%</span>
                </div>
              </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">访客数 (人)</span>
                <span class="text-xs text-green-600">+23.5%</span>
              </div>
              <div class="text-2xl font-semibold text-gray-800">1,245</div>
              <div class="mt-4 h-10">
                <div class="w-full h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div class="h-full bg-green-400 rounded-full" style="width: 85%"></div>
                </div>
                <div class="flex justify-between mt-1">
                  <span class="text-xs text-gray-500">目标: 1,500</span>
                  <span class="text-xs text-gray-500">完成率: 85%</span>
                </div>
              </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-gray-500">转化率 (%)</span>
                <span class="text-xs text-red-600">-2.1%</span>
              </div>
              <div class="text-2xl font-semibold text-gray-800">28.6%</div>
              <div class="mt-4 h-10">
                <div class="w-full h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div class="h-full bg-orange-400 rounded-full" style="width: 65%"></div>
                </div>
                <div class="flex justify-between mt-1">
                  <span class="text-xs text-gray-500">目标: 35%</span>
                  <span class="text-xs text-gray-500">完成率: 65%</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 图表区域 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 销售趋势图 -->
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-base font-medium text-gray-800">销售趋势</h2>
                <div class="flex items-center space-x-2 sales-chart-buttons">
                  <button class="px-2 py-1 text-xs bg-primary text-white rounded-full" data-range="week">本周</button>
                  <button class="px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-full hover:bg-gray-50 transition-all" data-range="month">本月</button>
                  <button class="px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-full hover:bg-gray-50 transition-all" data-range="year">全年</button>
                </div>
              </div>
              <div id="salesTrendChart" class="chart-container"></div>
            </div>
            
            <!-- 用户来源图 -->
            <div class="bg-white rounded-lg shadow-sm p-4">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-base font-medium text-gray-800">用户来源</h2>
                <div class="flex items-center space-x-2">
                  <button class="px-2 py-1 text-xs bg-primary text-white rounded-full">访客数</button>
                  <button class="px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-full hover:bg-gray-50 transition-all">转化率</button>
                </div>
              </div>
              <div id="userSourceChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // 销售趋势图表数据
        const salesData = {
          week: {
            xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            series: [820, 932, 901, 934, 1290, 1330, 1320]
          },
          month: {
            xAxis: ['1日', '5日', '10日', '15日', '20日', '25日', '30日'],
            series: [1200, 1800, 2200, 1800, 2500, 3000, 2800]
          },
          year: {
            xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            series: [3500, 3200, 4100, 4800, 5200, 6100, 5800, 6300, 7100, 6800, 7500, 8200]
          }
        };
        
        // 当前选中的时间范围
        let currentSalesRange = 'week';
        
        // 初始化销售趋势图表
        const salesTrendChart = echarts.init(document.getElementById('salesTrendChart'));
        
        function updateSalesTrendChart(range) {
          currentSalesRange = range;
          const data = salesData[range];
          
          const option = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: data.xAxis,
              axisLine: {
                lineStyle: {
                  color: '#E8EDF3'
                }
              },
              axisLabel: {
                color: '#8696A7'
              }
            },
            yAxis: {
              type: 'value',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                color: '#8696A7'
              },
              splitLine: {
                lineStyle: {
                  color: '#E8EDF3'
                }
              }
            },
            series: [
              {
                name: '销售额',
                type: 'bar',
                barWidth: '60%',
                data: data.series,
                itemStyle: {
                  color: '#1677FF'
                }
              }
            ]
          };
          
          salesTrendChart.setOption(option, true);
        }
        
        // 初始化用户来源图表
        const userSourceChart = echarts.init(document.getElementById('userSourceChart'));
        const userSourceOption = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
            data: ['直接访问', '搜索引擎', '社交媒体', '外部链接', '其他']
          },
          series: [
            {
              name: '访客来源',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '14',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 335, name: '直接访问' },
                { value: 310, name: '搜索引擎' },
                { value: 234, name: '社交媒体' },
                { value: 135, name: '外部链接' },
                { value: 48, name: '其他' }
              ]
            }
          ]
        };
        
        userSourceChart.setOption(userSourceOption);
        
        // 初始化图表
        updateSalesTrendChart(currentSalesRange);
        
        // 绑定销售趋势图表的时间范围切换按钮
        const salesButtons = document.querySelectorAll('.sales-chart-buttons button');
        if (salesButtons.length > 0) {
          salesButtons.forEach(button => {
            button.addEventListener('click', function() {
              const range = this.getAttribute('data-range');
              if (range) {
                // 更新按钮样式
                salesButtons.forEach(btn => {
                  btn.classList.remove('bg-primary', 'text-white');
                  btn.classList.add('bg-white', 'border', 'border-gray-200', 'text-gray-700');
                });
                this.classList.remove('bg-white', 'border', 'border-gray-200', 'text-gray-700');
                this.classList.add('bg-primary', 'text-white');
                
                // 更新图表
                updateSalesTrendChart(range);
              }
            });
          });
        }
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
          salesTrendChart.resize();
          userSourceChart.resize();
        });
      });
    </script>
  </body>
</html>
